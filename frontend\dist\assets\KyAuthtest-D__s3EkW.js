import{r as p,b5 as $,R as s,ab as w,U as m,ac as k,N as n,T as L,V as C}from"./index-gs31pxOi.js";const{Text:g,Paragraph:x}=C,P=()=>{const[l,u]=p.useState([]),[i,o]=p.useState(!1),t=(e,r="info")=>{const a=new Date().toLocaleTimeString();u(S=>[...S,{message:e,type:r,timestamp:a}]),console.log(`[${a}] ${e}`)},f=()=>{u([])},c=$.create({prefixUrl:"http://localhost:5000",credentials:"include",timeout:3e4,retry:{limit:2,methods:["get","post","put","delete"],statusCodes:[408,413,429,500,502,503,504]},headers:{Accept:"application/json","Content-Type":"application/json"},hooks:{beforeRequest:[e=>{t(`🔍 ${e.method} request to: ${e.url}`,"info")}],afterResponse:[(e,r,a)=>(t(`✅ Response ${a.status} from: ${e.url}`,"success"),a)]}}),T=async()=>{try{o(!0),t("🔐 Testing login with Ky...","info");const e=await c.post("api/login",{json:{email:"<EMAIL>",password:"admin123"}}).json();t("✅ Login successful!","success"),t(`Response: ${JSON.stringify(e,null,2)}`,"info")}catch(e){if(t(`❌ Login failed: ${e.message}`,"error"),e.name==="HTTPError"){t(`HTTP Status: ${e.response.status}`,"error");try{const r=await e.response.text();t(`Error Response: ${r}`,"error")}catch{t("Could not read error response","error")}}}finally{o(!1)}},d=async()=>{try{o(!0),t("👤 Testing /api/me with Ky...","info");const e=await c.get("api/me").json();t("✅ /api/me successful!","success"),t(`User: ${e.data.username} (${e.data.email})`,"info")}catch(e){t(`❌ /api/me failed: ${e.message}`,"error"),e.name==="HTTPError"&&t(`HTTP Status: ${e.response.status}`,"error")}finally{o(!1)}},y=async()=>{try{o(!0),t("🔑 Testing SSE token with Ky...","info");const e=await c.get("api/sse-token").json();t("✅ SSE token successful!","success"),t(`Token: ${e.data.sseToken.substring(0,20)}...`,"info")}catch(e){t(`❌ SSE token failed: ${e.message}`,"error"),e.name==="HTTPError"&&t(`HTTP Status: ${e.response.status}`,"error")}finally{o(!1)}},h=async()=>{try{o(!0),t("🚪 Testing logout with Ky...","info");const e=await c.post("api/logout").json();t("✅ Logout successful!","success"),t(`Response: ${JSON.stringify(e,null,2)}`,"info")}catch(e){t(`❌ Logout failed: ${e.message}`,"error"),e.name==="HTTPError"&&t(`HTTP Status: ${e.response.status}`,"error")}finally{o(!1)}},E=e=>{switch(e){case"success":return"#52c41a";case"error":return"#ff4d4f";case"warning":return"#faad14";default:return"#1890ff"}};return s.createElement(w,{title:"Ky Authentication Test",style:{margin:"20px"}},s.createElement(m,{direction:"vertical",size:"large",style:{width:"100%"}},s.createElement(k,{message:"Ky HTTP Client Test",description:"This component tests the new Ky HTTP client implementation for authentication. Check browser console for detailed logs.",type:"info",showIcon:!0}),s.createElement(m,{wrap:!0},s.createElement(n,{type:"primary",onClick:T,loading:i},"Test Login"),s.createElement(n,{onClick:d,loading:i},"Test /api/me"),s.createElement(n,{onClick:y,loading:i},"Test SSE Token"),s.createElement(n,{onClick:h,loading:i},"Test Logout"),s.createElement(n,{onClick:f},"Clear Logs")),s.createElement(L,null),s.createElement("div",{style:{maxHeight:"400px",overflow:"auto"}},s.createElement(g,{strong:!0},"Test Logs:"),l.length===0?s.createElement(x,{type:"secondary"},"No logs yet. Click a test button to start."):l.map((e,r)=>s.createElement("div",{key:r,style:{marginBottom:"4px"}},s.createElement(g,{code:!0,style:{color:E(e.type),fontSize:"12px"}},"[",e.timestamp,"] ",e.message))))))};export{P as default};
