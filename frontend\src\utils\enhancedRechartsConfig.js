import { SOMIPEM_COLORS } from '../styles/brand-colors';
import ChartConfigurationManager from '../config/ChartConfigurationManager';

/**
 * Enhanced Recharts Configuration Generator
 * DEPRECATED: Use useUnifiedChartConfig instead
 * This is maintained for backward compatibility
 *
 * Integrates all chart settings for immediate effects with Recharts
 */
export class EnhancedRechartsConfig {
  constructor(settings = {}) {
    this.settings = settings;
    this.charts = settings.charts || {};
    this.theme = settings.theme || {};

    // Use unified configuration manager internally
    this._unifiedManager = new ChartConfigurationManager(settings);

    console.warn('EnhancedRechartsConfig is deprecated. Please use useUnifiedChartConfig hook instead.');
  }

  /**
   * Update settings and refresh unified manager
   */
  updateSettings(newSettings) {
    this.settings = newSettings;
    this.charts = newSettings.charts || {};
    this.theme = newSettings.theme || {};
    this._unifiedManager.updateSettings(newSettings);
  }

  /**
   * Get chart height based on settings
   * DELEGATED to unified manager
   */
  getChartHeight() {
    return this._unifiedManager.getChartHeight();
  }

  /**
   * Get chart margins based on settings
   * DELEGATED to unified manager
   */
  getChartMargins() {
    return this._unifiedManager.getChartMargins();
  }

  /**
   * Get color scheme based on settings
   * DELEGATED to unified manager
   */
  getColorScheme() {
    return this._unifiedManager.getColorScheme();
  }

  /**
   * Get animation configuration
   */
  getAnimationConfig() {
    const animationsEnabled = this.theme.animationsEnabled && this.theme.chartAnimations;
    
    return {
      animationBegin: 0,
      animationDuration: animationsEnabled ? 750 : 0,
      animationEasing: 'ease-in-out'
    };
  }

  /**
   * Get grid configuration
   */
  getGridConfig() {
    const dataDisplay = this.charts.dataDisplay || {};
    const showGrid = dataDisplay.showGridLines !== false;
    
    return {
      stroke: this.theme.darkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
      strokeDasharray: showGrid ? '3 3' : 'none',
      opacity: showGrid ? 1 : 0
    };
  }

  /**
   * Get axis configuration
   * DELEGATED to unified manager
   */
  getAxisConfig() {
    return this._unifiedManager.getAxisConfig();
  }

  /**
   * Get tooltip configuration
   * DELEGATED to unified manager
   */
  getTooltipConfig() {
    return this._unifiedManager.getTooltipConfig();
  }

  /**
   * Get legend configuration
   * DELEGATED to unified manager
   */
  getLegendConfig() {
    return this._unifiedManager.getLegendConfig();
  }

  /**
   * Get data label configuration
   * DELEGATED to unified manager (via display config)
   */
  getDataLabelConfig() {
    const displayConfig = this._unifiedManager.getDisplayConfig();

    if (!displayConfig.showDataLabels) {
      return { dataKey: null };
    }

    return {
      fill: this._unifiedManager.getTextColor(),
      fontSize: 11,
      fontWeight: 'bold'
    };
  }

  /**
   * Get hover effects configuration
   */
  getHoverEffectsConfig() {
    const hoverEffects = this.charts.interactions?.hoverEffects !== false;

    if (!hoverEffects) {
      return {
        onMouseEnter: null,
        onMouseLeave: null,
        cursor: 'default'
      };
    }

    return {
      cursor: 'pointer',
      style: {
        filter: 'brightness(1.1)',
        transition: 'all 0.2s ease'
      }
    };
  }

  /**
   * Get zoom configuration for Recharts
   * Note: Recharts doesn't have built-in zoom, this provides configuration for custom implementation
   */
  getZoomConfig() {
    const enableZoom = this.charts.interactions?.enableZoom !== false;

    if (!enableZoom) {
      return {
        enabled: false,
        onWheel: null,
        onMouseDown: null,
        onMouseMove: null,
        onMouseUp: null
      };
    }

    return {
      enabled: true,
      mode: 'x', // 'x', 'y', or 'xy'
      wheelSensitivity: 0.1,
      minZoom: 0.1,
      maxZoom: 10,
      // Event handlers for custom zoom implementation
      onWheel: (event) => {
        if (event.ctrlKey || event.metaKey) {
          event.preventDefault();
          // Custom zoom logic would go here
          console.log('Zoom gesture detected:', event.deltaY);
        }
      },
      onDoubleClick: () => {
        // Reset zoom on double click
        console.log('Reset zoom on double click');
      }
    };
  }

  /**
   * Get click to expand configuration
   */
  getClickToExpandConfig() {
    const clickToExpand = this.charts.interactions?.clickToExpand !== false;

    return {
      enabled: clickToExpand,
      cursor: clickToExpand ? 'pointer' : 'default'
    };
  }

  /**
   * Get animation configuration
   */
  getAnimationConfig() {
    const chartAnimations = this.settings.theme?.chartAnimations !== false;
    const animationsEnabled = this.settings.theme?.animationsEnabled !== false;

    const enableAnimations = chartAnimations && animationsEnabled;

    if (!enableAnimations) {
      return {
        isAnimationActive: false,
        animationBegin: 0,
        animationDuration: 0
      };
    }

    return {
      isAnimationActive: true,
      animationBegin: 0,
      animationDuration: 800,
      animationEasing: 'ease-out'
    };
  }

  /**
   * Get comprehensive chart props for Recharts components
   */
  getChartProps(chartType = 'bar') {
    const margins = this.getChartMargins();
    const animation = this.getAnimationConfig();
    const hoverEffects = this.getHoverEffectsConfig();
    const clickToExpand = this.getClickToExpandConfig();

    return {
      width: '100%',
      height: this.getChartHeight(),
      margin: margins,
      ...animation,
      style: {
        cursor: clickToExpand.enabled ? 'pointer' : 'default',
        ...hoverEffects.style
      }
    };
  }

  /**
   * Get Bar chart specific configuration
   */
  getBarChartConfig() {
    const colors = this.getColorScheme();
    const hoverEffects = this.getHoverEffectsConfig();
    const animation = this.getAnimationConfig();

    return {
      ...this.getChartProps('bar'),
      barProps: {
        fill: colors[0],
        radius: [4, 4, 0, 0],
        ...hoverEffects,
        ...animation
      }
    };
  }

  /**
   * Get Bar element configuration with hover and animation
   */
  getBarElementConfig(color, index = 0) {
    const hoverEffects = this.getHoverEffectsConfig();
    const animation = this.getAnimationConfig();
    const colors = this.getColorScheme();

    return {
      fill: color || colors[index % colors.length],
      radius: [4, 4, 0, 0],
      ...animation,
      ...(hoverEffects.cursor === 'pointer' && {
        onMouseEnter: (data, index) => {
          // Add hover effect logic here if needed
        },
        onMouseLeave: (data, index) => {
          // Remove hover effect logic here if needed
        },
        style: {
          ...hoverEffects.style,
          cursor: hoverEffects.cursor
        }
      })
    };
  }

  /**
   * Get Line element configuration with hover and animation
   */
  getLineElementConfig(color, index = 0) {
    const hoverEffects = this.getHoverEffectsConfig();
    const animation = this.getAnimationConfig();
    const colors = this.getColorScheme();
    const showDataPoints = this.charts.dataDisplay?.showDataPoints !== false;

    return {
      stroke: color || colors[index % colors.length],
      strokeWidth: 2,
      dot: showDataPoints ? {
        fill: color || colors[index % colors.length],
        strokeWidth: 2,
        r: 4
      } : false,
      activeDot: hoverEffects.cursor === 'pointer' ? {
        r: 6,
        stroke: color || colors[index % colors.length],
        strokeWidth: 2,
        fill: '#fff'
      } : false,
      ...animation
    };
  }

  /**
   * Get Line chart specific configuration
   */
  getLineChartConfig() {
    const colors = this.getColorScheme();
    const showDataPoints = this.charts.dataDisplay?.showDataPoints !== false;
    
    return {
      ...this.getChartProps('line'),
      lineProps: {
        stroke: colors[0],
        strokeWidth: 2,
        dot: showDataPoints ? { fill: colors[0], r: 4 } : false,
        activeDot: { r: 6, fill: colors[0] }
      }
    };
  }

  /**
   * Get Pie chart specific configuration
   */
  getPieChartConfig() {
    const colors = this.getColorScheme();
    
    return {
      ...this.getChartProps('pie'),
      pieProps: {
        cx: '50%',
        cy: '50%',
        outerRadius: 80,
        dataKey: 'value',
        colors: colors
      }
    };
  }

  /**
   * Apply settings to chart data
   */
  applySettingsToData(data, chartType = 'bar') {
    if (!data) return data;
    
    const colors = this.getColorScheme();
    
    // For pie charts, add colors to each data point
    if (chartType === 'pie') {
      return data.map((item, index) => ({
        ...item,
        fill: colors[index % colors.length]
      }));
    }
    
    return data;
  }

  /**
   * Get performance mode configuration
   */
  getPerformanceModeConfig() {
    const performanceMode = this.charts.performanceMode || false;

    if (!performanceMode) {
      return {
        optimizeForSpeed: false,
        maxDataPoints: Infinity,
        enableSampling: false
      };
    }

    return {
      optimizeForSpeed: true,
      maxDataPoints: 200,
      enableSampling: true,
      animationDuration: 0
    };
  }

  /**
   * Get responsive container props
   */
  getResponsiveContainerProps() {
    const aspectRatio = this.charts.layout?.aspectRatio || 'auto';
    const performanceConfig = this.getPerformanceModeConfig();

    let aspect;
    switch (aspectRatio) {
      case '16:9':
        aspect = 16/9;
        break;
      case '4:3':
        aspect = 4/3;
        break;
      case '1:1':
        aspect = 1;
        break;
      default:
        aspect = undefined;
    }

    return {
      width: '100%',
      height: this.getChartHeight(),
      aspect: aspect,
      debounceMs: performanceConfig.optimizeForSpeed ? 100 : 0
    };
  }
}

/**
 * Hook to get enhanced Recharts configuration
 */

/**
 * Hook to create enhanced recharts configuration
 * DEPRECATED: Use useUnifiedChartConfig instead
 */
export const useEnhancedRechartsConfig = (settings) => {
  console.warn('useEnhancedRechartsConfig is deprecated. Please use useUnifiedChartConfig hook instead.');
  return new EnhancedRechartsConfig(settings);
};

export default EnhancedRechartsConfig;
