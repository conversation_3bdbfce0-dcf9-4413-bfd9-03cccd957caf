/* Enhanced Responsive Chart Modal Styles */

/* Base modal styles */
.unified-chart-modal-wrap {
  z-index: 9999;
}

.unified-chart-modal {
  z-index: 10000;
}

.unified-chart-modal .ant-modal-content {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.unified-chart-modal .ant-modal-header {
  border-bottom: none;
  padding: 0;
  background: transparent;
}

.unified-chart-modal .ant-modal-body {
  padding: 0;
  background: #fafafa;
}

.unified-chart-modal .ant-modal-close {
  display: none; /* We use custom close button */
}

/* Mobile-specific styles */
.unified-chart-modal.mobile .ant-modal-content {
  border-radius: 0;
  height: 100vh;
  width: 100vw;
  margin: 0;
  box-shadow: none;
}

.unified-chart-modal.mobile .ant-modal-body {
  height: calc(100vh - 48px);
  padding: 0;
}

/* Tablet-specific styles */
.unified-chart-modal.tablet .ant-modal-content {
  border-radius: 12px;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.15);
}

/* Fullscreen mode */
.unified-chart-modal.fullscreen .ant-modal-content {
  border-radius: 0;
  height: 100vh;
  width: 100vw;
  margin: 0;
  box-shadow: none;
}

.unified-chart-modal.fullscreen .ant-modal-body {
  height: calc(100vh - 60px);
}

/* Animation transitions */
.unified-chart-modal-enter {
  opacity: 0;
  transform: scale(0.95) translateY(20px);
}

.unified-chart-modal-enter-active {
  opacity: 1;
  transform: scale(1) translateY(0);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.unified-chart-modal-exit {
  opacity: 1;
  transform: scale(1) translateY(0);
}

.unified-chart-modal-exit-active {
  opacity: 0;
  transform: scale(0.95) translateY(20px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Mask animations */
.unified-chart-modal-mask-enter {
  opacity: 0;
}

.unified-chart-modal-mask-enter-active {
  opacity: 1;
  transition: opacity 0.3s ease;
}

.unified-chart-modal-mask-exit {
  opacity: 1;
}

.unified-chart-modal-mask-exit-active {
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Chart container enhancements */
.unified-chart-modal .chart-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.unified-chart-modal.mobile .chart-container {
  border-radius: 0;
}

.unified-chart-modal.fullscreen .chart-container {
  border-radius: 0;
}

/* Touch-friendly controls for mobile */
@media (max-width: 768px) {
  .unified-chart-modal .ant-btn {
    min-height: 44px;
    min-width: 44px;
    touch-action: manipulation;
  }
  
  .unified-chart-modal .ant-btn-sm {
    min-height: 36px;
    min-width: 36px;
  }
}

/* Responsive breakpoints */
@media (max-width: 480px) {
  .unified-chart-modal .ant-modal-content {
    margin: 0;
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
  }
  
  .unified-chart-modal .ant-modal-body {
    padding: 4px;
    height: calc(100vh - 44px);
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .unified-chart-modal .ant-modal-content {
    margin: 8px;
    height: calc(100vh - 16px);
    border-radius: 8px;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .unified-chart-modal .ant-modal-content {
    margin: 16px;
    height: calc(100vh - 32px);
    border-radius: 12px;
  }
}

@media (min-width: 1025px) {
  .unified-chart-modal .ant-modal-content {
    margin: 24px;
    height: calc(100vh - 48px);
    border-radius: 16px;
  }
}

/* Loading state improvements */
.unified-chart-modal .ant-spin-container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.unified-chart-modal .ant-spin-lg .ant-spin-dot {
  font-size: 32px;
}

/* Enhanced hover effects for desktop */
@media (hover: hover) {
  .unified-chart-modal .ant-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .unified-chart-modal .ant-modal-content {
    border: 2px solid #000;
  }
  
  .unified-chart-modal .ant-btn {
    border-width: 2px;
    font-weight: bold;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .unified-chart-modal-enter,
  .unified-chart-modal-enter-active,
  .unified-chart-modal-exit,
  .unified-chart-modal-exit-active,
  .unified-chart-modal-mask-enter,
  .unified-chart-modal-mask-enter-active,
  .unified-chart-modal-mask-exit,
  .unified-chart-modal-mask-exit-active {
    transition: none;
    animation: none;
  }
  
  .unified-chart-modal .chart-container,
  .unified-chart-modal .ant-btn {
    transition: none;
  }
}

/* Focus management for accessibility */
.unified-chart-modal .ant-btn:focus {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

.unified-chart-modal .ant-btn:focus:not(:focus-visible) {
  outline: none;
}

/* Print styles */
@media print {
  .unified-chart-modal {
    display: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .unified-chart-modal .ant-modal-body {
    background: #1f1f1f;
  }
  
  .unified-chart-modal .chart-container {
    background: #2d2d2d;
    color: #ffffff;
  }
}

/* Landscape orientation on mobile */
@media (max-width: 768px) and (orientation: landscape) {
  .unified-chart-modal .ant-modal-body {
    height: calc(100vh - 40px);
  }
}

/* Safe area support for devices with notches */
@supports (padding: max(0px)) {
  .unified-chart-modal.mobile .ant-modal-content {
    padding-top: max(0px, env(safe-area-inset-top));
    padding-bottom: max(0px, env(safe-area-inset-bottom));
    padding-left: max(0px, env(safe-area-inset-left));
    padding-right: max(0px, env(safe-area-inset-right));
  }
}
