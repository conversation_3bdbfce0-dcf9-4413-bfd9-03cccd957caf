import{r,R as a,V as C,ab as A,U as x,ak as E,T as F,a7 as T}from"./index-gs31pxOi.js";import{u as k}from"./useStopTableGraphQL-CaU6Bte3.js";function L(){const[u,s]=r.useState([]),[c,g]=r.useState([]),[i,n]=r.useState([]),[y,o]=r.useState(""),[S,e]=r.useState(""),[f,d]=r.useState(!1),[v,h]=r.useState(null),M=k();return r.useEffect(()=>{async function l(){d(!0);try{const t=await M.getMachineModels();s(Array.isArray(t)?t:[]);const m=await M.getMachineNames();g(Array.isArray(m)?m:[]),n(Array.isArray(m)?m:[]),h(null)}catch(t){console.error("Failed to load machine models:",t),h(t.message),s(["IPS","AKROS","ML","FCS"])}finally{d(!1)}}l()},[M]),{machineModels:u,allMachineNames:c,filteredMachineNames:i,selectedModel:y,selectedMachine:S,loading:f,error:v,handleModelChange:async l=>{if(o(l),e(""),!l){n(c);return}d(!0);try{const t=await M.getMachineNames({model:l});n(Array.isArray(t)?t:[]),h(null)}catch(t){console.error(`Failed to get machines for model ${l}:`,t),h(t.message),n([])}finally{d(!1)}},handleMachineChange:l=>{e(l)},resetSelections:()=>{o(""),e(""),n(c)}}}const{Title:N,Text:p}=C,{Option:w}=E,z=()=>{const{machineModels:u,filteredMachineNames:s,selectedModel:c,selectedMachine:g,loading:i,error:n,handleModelChange:y,handleMachineChange:o}=L(),S=[{title:"Machine Name",dataIndex:"Machine_Name",key:"name",render:e=>e||"Unknown"},{title:"Raw Data",key:"raw",render:(e,f)=>a.createElement("pre",{style:{maxHeight:"100px",overflow:"auto"}},JSON.stringify(f,null,2))}];return a.createElement("div",{style:{padding:24}},a.createElement(N,{level:2},"Machine Data Fixer Test"),n&&a.createElement(p,{type:"danger"},n),a.createElement(A,{loading:i},a.createElement(x,{direction:"vertical",size:"large",style:{width:"100%"}},a.createElement("div",null,a.createElement(p,{strong:!0},"Machine Model:"),a.createElement(E,{style:{width:200,marginLeft:16},value:c,onChange:y,placeholder:"Select a model",allowClear:!0,loading:i},u.map(e=>a.createElement(w,{key:e,value:e},e)))),a.createElement("div",null,a.createElement(p,{strong:!0},"Machine:"),a.createElement(E,{style:{width:300,marginLeft:16},value:g,onChange:o,placeholder:"Select a machine",allowClear:!0,loading:i,disabled:s.length===0},s.map(e=>a.createElement(w,{key:e.Machine_Name||String(e),value:e.Machine_Name||String(e)},e.Machine_Name||String(e))))))),a.createElement(F,null),a.createElement(N,{level:3},"Machines for ",c||"All Models"),a.createElement(T,{dataSource:s,columns:S,rowKey:e=>e.Machine_Name||JSON.stringify(e),pagination:{pageSize:5},loading:i}))};export{z as default};
