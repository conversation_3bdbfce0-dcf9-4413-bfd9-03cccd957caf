import{v as Ve,A as Je,r as w,a0 as y,R as e,a1 as R,D as ve,E as Ge,aa as $,ah as A,V as Ze,U as oe,N as v,ac as ke,T as q,ad as De,W as Re,a8 as Q,a9 as E,ab as I,a2 as M,ae as V,ag as _e,G as Ce,a6 as de,a7 as Te,af as Ke,a4 as Ye,$ as L,a5 as z}from"./index-gs31pxOi.js";import{L as Xe,B as se}from"./index-B6tjFLDG.js";import{u as et,w as _,E as tt,g as nt,r as at}from"./chart-config-BlCOOSyR.js";import{R as J}from"./DashboardOutlined-mvH9ifye.js";import{R as rt}from"./FilterOutlined-CWlXg5FA.js";import{R as ot}from"./FileTextOutlined-C7OWCm18.js";import{R as G}from"./CheckCircleOutlined-DSx8vC-g.js";import{R as Ie}from"./WarningOutlined-X6B97o_R.js";import{P as Me}from"./progress-B0lrj7SI.js";import{R as ie}from"./ClockCircleOutlined-CiulfqLg.js";import{R as ce}from"./CloseCircleOutlined-CwMt_cM7.js";import{R as st}from"./LineChartOutlined-Bf4j28ts.js";import{S as j}from"./index-C-rwPe8o.js";import{R as Ne}from"./HistoryOutlined-C_ww08XJ.js";import{R as Pe}from"./PlayCircleOutlined-DH-Pzoxe.js";import{R as it}from"./AreaChartOutlined-CNWfrZDb.js";import{R as ct}from"./RiseOutlined-D2H85vlr.js";const lt=`
  .machine-card-placeholder {
    position: relative;
    transition: all 0.3s;
  }
  .machine-card-placeholder:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .info-card {
    background: var(--info-card-bg, #f9f9f9);
    color: var(--info-card-text, inherit);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    transition: all 0.3s;
  }

  [data-theme="dark"] .info-card {
    --info-card-bg: #141414;
    --info-card-text: rgba(255, 255, 255, 0.85);
  }

  .info-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .info-card-title {
    font-weight: 500;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
  }

  .info-card-title .anticon {
    margin-right: 8px;
    color: #1890ff;
  }

  .info-card-content {
    display: flex;
    flex-wrap: wrap;
  }

  .info-item {
    flex: 1 0 50%;
    margin-bottom: 8px;
  }

  .info-label {
    color: var(--info-label-color, #8c8c8c);
    font-size: 12px;
  }

  .info-value {
    font-weight: 500;
    color: var(--info-value-color, inherit);
  }

  [data-theme="dark"] {
    --info-label-color: rgba(255, 255, 255, 0.45);
    --info-value-color: rgba(255, 255, 255, 0.85);
  }

  .chart-container {
    background: var(--chart-bg, white);
    color: var(--chart-text, #000);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid var(--chart-border, rgba(0, 0, 0, 0.06));
  }

  .chart-container:hover {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  }

  .chart-title {
    font-weight: 500;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    color: var(--chart-title, inherit);
    padding-bottom: 8px;
    border-bottom: 1px solid var(--chart-border, rgba(0, 0, 0, 0.06));
  }

  .chart-title .anticon {
    margin-right: 8px;
    color: var(--chart-icon, #1890ff);
  }

  .chart-container canvas {
    margin: 0 auto;
  }

  /* Tooltip custom styling */
  .chart-tooltip {
    background-color: var(--chart-tooltip-bg, rgba(255, 255, 255, 0.95)) !important;
    border-color: var(--chart-border, rgba(0, 0, 0, 0.1)) !important;
    color: var(--chart-text, rgba(0, 0, 0, 0.7)) !important;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  }

  /* Dark mode styles */
  [data-theme="dark"] .chart-container {
    --chart-bg: #141414;
    --chart-text: rgba(255, 255, 255, 0.85);
    --chart-title: rgba(255, 255, 255, 0.85);
    --chart-icon: #1890ff;
    --chart-border: rgba(255, 255, 255, 0.1);
    --chart-tooltip-bg: rgba(33, 33, 33, 0.95);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }

  [data-theme="dark"] .chart-container:hover {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
  }

  /* Ensure chart legends are properly styled in dark mode */
  [data-theme="dark"] .chart-container .recharts-legend-item-text,
  [data-theme="dark"] .chart-container .recharts-cartesian-axis-tick-value {
    fill: var(--chart-text, rgba(255, 255, 255, 0.85)) !important;
    color: var(--chart-text, rgba(255, 255, 255, 0.85)) !important;
  }

  /* WebSocket status indicators */
  .ws-status-tag {
    display: inline-flex;
    align-items: center;
    transition: all 0.3s;
  }

  .ws-status-tag .anticon {
    margin-right: 6px;
  }

  .ws-status-updating {
    animation: pulse 1.5s infinite;
  }

  @keyframes pulse {
    0% {
      opacity: 0.7;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.7;
    }
  }

  /* Smooth transitions for data updates */
  .ant-statistic-content-value-int,
  .ant-statistic-content-value-decimal,
  .ant-progress-inner,
  .ant-progress-bg,
  .ant-tag,
  .ant-badge-status-text,
  .ant-progress-text {
    transition: all 0.5s ease-in-out;
  }

  /* Highlight effect for updated values */
  .value-updated {
    animation: highlight-update 1.5s ease-out;
  }

  @keyframes highlight-update {
    0% {
      background-color: rgba(24, 144, 255, 0.2);
    }
    100% {
      background-color: transparent;
    }
  }

  /* Make machine cards transition smoothly */
  .machine-card-container {
    transition: all 0.3s ease-in-out;
  }

  /* Ensure no flicker during updates */
  .ant-card,
  .ant-table-wrapper,
  .ant-progress,
  .ant-statistic {
    will-change: contents;
    transform: translateZ(0);
    backface-visibility: hidden;
  }
`;if(typeof document<"u"){const d=document.createElement("style");d.textContent=lt,document.head.appendChild(d)}const{Title:le,Text:k}=Ze,{TabPane:W}=de;at();const Ht=()=>{const{darkMode:d}=Ve(),{isAuthenticated:ue,user:C}=Je(),Qe=w.useRef({}),[dt,B]=w.useState({}),[ut,me]=w.useState(null),[T,D]=w.useState({connected:!1,connecting:!1,updating:!1}),[He]=w.useState(new Date().toLocaleDateString("fr-FR",{weekday:"long",year:"numeric",month:"long",day:"numeric"})),[mt,gt]=w.useState(new Date),[pt,ft]=w.useState("all"),[ge,Le]=w.useState("machines"),[ht,je]=w.useState([]),[bt,Be]=w.useState(null),[s,S]=w.useState({machineData:[],previousMachineData:[],sideCardData:{},dailyStats:[],selectedMachine:null,machineHistory:[],historyLoading:!1,historyError:null,loading:!0,error:null,visible:!1,lastUpdate:new Date}),Z=(()=>{if(typeof window<"u"){const t=window.location.origin;return t.includes("ngrok-free.app")||t.includes("ngrok.io")?t:"http://localhost:5000"}return"http://localhost:5000"})();w.useEffect(()=>{et(d),typeof document<"u"&&document.documentElement.setAttribute("data-theme",d?"dark":"light"),Object.values(Qe.current).forEach(t=>{t&&t.current&&t.current.update()})},[d]);const u=t=>{const a=parseFloat(t);return isNaN(a)?0:a};w.useEffect(()=>{D(h=>({...h,connecting:!0})),_.connect();const t=h=>{console.log("Received initial data from WebSocket"),D(f=>({...f,connecting:!1,updating:!1}));const x=h.machineData.map(f=>{const b=u(f.TRS||"0"),O=b>80?"success":b>60?"warning":"error",ne=u(f.Quantite_Bon||"0"),ae=u(f.Quantite_Planifier||"0"),re=ne/(ae||1)*100;return{...f,status:O,progress:re}}),N={};h.activeSessions.forEach(f=>{N[f.machine_id]=f});const P={};h.activeSessions.forEach(f=>{P[f.machine_id]={active:!0,startTime:new Date(f.session_start),lastUpdate:new Date(f.last_updated),sessionId:f.id}}),B(P),S(f=>({...f,machineData:x,previousMachineData:[...x],sideCardData:h.sideCardData||{},dailyStats:h.dailyStats||[],error:null,loading:!1,lastUpdate:new Date})),y.success({message:"Données chargées",description:"Connexion en temps réel établie avec succès",icon:e.createElement(G,{style:{color:"#52c41a"}}),placement:"bottomRight",duration:3})},a=h=>{console.log("Received update from WebSocket",h),D(b=>({...b,updating:!0})),setTimeout(()=>{D(b=>({...b,updating:!1}))},500);const x=[...s.machineData],N=h.data.changedMachines||[],f=(h.data.fullData||[]).map(b=>{const O=u(b.TRS||"0"),ne=O>80?"success":O>60?"warning":"error",ae=u(b.Quantite_Bon||"0"),re=u(b.Quantite_Planifier||"0"),qe=ae/(re||1)*100;return{...b,status:ne,progress:qe}});S(b=>({...b,previousMachineData:b.machineData,machineData:f,lastUpdate:new Date})),console.log("Formatted machine data:",f),pe(f,x),N.length>2&&y.info({message:"Données mises à jour",description:`${N.length} machine(s) mise(s) à jour`,icon:e.createElement(R,{style:{color:"#1890ff"}}),placement:"bottomRight",duration:2})},i=h=>{console.log("Received session update from WebSocket",h);const{sessionData:x,updateType:N}=h,P=x.machine_id;if(N==="created"||N==="updated"){B(b=>({...b,[P]:{active:!0,startTime:new Date(x.session_start),lastUpdate:new Date(x.last_updated),sessionId:x.id}}));const f={created:{message:"Session démarrée",description:`Nouvelle session pour ${x.Machine_Name||"la machine "+P}`,icon:e.createElement(Pe,{style:{color:"#52c41a"}})},updated:{message:"Session mise à jour",description:`Session mise à jour pour ${x.Machine_Name||"la machine "+P}`,icon:e.createElement(R,{style:{color:"#1890ff"}})}};y.info({...f[N],placement:"bottomRight",duration:3})}else N==="stopped"&&(B(f=>{const b={...f};return delete b[P],b}),y.info({message:"Session terminée",description:`Session terminée pour ${x.Machine_Name||"la machine "+P}`,icon:e.createElement(ie,{style:{color:"#faad14"}}),placement:"bottomRight",duration:3}))},c=()=>{console.log("WebSocket connected"),D(h=>({...h,connected:!0,connecting:!1})),_.requestUpdate(),y.success({message:"Connexion établie",description:"Connexion en temps réel établie avec succès",icon:e.createElement(G,{style:{color:"#52c41a"}}),placement:"bottomRight",duration:3,key:"websocket-connecting"})},r=()=>{console.log("WebSocket disconnected"),D(h=>({...h,connected:!1,connecting:!0})),y.warning({message:"Connexion perdue",description:"Tentative de reconnexion en cours...",icon:e.createElement(R,{spin:!0,style:{color:"#faad14"}}),placement:"bottomRight",duration:4,key:"websocket-reconnecting"}),console.log("WebSocket disconnected - NOT falling back to HTTP polling (disabled for testing)")},n=h=>{console.error("WebSocket error:",h),D(x=>({...x,connected:!1,connecting:!1})),y.error({message:"Erreur de connexion",description:"Impossible de se connecter au service de données en temps réel. Utilisation du mode de secours.",icon:e.createElement(ce,{style:{color:"#ff4d4f"}}),placement:"bottomRight",duration:4,key:"websocket-error"}),S(x=>({...x,error:"Erreur de connexion WebSocket"})),console.log("WebSocket error - NOT falling back to HTTP polling (disabled for testing)")},l=_.addEventListener("initialData",t),o=_.addEventListener("update",a),g=_.addEventListener("sessionUpdate",i),p=_.addEventListener("connect",c),H=_.addEventListener("disconnect",r),F=_.addEventListener("error",n);D(h=>({...h,connecting:!0})),y.info({message:"Connexion en cours",description:"Établissement de la connexion en temps réel...",icon:e.createElement(R,{spin:!0,style:{color:"#1890ff"}}),placement:"bottomRight",duration:2,key:"websocket-connecting"});const te=setTimeout(()=>{_.isConnected||(console.log("WebSocket connection timeout - NOT falling back to HTTP polling (disabled for testing)"),y.info({message:"WebSocket Connection",description:"Still attempting to establish WebSocket connection. No fallback to HTTP polling.",icon:e.createElement(R,{spin:!0,style:{color:"#1890ff"}}),placement:"bottomRight",duration:5,key:"websocket-waiting"}))},5e3);return()=>{l(),o(),g(),p(),H(),F(),_.disconnect(),clearTimeout(te)}},[]);const Ae=async()=>{try{S(o=>({...o,loading:!0}));const[t,a,i,c]=await Promise.all([L.get(Z+"/api/MachineCard").withCredentials().timeout(3e4).retry(2),L.get(Z+"/api/sidecards").withCredentials().timeout(3e4).retry(2),L.get(Z+"/api/dailyStats").withCredentials().timeout(3e4).retry(2),Y()]),r=t.data.map(o=>{const g=u(o.TRS||"0"),p=g>80?"success":g>60?"warning":"error",H=u(o.Quantite_Bon||"0"),F=u(o.Quantite_Planifier||"0"),te=H/(F||1)*100;return{...o,status:p,progress:te}}),n={};c.forEach(o=>{n[o.machine_id]=o});const l={};return c.forEach(o=>{l[o.machine_id]={active:!0,startTime:new Date(o.session_start),lastUpdate:new Date(o.last_updated),sessionId:o.id}}),B(l),S(o=>({...o,machineData:r,previousMachineData:[...r],sideCardData:a.data[0]||{},dailyStats:i.data||[],activeSessions:n,error:null,loading:!1,lastUpdate:new Date})),pe(r,[]),Promise.resolve()}catch(t){return console.error("Error fetching data:",t),S(a=>({...a,error:t.message||"Failed to fetch data",loading:!1,lastUpdate:new Date})),y.error({message:"Erreur de chargement",description:`Impossible de charger les données: ${t.message}`,icon:e.createElement(ce,{style:{color:"#ff4d4f"}}),placement:"bottomRight",duration:4}),Promise.reject(t)}},pe=async(t,a)=>{try{const i=await Y(),c={};i.forEach(n=>{c[n.machine_id]=n});const r={};a.forEach(n=>{n.id&&(r[n.id]=n)});for(const n of t){if(!n.id)continue;const l={...n,Regleur_Prenom:n.Regleur_Prenom||"0",Quantite_Planifier:n.Quantite_Planifier||"0",Quantite_Bon:n.Quantite_Bon||"0",Quantite_Rejet:n.Quantite_Rejet||"0",TRS:n.TRS||"0",Poid_unitaire:n.Poid_unitaire||"0",cycle_theorique:n.cycle_theorique||"0",empreint:n.empreint||"0",Etat:n.Etat||"off",Code_arret:n.Code_arret||""},o=r[n.id],g=!!c[n.id];n.Etat==="on"&&!g?(await axios.post("/api/createSession",{machineId:n.id,machineData:l}),B(p=>({...p,[n.id]:{active:!0,startTime:new Date,lastUpdate:new Date}})),y.success({message:"Nouvelle session démarrée",description:`Session started for ${n.Machine_Name}`,icon:e.createElement($,{style:{color:"#52c41a"}}),placement:"bottomRight",duration:3})):n.Etat==="on"&&g?(await axios.post("/api/updateSession",{machineId:n.id,machineData:l}),B(p=>({...p,[n.id]:{...p[n.id],lastUpdate:new Date}}))):n.Etat==="off"&&g&&(await axios.post("/api/stopSession",{machineId:n.id}),B(p=>({...p,[n.id]:{active:!1,endTime:new Date}})),y.info({message:"Session terminée",description:`Session ended for ${n.Machine_Name}`,icon:e.createElement($,{style:{color:"#1890ff"}}),placement:"bottomRight",duration:3}))}}catch(i){console.error("Erreur lors de la gestion des sessions:",i),y.error({message:"Erreur de session",description:`Session management error: ${i.message}`,icon:e.createElement(ve,{style:{color:"#ff4d4f"}}),placement:"bottomRight",duration:4})}},fe=s.machineData.reduce((t,a)=>t+u(a.Quantite_Bon||0),0),K=s.machineData.reduce((t,a)=>t+u(a.Quantite_Rejet||0),0);fe+K>0&&(K/(fe+K)*100).toFixed(1);const $e=()=>{_.isConnected?(D(t=>({...t,updating:!0})),_.requestUpdate(),setTimeout(()=>{D(t=>({...t,updating:!1}))},1e3)):(S(t=>({...t,loading:!0})),console.log("WebSocket not connected - NOT falling back to HTTP polling (disabled for testing)"),y.info({message:"WebSocket Connection",description:"Attempting to establish WebSocket connection. No fallback to HTTP polling.",icon:e.createElement(R,{spin:!0,style:{color:"#1890ff"}}),placement:"bottomRight",duration:3}))},he=async t=>{try{S(o=>({...o,historyLoading:!0,historyError:null}));const a=s.machineData.find(o=>o.Machine_Name===t);if(!a||!a.id)throw new Error("Machine non trouvée ou ID manquant");const i=localStorage.getItem("token"),c=(()=>{if(typeof window<"u"){const o=window.location.origin;return o.includes("ngrok-free.app")||o.includes("ngrok.io")?o:"http://localhost:5000"}return"http://localhost:5000"})(),r=await L.get(c+`/api/machineSessions/${a.id}`).set("x-auth-token",i).withCredentials();if(!r.body)throw new Error("Données de session invalides");const l=(Array.isArray(r.body)?r.body:[]).map(o=>({...o,isActive:!o.session_end,highlight:!o.session_end}));S(o=>({...o,machineHistory:l,historyLoading:!1}))}catch(a){console.error("Erreur lors de la récupération de l'historique:",a),S(i=>({...i,historyError:a.message||"Impossible de récupérer l'historique de la machine",historyLoading:!1,machineHistory:[]}))}},Y=async()=>{try{const t=(()=>{if(typeof window<"u"){const i=window.location.origin;return i.includes("ngrok-free.app")||i.includes("ngrok.io")?i:"http://localhost:5000"}return"http://localhost:5000"})();return(await L.get(t+"/api/activeSessions").withCredentials().timeout(3e4).retry(2)).body}catch(t){return console.error("Erreur lors de la récupération des sessions actives:",t),[]}},ze=async()=>{try{const t=localStorage.getItem("token");return(await L.get("http://localhost:5000"+"/api/allSessions").set("x-auth-token",t).withCredentials()).body}catch(t){return console.error("Erreur lors de la récupération des sessions terminées:",t),[]}},be=async()=>{try{const t=await Y(),a={};if(t.forEach(c=>{a[c.machine_id]=c}),s.machineData.every(c=>c.Etat==="off")){const c=await ze();if(c.length>0){const r=JSON.parse(JSON.stringify(s.machineData)),n={};c.forEach(o=>{n[o.machine_id]||(n[o.machine_id]=[]),n[o.machine_id].push(o)});const l=r.map(o=>{const g=n[o.id]||[];if(g.length>0){g.sort((H,F)=>new Date(F.session_end)-new Date(H.session_end));const p=g[0];return{...o,TRS:o.TRS||p.TRS,Quantite_Bon:p.Quantite_Bon||o.Quantite_Bon,Quantite_Rejet:p.Quantite_Rejet||o.Quantite_Rejet,progress:(u(p.Quantite_Bon)||0)/(u(o.Quantite_Planifier)||1)*100,status:(u(p.TRS)||0)>80?"success":(u(p.TRS)||0)>60?"warning":"error",sessionData:p,isHistoricalData:!0}}return o});S(o=>({...o,machineData:l,lastUpdate:new Date,isHistoricalView:!0}));return}}if(t.length>0){const r=JSON.parse(JSON.stringify(s.machineData)).map(l=>{const o=a[l.id];return o&&l.Etat==="on"?{...l,TRS:o.TRS||l.TRS,Quantite_Bon:o.Quantite_Bon||l.Quantite_Bon,Quantite_Rejet:o.Quantite_Rejet||l.Quantite_Rejet,progress:(u(o.Quantite_Bon)||0)/(u(l.Quantite_Planifier)||1)*100,status:(u(o.TRS)||0)>80?"success":(u(o.TRS)||0)>60?"warning":"error",sessionData:o,isHistoricalData:!1}:l});JSON.stringify(r)!==JSON.stringify(s.machineData)&&S(l=>({...l,machineData:r,lastUpdate:new Date,isHistoricalView:!1}))}}catch(t){console.error("Erreur lors de la préparation des données des graphiques:",t)}},ye=async()=>{try{const t=localStorage.getItem("token"),i=await L.get("http://localhost:5000"+"/api/operator-stats").set("x-auth-token",t).withCredentials();je(i.body)}catch(t){console.error("Error fetching operator stats:",t),A.error("Failed to load operator statistics")}},Ee=async()=>{try{const t=localStorage.getItem("token"),a=(()=>{if(typeof window<"u"){const c=window.location.origin;return c.includes("ngrok-free.app")||c.includes("ngrok.io")?c:"http://localhost:5000"}return"http://localhost:5000"})(),i=await L.get(a+"/api/production-stats").set("x-auth-token",t).withCredentials();Be(i.body)}catch(t){console.error("Error fetching production stats:",t),A.error("Failed to load production statistics")}},Ue=t=>{Le(t.target.value)};w.useEffect(()=>{let t=!0;const a=async()=>{if(t)try{await Ae(),t&&setTimeout(()=>{t&&be()},500)}catch(c){console.error("Erreur lors de la mise à jour des données:",c)}};a(),ye(),Ee();const i=setInterval(a,15e3);return()=>{t=!1,clearInterval(i)}},[]);const X=t=>{if(!t.id){A.info("Cette machine n'est pas encore configurée");return}console.log("the trs is"+t.trs),S(a=>({...a,selectedMachine:t.Machine_Name,visible:!0})),he(t.Machine_Name)},xe=async()=>{try{T.connected?(D(t=>({...t,updating:!0})),_.requestUpdate(),await be(),await ye(),await Ee(),setTimeout(()=>{D(t=>({...t,updating:!1}))},1e3)):(A.loading({content:"Actualisation des données en cours...",key:"refreshMessage",duration:0}),console.log("WebSocket not connected - NOT falling back to HTTP polling (disabled for testing)"),y.info({message:"WebSocket Connection",description:"Attempting to establish WebSocket connection. No fallback to HTTP polling.",icon:e.createElement(R,{spin:!0,style:{color:"#1890ff"}}),placement:"bottomRight",duration:3,key:"websocket-waiting"}),A.success({content:"Données actualisées avec succès",key:"refreshMessage",duration:2}))}catch(t){D(a=>({...a,updating:!1})),A.error({content:`Erreur lors de l'actualisation: ${t.message}`,key:"refreshMessage",duration:3})}},Se=(t,a)=>a&&a.Etat==="on"?"#52c41a":"#d9d9d9",U=()=>{const a=new Date().getHours();return a>=6&&a<14?"Matin":a>=14&&a<22?"Après-midi":"Nuit"},ee=(t,a)=>{const c=new Date(t.session_start).getHours();return a==="Matin"&&c>=6&&c<14||a==="Après-midi"&&c>=14&&c<22?!0:a==="Nuit"&&(c>=22||c<6)},m=nt(d),we=t=>!t.session_end,Fe=[{title:"Statut",key:"status",render:(t,a)=>e.createElement(M,{color:we(a)?"processing":"default"},we(a)?"Active":"Terminée"),width:100},{title:"Début de session",dataIndex:"session_start",key:"session_start",render:t=>new Date(t).toLocaleString(),sorter:(t,a)=>new Date(a.session_start)-new Date(t.session_start)},{title:"Fin de session",dataIndex:"session_end",key:"session_end",render:t=>t?new Date(t).toLocaleString():"En cours"},{title:"Durée",key:"duration",render:(t,a)=>{const i=new Date(a.session_start),r=(a.session_end?new Date(a.session_end):new Date)-i,n=Math.floor(r/6e4);return`${Math.floor(n/60)}h ${n%60}m`}},{title:"Quantité bonne",dataIndex:"Quantite_Bon",key:"Quantite_Bon"},{title:"Quantité rejetée",dataIndex:"Quantite_Rejet",key:"Quantite_Rejet"},{title:"TRS",dataIndex:"TRS",key:"TRS",render:t=>e.createElement(M,{color:t>80?"success":t>60?"warning":"error"},t,"%")},{title:"Cycle",dataIndex:"cycle",key:"cycle"}],We=[{title:"Machine",dataIndex:"Machine_Name",key:"Machine_Name",render:t=>e.createElement("strong",null,t)},{title:"TRS",dataIndex:"TRS",key:"TRS",render:t=>e.createElement(M,{color:u(t)>80?"success":u(t)>60?"warning":"error"},u(t).toFixed(1),"%")},{title:"Planifié",dataIndex:"Quantite_Planifier",key:"Quantite_Planifier",render:t=>u(t)},{title:"Produit",dataIndex:"Quantite_Bon",key:"Quantite_Bon",render:t=>u(t)},{title:"Rejeté",dataIndex:"Quantite_Rejet",key:"Quantite_Rejet",render:t=>u(t)},{title:"Progression",dataIndex:"progress",key:"progress",render:t=>e.createElement(Me,{percent:Number.parseFloat(t.toFixed(1)),size:"small",status:t>90?"success":t>70?"normal":"exception"})},{title:"Session",key:"session",render:(t,a)=>e.createElement(M,{color:a.Etat==="on"?"processing":"default"},a.Etat==="on"?"Active":"Inactive")}],Oe=()=>{if(s.historyLoading)return e.createElement(_e,{size:"large",style:{display:"block",margin:"40px auto"}});if(s.historyError)return e.createElement(ke,{type:"error",message:"Erreur de chargement",description:s.historyError,showIcon:!0});if(!s.machineHistory||s.machineHistory.length===0)return e.createElement(z,{description:e.createElement(e.Fragment,null,e.createElement("p",null,"Aucune session trouvée pour cette machine"),e.createElement("p",null,"La table machine_sessions est vide ou aucune donnée n'est disponible"),e.createElement(v,{type:"primary",icon:e.createElement(R,null),onClick:()=>he(s.selectedMachine)},"Rafraîchir")),image:z.PRESENTED_IMAGE_SIMPLE});const t={labels:s.machineHistory.map(r=>{const n=new Date(r.session_start);return n.toLocaleDateString()+" "+n.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),datasets:[{label:"TRS (%)",data:s.machineHistory.map(r=>Number.parseFloat(r.TRS)||0),backgroundColor:"rgba(153, 102, 255, 0.2)",borderColor:"rgba(153, 102, 255, 1)",borderWidth:2,fill:!0}]},a={labels:s.machineHistory.map(r=>{const n=new Date(r.session_start);return n.toLocaleDateString()+" "+n.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),datasets:[{label:"Quantité bonne",data:s.machineHistory.map(r=>Number.parseFloat(r.Quantite_Bon)||0),backgroundColor:"rgba(75, 192, 192, 0.6)",borderColor:"rgba(75, 192, 192, 1)",borderWidth:1}]},i={labels:s.machineHistory.map(r=>{const n=new Date(r.session_start);return n.toLocaleDateString()+" "+n.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),datasets:[{label:"Quantité rejetée",data:s.machineHistory.map(r=>Number.parseFloat(r.Quantite_Rejet)||0),backgroundColor:"rgba(255, 99, 132, 0.6)",borderColor:"rgba(255, 99, 132, 1)",borderWidth:1}]},c={labels:s.machineHistory.map(r=>{const n=new Date(r.session_start);return n.toLocaleDateString()+" "+n.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),datasets:[{label:"Durée de session (min)",data:s.machineHistory.map(r=>{const n=new Date(r.session_start),l=r.session_end?new Date(r.session_end):new Date;return Math.round((l-n)/6e4)}),backgroundColor:"rgba(255, 159, 64, 0.6)",borderColor:"rgba(255, 159, 64, 1)",borderWidth:1}]};return e.createElement(de,{defaultActiveKey:"1",className:d?"dark-mode":""},e.createElement(W,{tab:"Sessions",key:"1"},e.createElement(Te,{columns:Fe,dataSource:s.machineHistory.map((r,n)=>({...r,key:n})),pagination:{pageSize:5},scroll:{x:!0}})),e.createElement(W,{tab:"Graphique",key:"2"},e.createElement(Q,{gutter:[16,16]},e.createElement(E,{xs:24,md:12},e.createElement("div",{className:"chart-container"},e.createElement("h3",{className:"chart-title"},e.createElement(st,null)," TRS (%)"),e.createElement("div",{style:{height:200}},e.createElement(Xe,{data:t,options:{...m,scales:{...m.scales,y:{...m.scales.y,beginAtZero:!0,max:100,grid:{color:d?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:d?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...m.scales.x,grid:{display:!1,color:d?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:d?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...m.plugins,legend:{...m.plugins.legend,labels:{...m.plugins.legend.labels,color:d?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}}})))),e.createElement(E,{xs:24,md:12},e.createElement("div",{className:"chart-container"},e.createElement("h3",{className:"chart-title"},e.createElement(G,null)," Production (pcs)"),e.createElement("div",{style:{height:200}},e.createElement(se,{data:a,options:{...m,scales:{...m.scales,y:{...m.scales.y,beginAtZero:!0,grid:{color:d?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:d?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...m.scales.x,grid:{display:!1,color:d?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:d?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...m.plugins,legend:{...m.plugins.legend,labels:{...m.plugins.legend.labels,color:d?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}}})))),e.createElement(E,{xs:24,md:12},e.createElement("div",{className:"chart-container"},e.createElement("h3",{className:"chart-title"},e.createElement(ce,null)," Rejets (pcs)"),e.createElement("div",{style:{height:200}},e.createElement(se,{data:i,options:{...m,scales:{...m.scales,y:{...m.scales.y,beginAtZero:!0,grid:{color:d?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:d?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...m.scales.x,grid:{display:!1,color:d?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:d?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...m.plugins,legend:{...m.plugins.legend,labels:{...m.plugins.legend.labels,color:d?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}}})))),e.createElement(E,{xs:24,md:12},e.createElement("div",{className:"chart-container"},e.createElement("h3",{className:"chart-title"},e.createElement(ie,null)," Durée des sessions (min)"),e.createElement("div",{style:{height:200}},e.createElement(se,{data:c,options:{...m,scales:{...m.scales,y:{...m.scales.y,beginAtZero:!0,grid:{color:d?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:d?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...m.scales.x,grid:{display:!1,color:d?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:d?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...m.plugins,legend:{...m.plugins.legend,labels:{...m.plugins.legend.labels,color:d?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}}})))))),e.createElement(W,{tab:e.createElement("span",null,e.createElement($,{style:{marginRight:8}}),"Informations"),key:"3"},e.createElement("div",{style:{padding:"16px 0"}},e.createElement(Q,{gutter:[24,24]},e.createElement(E,{xs:24,md:12},e.createElement(I,{title:e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(J,{style:{color:"#1890ff",marginRight:8}}),e.createElement("span",null,"Détails de la machine")),bordered:!0,style:{height:"100%"}},e.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:16}},e.createElement("div",{style:{width:64,height:64,borderRadius:8,background:"rgba(24, 144, 255, 0.1)",display:"flex",alignItems:"center",justifyContent:"center",marginRight:16}},e.createElement(J,{style:{fontSize:32,color:"#1890ff"}})),e.createElement("div",null,e.createElement(le,{level:4,style:{margin:0}},s.selectedMachine),e.createElement(k,{type:"secondary"},s.machineHistory.length>0&&s.machineHistory[0].Ordre_Fabrication?`OF: ${s.machineHistory[0].Ordre_Fabrication}`:"Aucun ordre de fabrication"))),e.createElement(q,{style:{margin:"16px 0"}}),e.createElement(Q,{gutter:[16,16]},e.createElement(E,{span:12},e.createElement(j,{title:e.createElement(k,{style:{fontSize:14}},"Sessions ",U()),value:s.machineHistory.filter(r=>ee(r,U())).length,prefix:e.createElement(Ne,null),valueStyle:{color:"#1890ff",fontSize:20}})),e.createElement(E,{span:12},e.createElement(j,{title:e.createElement(k,{style:{fontSize:14}},"Sessions actives ",U()),value:s.machineHistory.filter(r=>!r.session_end&&ee(r,U())).length,prefix:e.createElement(Pe,null),valueStyle:{color:s.machineHistory.filter(r=>!r.session_end&&ee(r,U())).length>0?"#52c41a":"#8c8c8c",fontSize:20}}))))),e.createElement(E,{xs:24,md:12},e.createElement(I,{title:e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(Ne,{style:{color:"#1890ff",marginRight:8}}),e.createElement("span",null,"Historique des sessions")),bordered:!0,style:{height:"100%"}},s.machineHistory.length>0?e.createElement(e.Fragment,null,e.createElement("div",{style:{marginBottom:16}},e.createElement(k,{strong:!0},"Dernière session:"),e.createElement("div",{style:{background:"rgba(0,0,0,0.02)",padding:"12px",borderRadius:"8px",marginTop:"8px"}},e.createElement("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:8}},e.createElement(k,null,"Début:"),e.createElement(k,{strong:!0},new Date(s.machineHistory[0].session_start).toLocaleString())),e.createElement("div",{style:{display:"flex",justifyContent:"space-between"}},e.createElement(k,null,"Fin:"),e.createElement(k,{strong:!0},s.machineHistory[0].session_end?new Date(s.machineHistory[0].session_end).toLocaleString():e.createElement(M,{color:"processing"},"En cours"))))),e.createElement(q,{style:{margin:"16px 0"}}),e.createElement(Q,{gutter:[16,16]},e.createElement(E,{span:8},e.createElement(j,{title:e.createElement(k,{style:{fontSize:14}},"TRS moyen"),value:(()=>{const r=s.machineHistory.map(n=>Number(n.TRS||0)).filter(n=>!isNaN(n));return r.length?(r.reduce((n,l)=>n+l,0)/r.length).toFixed(1):"N/A"})(),suffix:"%",valueStyle:{fontSize:18}})),e.createElement(E,{span:8},e.createElement(j,{title:e.createElement(k,{style:{fontSize:14}},"Pièces bonnes"),value:s.machineHistory.reduce((r,n)=>r+Number(n.Quantite_Bon||0),0),valueStyle:{color:"#52c41a",fontSize:18}})),e.createElement(E,{span:8},e.createElement(j,{title:e.createElement(k,{style:{fontSize:14}},"Pièces rejetées"),value:s.machineHistory.reduce((r,n)=>r+Number(n.Quantite_Rejet||0),0),valueStyle:{color:"#ff4d4f",fontSize:18}})))):e.createElement(z,{description:"Aucune donnée de session disponible",image:z.PRESENTED_IMAGE_SIMPLE}))),e.createElement(E,{xs:24},e.createElement(I,{title:e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(it,{style:{color:"#1890ff",marginRight:8}}),e.createElement("span",null,"Métriques de performance")),bordered:!0},s.machineHistory.length>0?e.createElement(Q,{gutter:[24,24]},e.createElement(E,{xs:24,md:8},e.createElement(I,{style:{background:"rgba(0,0,0,0.02)"}},e.createElement(j,{title:"Durée moyenne des sessions",value:(()=>{const r=s.machineHistory.map(g=>{const p=new Date(g.session_start);return(g.session_end?new Date(g.session_end):new Date)-p}),n=r.reduce((g,p)=>g+p,0)/r.length,l=Math.floor(n/36e5),o=Math.floor(n%36e5/6e4);return`${l}h ${o}m`})(),prefix:e.createElement(ie,null)}))),e.createElement(E,{xs:24,md:8},e.createElement(I,{style:{background:"rgba(0,0,0,0.02)"}},e.createElement(j,{title:"Taux de rejet moyen",value:(()=>{const r=s.machineHistory.reduce((l,o)=>l+Number(o.Quantite_Bon||0),0),n=s.machineHistory.reduce((l,o)=>l+Number(o.Quantite_Rejet||0),0);return r+n>0?(n/(r+n)*100).toFixed(1):"0.0"})(),suffix:"%",prefix:e.createElement(Ie,null),valueStyle:{color:"#faad14"}}))),e.createElement(E,{xs:24,md:8},e.createElement(I,{style:{background:"rgba(0,0,0,0.02)"}},e.createElement(j,{title:"Productivité",value:(()=>{const r=s.machineHistory.reduce((o,g)=>o+Number(g.Quantite_Bon||0),0),l=s.machineHistory.reduce((o,g)=>{const p=new Date(g.session_start),H=g.session_end?new Date(g.session_end):new Date;return o+(H-p)},0)/36e5;return l>0?Math.round(r/l):0})(),suffix:"pcs/h",prefix:e.createElement(ct,null),valueStyle:{color:"#52c41a"}})))):e.createElement(z,{description:"Aucune donnée de performance disponible",image:z.PRESENTED_IMAGE_SIMPLE})))))))};return w.useEffect(()=>{if(!ue||!(C!=null&&C.id))return;const a=`${window.location.protocol==="https:"?"wss:":"ws:"}//${window.location.host}/api/notifications`,i=new WebSocket(a);return i.onopen=()=>{C!=null&&C.id&&i.send(JSON.stringify({type:"auth",userId:C.id}))},i.onmessage=c=>{try{const r=JSON.parse(c.data);if(r.type==="notification"){const n=r.notification;n.category==="alert"?y.error({message:n.title,description:n.message,icon:e.createElement(ve,{style:{color:"#ff4d4f"}}),placement:"topRight",duration:5}):n.category==="maintenance"?y.warning({message:n.title,description:n.message,icon:e.createElement(Ge,{style:{color:"#faad14"}}),placement:"topRight",duration:5}):n.category==="update"?y.info({message:n.title,description:n.message,icon:e.createElement($,{style:{color:"#1890ff"}}),placement:"topRight",duration:4}):y.success({message:n.title,description:n.message,icon:e.createElement($,{style:{color:"#52c41a"}}),placement:"topRight",duration:4})}}catch(r){console.error("Error parsing WebSocket message:",r)}},i.onerror=()=>{A.error("Erreur de connexion aux notifications")},i.onclose=()=>{setTimeout(()=>{me(null)},3e3)},me(i),()=>{i&&i.close()}},[C==null?void 0:C.id,ue]),e.createElement("div",{style:{padding:"24px"}},e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:24}},e.createElement("div",null,e.createElement(le,{level:2}," Performance Temps Réel des Machines"),e.createElement(k,{type:"secondary"},He)),e.createElement(oe,null,e.createElement(v,{type:"primary",icon:e.createElement(R,null),onClick:xe},"Actualiser"))),s.error&&e.createElement(ke,{type:"error",message:"Erreur de connexion",description:`Dernière erreur: ${s.error} | Mise à jour: ${s.lastUpdate.toLocaleTimeString()}`,showIcon:!0,closable:!0,style:{marginBottom:16}}),e.createElement(q,null),e.createElement("div",{style:{marginBottom:16,display:"flex",justifyContent:"space-between",alignItems:"center"}},e.createElement(De.Group,{value:ge,onChange:Ue,buttonStyle:"solid"},e.createElement(De.Button,{value:"machines"},e.createElement(J,null)," Machines")),e.createElement(oe,null,e.createElement(Re,{title:"Filtrer les données"},e.createElement(v,{icon:e.createElement(rt,null)},"Filtres")),e.createElement(Re,{title:"Exporter les données"},e.createElement(v,{icon:e.createElement(ot,null)},"Exporter")))),e.createElement(Q,{gutter:[16,16],style:{marginBottom:"16px"}},e.createElement(E,{span:24},e.createElement(I,null,e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"}},e.createElement("div",null,e.createElement(k,{strong:!0},"Dernière mise à jour: ",s.lastUpdate.toLocaleTimeString()),T.connected?e.createElement(M,{color:"success",className:"ws-status-tag",style:{marginLeft:"10px"}},e.createElement(G,null)," Connecté en temps réel"):T.connecting?e.createElement(M,{color:"processing",className:"ws-status-tag",style:{marginLeft:"10px"}},e.createElement(R,{spin:!0})," Connexion en cours..."):e.createElement(M,{color:"warning",className:"ws-status-tag",style:{marginLeft:"10px"}},e.createElement(Ie,null)," Mode de secours"),T.updating&&e.createElement(M,{color:"blue",className:"ws-status-tag ws-status-updating",style:{marginLeft:"10px"}},e.createElement(R,{spin:!0})," Mise à jour en cours")),e.createElement(v,{type:"primary",icon:e.createElement(R,{spin:T.updating}),onClick:$e,loading:!T.connected&&s.loading,disabled:T.updating},"Rafraîchir les données"))))),ge==="machines"&&e.createElement(Q,{gutter:[18,18]},e.createElement(E,{xs:24,lg:24},e.createElement(I,{title:e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(J,{style:{fontSize:20,marginRight:8}}),e.createElement("span",null,"Statistiques des machines")),extra:e.createElement(V,{count:s.machineData.length,style:{backgroundColor:"#1890ff"}})},s.loading&&!T.connected?e.createElement("div",{style:{textAlign:"center",padding:"40px 0"}},e.createElement(_e,{size:"large"}),e.createElement("div",{style:{marginTop:16}},"Chargement des données...")):T.connecting?e.createElement("div",{style:{textAlign:"center",padding:"10px 0"}},e.createElement(V,{status:"processing",text:"Établissement de la connexion en temps réel...",style:{color:"#1890ff"}})):T.updating?e.createElement("div",{style:{textAlign:"center",padding:"10px 0"}},e.createElement(V,{status:"processing",text:"Mise à jour en temps réel...",style:{color:"#1890ff"}})):e.createElement(Q,{gutter:[16,16]},s.machineData.slice(0,4).map((t,a)=>e.createElement(E,{key:a,xs:24,sm:24,md:12},e.createElement("div",{className:"machine-card-container",style:{position:"relative"}},e.createElement(tt,{machine:t,handleMachineClick:X,getStatusColor:Se}),t.id!==1&&e.createElement("div",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%",backdropFilter:"blur(2px)",backgroundColor:"rgba(0, 0, 0, 0.05)",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",borderRadius:"8px",zIndex:10,border:"2px dashed #1890ff"}},e.createElement("div",{style:{fontSize:"26px",fontWeight:"bold",color:"#1890ff",textShadow:"2px 2px 4px rgba(0,0,0,0.2)",marginBottom:"10px"}},"En cours de développement ..."),e.createElement(v,{type:"primary",ghost:!0,size:"small",icon:e.createElement(Ce,null)},"Configuration requise")),!t.id&&e.createElement("div",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%",backdropFilter:"blur(2px)",backgroundColor:"rgba(0, 0, 0, 0.05)",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",borderRadius:"8px",zIndex:10,border:"2px dashed #1890ff"}},e.createElement("div",{style:{fontSize:"28px",fontWeight:"bold",color:"#1890ff",textShadow:"2px 2px 4px rgba(0,0,0,0.2)",marginBottom:"10px"}},"En cours de développement ..."),e.createElement(v,{type:"default",size:"small"},"Configuration requise"))))))))),e.createElement(q,null),e.createElement(I,{title:e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(Ce,{style:{fontSize:20,marginRight:8}}),e.createElement("span",null,"Détails des machines")),extra:e.createElement(oe,null,e.createElement(v,{type:"primary",icon:e.createElement(R,null),onClick:xe,size:"small"},"Actualiser"),e.createElement(M,{color:"processing"},s.machineData.filter(t=>t.Etat==="on").length," sessions actives"))},e.createElement(de,{defaultActiveKey:"1",className:d?"dark-mode":""},e.createElement(W,{tab:"Tableau",key:"1"},e.createElement(Te,{columns:We,dataSource:s.machineData.map((t,a)=>({...t,key:a})),pagination:{pageSize:10},scroll:{x:!0},onRow:t=>({onClick:()=>X(t)})})),e.createElement(W,{tab:"Cartes",key:"2"},e.createElement(Q,{gutter:[16,16]},s.machineData.map((t,a)=>e.createElement(E,{key:a,xs:24,sm:12,md:8,lg:6},e.createElement("div",{style:{position:"relative"}},e.createElement(I,{hoverable:!!t.id,onClick:()=>t.id&&X(t),style:{borderTop:`2px solid ${Se(t.status,t)}`}},e.createElement("div",{style:{textAlign:"center"}},e.createElement(le,{level:4},t.Machine_Name||"Machine"),e.createElement(Me,{type:"dashboard",percent:u(t.TRS||"0"),status:u(t.TRS)>80?"success":u(t.TRS)>60?"normal":"exception"}),t.Etat==="on"&&e.createElement(V,{status:"processing",text:"Session active",style:{marginTop:8}}),e.createElement("div",{style:{marginTop:8}},e.createElement(k,null,"Production: ",u(t.Quantite_Bon||0))))),t.id!==1&&e.createElement("div",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%",backdropFilter:"blur(2px)",backgroundColor:"rgba(0, 0, 0, 0.05)",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",borderRadius:"8px",zIndex:10,border:"2px dashed #1890ff"}},e.createElement("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#1890ff",textShadow:"2px 2px 4px rgba(0,0,0,0.2)",marginBottom:"8px"}},"En cours de développement ..."),e.createElement(v,{type:"default",size:"small"},"Configuration requise")),!t.id&&e.createElement("div",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%",backdropFilter:"blur(2px)",backgroundColor:"rgba(0, 0, 0, 0.05)",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",borderRadius:"8px",zIndex:10,border:"2px dashed #1890ff"}},e.createElement("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#1890ff",textShadow:"2px 2px 4px rgba(0,0,0,0.2)",marginBottom:"8px"}},"En cours de développement ..."),e.createElement(v,{type:"default",size:"small"},"Configuration requise"))))))))),e.createElement("div",{style:{position:"fixed",bottom:20,right:20,zIndex:1e3}},e.createElement(Ke,{content:e.createElement("div",{style:{width:250}},e.createElement("p",null,e.createElement("strong",null,"Outils disponibles:")),e.createElement("ul",null,e.createElement("li",null,"Vue des machines"),e.createElement("li",null,"Analyse détaillée des performances"),e.createElement("li",null,"Export des données")),e.createElement(v,{type:"primary",block:!0},"Guide d'utilisation")),title:"Aide et outils",trigger:"click",placement:"topRight"},e.createElement(v,{type:"primary",shape:"circle",icon:e.createElement($,null),size:"large",style:{boxShadow:"0 4px 12px rgba(0, 0, 0, 0.15)"}}))),e.createElement(Ye,{title:`Sessions de ${s.selectedMachine}`,open:s.visible,width:800,onCancel:()=>S(t=>({...t,visible:!1,historyError:null})),footer:[e.createElement(v,{key:"close",onClick:()=>S(t=>({...t,visible:!1,historyError:null}))},"Fermer"),e.createElement(v,{key:"allSessions",type:"primary",onClick:()=>window.open("/sessions-report","_blank")},"Voir toutes les sessions")],destroyOnClose:!0},Oe()))};export{Ht as default};
