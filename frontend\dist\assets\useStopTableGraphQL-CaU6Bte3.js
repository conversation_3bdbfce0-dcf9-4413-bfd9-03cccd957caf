import{r as s,$ as J}from"./index-gs31pxOi.js";let T=0;const _=3,X=(n,u,R,S=!1)=>{var w;const c=Date.now()-R;return n.name==="AbortError"||n.message&&n.message.includes("aborted")?(T++,T<=_?console.log(`🛑 Request ${u} was cancelled ${S?"during unmount":""} after ${c}ms`):T===_+1&&console.log("🛑 Additional abort errors will be suppressed to prevent console spam"),{isAbortError:!0,message:"Request was cancelled",requestId:u,responseTime:c}):n.message&&(n.message.includes("Failed to fetch")||n.message.includes("Network"))?(console.error(`❌ Request ${u}: Network error after ${c}ms`,n.message),{isNetworkError:!0,message:"Network error - please check your connection and try again",requestId:u,responseTime:c}):n.message&&n.message.includes("timeout")?(console.error(`⏱️ Request ${u}: Timeout after ${c}ms`),{isTimeoutError:!0,message:"Request timed out - server may be experiencing high load",requestId:u,responseTime:c}):n.graphQLErrors?(console.error(`🔍 Request ${u}: GraphQL error after ${c}ms`,n.graphQLErrors),{isGraphQLError:!0,message:((w=n.graphQLErrors[0])==null?void 0:w.message)||"GraphQL error",errors:n.graphQLErrors,requestId:u,responseTime:c}):(console.error(`❌ Request ${u}: Failed after ${c}ms:`,n),{isGeneralError:!0,message:n.message||"Unknown error occurred",originalError:n,requestId:u,responseTime:c})},H=()=>{T=0},Z=n=>n?n.isAbortError||n.name==="AbortError"||n.message&&(n.message.includes("aborted")||n.message.includes("cancelled")):!1,j="/api/graphql",C=3e4,f=1e4,W=10,ee=()=>{const[n,u]=s.useState(!1),[R,S]=s.useState(null),c=s.useRef(new Map),w=s.useRef(0),m=s.useRef(new Map),p=s.useRef({models:{data:null,timestamp:null},names:new Map}),A=s.useRef(new Map),d=s.useRef({cacheHits:0,cacheMisses:0,totalRequests:0,avgResponseTime:0,lastCleanup:Date.now()});s.useEffect(()=>{const e=()=>{const a=Date.now();for(const[o,l]of m.current.entries())a-l.timestamp>C&&m.current.delete(o);p.current.models.timestamp&&a-p.current.models.timestamp>C*2&&(p.current.models={data:null,timestamp:null});for(const[o,l]of p.current.names.entries())a-l.timestamp>C&&p.current.names.delete(o);if(m.current.size>W){const o=m.current.keys().next().value;m.current.delete(o)}d.current.lastCleanup=a};e();const t=setInterval(e,C);return()=>{clearInterval(t),console.log("🧹 GraphQL Hook: Cleaning up on unmount"),H(),c.current.forEach((a,o)=>{try{a.abort("Component unmounted")}catch{a.abort()}}),c.current.clear(),A.current.clear()}},[]);const E=s.useCallback(e=>{const t={model:(e==null?void 0:e.model)||"",machine:(e==null?void 0:e.machine)||"",startDate:(e==null?void 0:e.startDate)||"",endDate:(e==null?void 0:e.endDate)||"",date:(e==null?void 0:e.date)||"",limit:(e==null?void 0:e.limit)||1e3};return JSON.stringify(t)},[]),$=s.useCallback(e=>{const t=E(e),a=m.current.get(t);if(console.log("🔍 CACHE CHECK:",{cacheKey:t,hasCached:!!a,cacheSize:m.current.size}),!a)return null;const o=Date.now()-a.timestamp;return o>C?(m.current.delete(t),console.log("🗑️ CACHE EXPIRED:",{age:o,ttl:C}),null):(d.current.cacheHits++,console.log(`✅ CACHE HIT: Comprehensive data (age: ${o}ms)`,a.data.sidecards),a.data)},[E]),v=s.useCallback((e,t)=>{const a=E(t);m.current.set(a,{data:e,filters:{...t},timestamp:Date.now(),size:JSON.stringify(e).length}),d.current.cacheMisses++,console.log(`💾 CACHED: Comprehensive data (${m.current.size} entries)`)},[E]),D=s.useCallback(async(e,t={})=>{const a=JSON.stringify({query:e,variables:t});if(A.current.has(a))return console.log("🔄 DEDUP: Waiting for existing request"),A.current.get(a);const o=V(e,t);A.current.set(a,o);try{return await o}finally{A.current.delete(a)}},[]),V=s.useCallback(async(e,t={})=>{const a=Date.now();u(!0),S(null);const o=++w.current,l=new AbortController;console.log(`🚀 ADVANCED Request ${o}: Starting query`),d.current.totalRequests++;try{c.current.set(o,l);const h=new Promise((k,Y)=>setTimeout(()=>{l.abort(),Y(new Error(`Request timeout after ${f}ms`))},f)),r=await Promise.race([J.post(j).send({query:e,variables:t}).timeout(f).retry(2),h]);if(c.current.delete(o),r.status>=400)throw new Error(`HTTP ${r.status}: ${r.text||"Request failed"}`);const g=r.body;if(g.errors)throw console.error("GraphQL errors:",g.errors),new Error(`GraphQL: ${g.errors.map(k=>k.message).join(", ")}`);const q=Date.now()-a,y=d.current;return y.avgResponseTime=(y.avgResponseTime*(y.totalRequests-1)+q)/y.totalRequests,console.log(`✅ ADVANCED Request ${o}: Completed in ${q}ms (avg: ${Math.round(y.avgResponseTime)}ms)`),u(!1),g.data}catch(h){c.current.delete(o);const r=X(h,o,a);throw r.isAbortError?S(null):S(r.message),r}finally{u(!1)}},[]),L=s.useCallback(()=>{const e=d.current,t=e.totalRequests>0?e.cacheHits/(e.cacheHits+e.cacheMisses)*100:0;return{cacheHitRate:Math.round(t),totalRequests:e.totalRequests,avgResponseTime:Math.round(e.avgResponseTime),cacheEntries:m.current.size,lastCleanup:new Date(e.lastCleanup).toLocaleTimeString()}},[]),M=s.useCallback((e=null)=>{if(e){const t=E(e);m.current.delete(t),console.log("🗑️ INVALIDATED: Specific cache entry")}else m.current.clear(),p.current.models={data:null,timestamp:null},p.current.names.clear(),console.log("🗑️ INVALIDATED: All cache entries")},[E]),F=s.useCallback((e="User cancelled")=>{console.log(`🛑 Cancelling ${c.current.size} active requests`),H(),c.current.forEach((t,a)=>{try{t.abort(e)}catch{t.abort()}console.log(`🛑 Cancelled request ${a}: ${e}`)}),c.current.clear(),A.current.clear(),S(null),M()},[M]),i=s.useCallback(async(e={})=>{var o,l;console.log("🚀 ADVANCED: getComprehensiveStopData called with filters:",e);const t=$(e);if(t)return t;console.log("❌ CACHE MISS: Fetching fresh comprehensive data");const a=`
      query GetFinalComprehensiveStopData($filters: FinalOptimizedStopFilterInput) {
        getFinalComprehensiveStopData(filters: $filters) {
          # Raw stop data
          allStops {
            Machine_Name
            Date_Insert
            Part_NO
            Code_Stop
            Debut_Stop
            Fin_Stop_Time
            Regleur_Prenom
            duration_minutes
            Cause
            Raison_Arret
            Operateur
          }
          
          # Pre-computed analytics
          topStops {
            stopName
            count
          }
          
          stopReasons {
            reason
            count
          }
          
          machineComparison {
            Machine_Name
            stops
            totalDuration
          }
          
          operatorStats {
            operator
            interventions
            totalDuration
          }
          
          durationTrend {
            hour
            avgDuration
          }
          
          stopStats {
            Stop_Date
            Total_Stops
          }
          
          # Summary statistics
          sidecards {
            Arret_Totale
            Arret_Totale_nondeclare
          }
          
          # Metadata
          totalRecords
          queryExecutionTime
          cacheHit
        }
      }
    `;try{const r=(await D(a,{filters:e})).getFinalComprehensiveStopData;return console.log(`✅ ADVANCED: Comprehensive data loaded in ${r.queryExecutionTime}ms`),console.log(`📊 ADVANCED: Retrieved ${r.totalRecords} stops, ${r.topStops.length} top stops`),console.log("🔍 ADVANCED: Data structure returned:",{keys:Object.keys(r),sidecards:r.sidecards,allStopsCount:((o=r.allStops)==null?void 0:o.length)||0,firstStop:((l=r.allStops)==null?void 0:l[0])||null}),v(r,e),r}catch(h){throw console.error("❌ ADVANCED: getComprehensiveStopData failed:",h),h}},[$,v,D]),N=s.useCallback(async()=>{const e=p.current.models;if(e.data&&e.timestamp&&Date.now()-e.timestamp<C*2)return console.log("✅ UTILITY CACHE HIT: Machine models"),d.current.cacheHits++,e.data;const t=`
      query {
        getFinalStopMachineModels {
          model
        }
      }
    `;try{console.log("🔧 UTILITY: Fetching machine models");const o=(await D(t)).getFinalStopMachineModels.map(l=>l.model)||[];return p.current.models={data:o,timestamp:Date.now()},d.current.cacheMisses++,o}catch(a){console.error("❌ ADVANCED: getMachineModels failed:",a);const o=[{model:"IPS"},{model:"AKROS"},{model:"ML"},{model:"FCS"}];return p.current.models={data:o,timestamp:Date.now()},o.map(l=>l.model)}},[D]),b=s.useCallback(async(e={})=>{const t=e.model||"all",a=p.current.names.get(t);if(a&&Date.now()-a.timestamp<C)return console.log(`✅ UTILITY CACHE HIT: Machine names for ${t}`),d.current.cacheHits++,a.data;const o=`
      query GetFinalStopMachineNames($filters: FinalOptimizedStopFilterInput) {
        getFinalStopMachineNames(filters: $filters) {
          Machine_Name
        }
      }
    `;try{console.log(`🔧 UTILITY: Fetching machine names for ${t}`);const r=((await D(o,{filters:e})).getFinalStopMachineNames||[]).map(g=>({...g,model:e.model||null}));return console.log(`✅ Fetched ${r.length} machine names for ${t}`,r.slice(0,3).map(g=>g.Machine_Name)),p.current.names.set(t,{data:r,timestamp:Date.now()}),d.current.cacheMisses++,r}catch(l){return console.error("❌ ADVANCED: getMachineNames failed:",l),[]}},[D]),x=s.useCallback(async(e={})=>(console.log("🔄 ADVANCED: getAllMachineStops - extracting from comprehensive data"),{getAllMachineStops:(await i(e)).allStops}),[i]),I=s.useCallback(async(e={})=>(console.log("🔄 ADVANCED: getTop5Stops - extracting from comprehensive data"),{getTop5Stops:(await i(e)).topStops.slice(0,5)}),[i]),Q=s.useCallback(async(e={})=>(console.log("🔄 ADVANCED: getStopStats - extracting from comprehensive data"),{getStopStats:(await i(e)).stopStats}),[i]),G=s.useCallback(async(e={})=>(console.log("🔄 ADVANCED: getStopSidecards - extracting from comprehensive data"),{getStopSidecards:(await i(e)).sidecards}),[i]),O=s.useCallback(async(e,t={})=>{console.log("🔄 ADVANCED: getMachineStopComparison - extracting from comprehensive data");const a={...t};return e&&(a.date=e),{getMachineStopComparison:(await i(a)).machineComparison}},[i]),P=s.useCallback(async()=>(console.log("🔧 ADVANCED: getStopMachineModels - using cached utility query"),{getStopMachineModels:await N()}),[N]),z=s.useCallback(async(e={})=>(console.log("🔧 ADVANCED: getStopMachineNames - using cached utility query"),{getStopMachineNames:await b(e)}),[b]),K=s.useCallback(async(e={})=>{console.log("🚀 ADVANCED: getStopDashboardData called with filters:",e);try{const t=await i(e),a={allStops:t.allStops,topStops:t.topStops,sidecards:t.sidecards,stopComparison:t.machineComparison,stopReasons:t.stopReasons,operatorStats:t.operatorStats,durationTrend:t.durationTrend,stopStats:t.stopStats,totalRecords:t.totalRecords,queryExecutionTime:t.queryExecutionTime};return console.log(`✅ ADVANCED: Dashboard data loaded in ${t.queryExecutionTime}ms`),console.log("📊 ADVANCED: Data summary:",{allStopsCount:a.allStops.length,topStopsCount:a.topStops.length,machineComparisonCount:a.stopComparison.length,sidecards:a.sidecards}),a}catch(t){throw console.error("💥 ADVANCED: Error fetching dashboard data:",t),t}},[i]),U=s.useCallback(async(e={})=>{console.log("🚀 ADVANCED: getStopsAnalysisData called with filters:",e);try{const t=await i(e),a={durationTrend:t.durationTrend,operatorStats:t.operatorStats,stopReasons:t.stopReasons,stopStats:t.stopStats};return console.log(`✅ ADVANCED: Analysis data loaded in ${t.queryExecutionTime}ms`),a}catch(t){throw console.error("💥 ADVANCED: Error fetching analysis data:",t),t}},[i]);return{loading:n,error:R,getComprehensiveStopData:i,getMachineModels:N,getMachineNames:b,getStopDashboardData:K,getStopsAnalysisData:U,getAllMachineStops:x,getTop5Stops:I,getStopStats:Q,getStopSidecards:G,getMachineStopComparison:O,getStopMachineModels:P,getStopMachineNames:z,invalidateCache:M,getCacheStats:L,executeQueryWithDeduplication:D,cancelAllRequests:F}};export{Z as i,ee as u};
