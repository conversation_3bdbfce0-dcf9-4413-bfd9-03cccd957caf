import { esClient } from '../config/elasticsearch.js';

class ElasticsearchService {
  constructor() {
    this.client = esClient;
  }

  // Generic document indexing
  async indexDocument(index, id, document) {
    try {
      const response = await this.client.index({
        index,
        id,
        document: {  // Use 'document' instead of 'body' for ES 8.x
          ...document,
          indexed_at: new Date().toISOString()
        }
      });
      return response;
    } catch (error) {
      console.error(`Error indexing document in ${index}:`, error);
      throw error;
    }
  }

  // Bulk indexing for performance
  async bulkIndex(operations) {
    try {
      const response = await this.client.bulk({
        operations  // Use 'operations' instead of 'body' for ES 8.x
      });

      if (response.errors) {
        console.error('Bulk indexing errors:', response.items.filter(item => item.index?.error));
      }

      return response;
    } catch (error) {
      console.error('Error in bulk indexing:', error);
      throw error;
    }
  }

  // Search functionality
  async search(index, query, options = {}) {
    try {
      const searchParams = {
        index,
        ...query,  // Spread query directly instead of using body
        ...options
      };

      const response = await this.client.search(searchParams);
      return {
        hits: response.hits.hits,
        total: response.hits.total.value,
        aggregations: response.aggregations
      };
    } catch (error) {
      console.error(`Error searching in ${index}:`, error);
      throw error;
    }
  }

  // Machine sessions specific methods
  async indexMachineSession(sessionData) {
    const document = {
      sessionId: sessionData.id,
      machineId: sessionData.machine_id,
      machineName: sessionData.machine_name,
      machineModel: sessionData.machine_model,
      timestamp: sessionData.timestamp,
      startTime: sessionData.start_time,
      endTime: sessionData.end_time,
      duration: sessionData.duration,
      status: sessionData.status,
      production: {
        total: sessionData.production_total || 0,
        rate: sessionData.production_rate || 0,
        target: sessionData.production_target || 0,
        efficiency: sessionData.efficiency || 0
      },
      quality: {
        goodParts: sessionData.good_parts || 0,
        defects: sessionData.defects || 0,
        rejects: sessionData.rejects || 0,
        qualityRate: sessionData.quality_rate || 0
      },
      performance: {
        trs: sessionData.trs || 0,
        availability: sessionData.availability || 0,
        performance: sessionData.performance || 0,
        quality: sessionData.quality || 0
      },
      shift: sessionData.shift,
      operator: sessionData.operator,
      notes: sessionData.notes
    };

    return await this.indexDocument('machine-sessions', sessionData.id, document);
  }

  // Real-time data indexing
  async indexRealTimeData(realTimeData) {
    const document = {
      machineId: realTimeData.Machine_ID,
      machineName: realTimeData.Machine_Name,
      machineModel: realTimeData.Machine_Model,
      timestamp: new Date().toISOString(),
      status: realTimeData.Status,
      currentProduction: realTimeData.Current_Production || 0,
      productionRate: realTimeData.Production_Rate || 0,
      trs: realTimeData.TRS || 0,
      alerts: {
        active: realTimeData.Alert_Status === 'active',
        count: realTimeData.Alert_Count || 0,
        severity: realTimeData.Alert_Severity || 'none',
        messages: realTimeData.Alert_Messages || []
      },
      maintenance: {
        required: realTimeData.Maintenance_Required || false,
        lastMaintenance: realTimeData.Last_Maintenance,
        nextMaintenance: realTimeData.Next_Maintenance
      }
    };

    return await this.indexDocument('machine-realtime', realTimeData.Machine_ID, document);
  }

  // Reports indexing
  async indexReport(reportData) {
    const document = {
      reportId: reportData.id,
      type: reportData.type,
      title: reportData.title,
      description: reportData.description,
      content: this.extractTextContent(reportData),
      date: reportData.date,
      shift: reportData.shift,
      machineId: reportData.machine_id,
      machineName: reportData.machine_name,
      status: reportData.status,
      generatedAt: reportData.generated_at,
      generatedBy: reportData.generatedBy,
      tags: this.extractTags(reportData),
      data: reportData.data // Store raw data for retrieval
    };

    return await this.indexDocument('reports', reportData.id, document);
  }

  // Search machine sessions with filters
  async searchMachineSessions(searchParams) {
    const {
      query,
      machineId,
      machineModel,
      dateFrom,
      dateTo,
      status,
      shift,
      page = 1,
      size = 20
    } = searchParams;

    const must = [];
    const filter = [];

    // Text search
    if (query) {
      must.push({
        multi_match: {
          query,
          fields: ['machineName^2', 'operator', 'notes'],
          type: 'best_fields',
          fuzziness: 'AUTO'
        }
      });
    }

    // Filters
    if (machineId) filter.push({ term: { machineId } });
    if (machineModel) filter.push({ term: { machineModel } });
    if (status) filter.push({ term: { status } });
    if (shift) filter.push({ term: { shift } });

    // Date range
    if (dateFrom || dateTo) {
      const dateRange = {};
      if (dateFrom) dateRange.gte = dateFrom;
      if (dateTo) dateRange.lte = dateTo;
      filter.push({ range: { timestamp: dateRange } });
    }

    const searchQuery = {
      query: {
        bool: {
          must: must.length > 0 ? must : [{ match_all: {} }],
          filter
        }
      },
      sort: [{ timestamp: { order: 'desc' } }],
      from: (page - 1) * size,
      size
    };

    return await this.search('machine-sessions', searchQuery);
  }

  // Search reports with advanced filtering
  async searchReports(searchParams) {
    const {
      query,
      type,
      machineId,
      dateFrom,
      dateTo,
      generatedBy,
      page = 1,
      size = 20
    } = searchParams;

    const must = [];
    const filter = [];

    // Text search across multiple fields
    if (query) {
      must.push({
        multi_match: {
          query,
          fields: ['title^3', 'description^2', 'content', 'machineName'],
          type: 'best_fields',
          fuzziness: 'AUTO'
        }
      });
    }

    // Filters
    if (type) filter.push({ term: { type } });
    if (machineId) filter.push({ term: { machineId } });
    if (generatedBy) filter.push({ match: { generatedBy: generatedBy } });

    // Date range
    if (dateFrom || dateTo) {
      const dateRange = {};
      if (dateFrom) dateRange.gte = dateFrom;
      if (dateTo) dateRange.lte = dateTo;
      filter.push({ range: { date: dateRange } });
    }

    const searchQuery = {
      query: {
        bool: {
          must: must.length > 0 ? must : [{ match_all: {} }],
          filter
        }
      },
      highlight: {
        fields: {
          title: {},
          description: {},
          content: {}
        }
      },
      sort: [{ generatedAt: { order: 'desc' } }],
      from: (page - 1) * size,
      size
    };

    return await this.search('reports', searchQuery);
  }

  // Production data indexing
  async indexProductionData(productionData) {
    const document = {
      recordId: productionData.id || `${productionData.Machine_Name}_${productionData.Date_Insert_Day}`,
      machineId: productionData.machine_id || productionData.Machine_Name,
      machineName: productionData.Machine_Name,
      machineModel: this.extractMachineModel(productionData.Machine_Name),
      date: this.parseDate(productionData.Date_Insert_Day),
      shift: productionData.Shift || this.determineShift(),
      operator: productionData.Regleur_Prenom || 'Unknown',
      production: {
        good: parseInt(productionData.Good_QTY_Day) || 0,
        rejects: parseInt(productionData.Rejects_QTY_Day) || 0,
        total: (parseInt(productionData.Good_QTY_Day) || 0) + (parseInt(productionData.Rejects_QTY_Day) || 0),
        target: parseInt(productionData.Target_QTY_Day) || 0,
        rate: parseFloat(productionData.Production_Rate_Day) || 0
      },
      performance: {
        oee: parseFloat(productionData.OEE_Day) || 0,
        availability: parseFloat(productionData.Availability_Rate_Day) || 0,
        performance: parseFloat(productionData.Performance_Rate_Day) || 0,
        quality: parseFloat(productionData.Quality_Rate_Day) || 0,
        trs: parseFloat(productionData.TRS_Day) || parseFloat(productionData.OEE_Day) || 0
      },
      timing: {
        runHours: parseFloat(productionData.Run_Hours_Day) || 0,
        downHours: parseFloat(productionData.Down_Hours_Day) || 0,
        speed: parseFloat(productionData.Speed_Day) || 0
      },
      partInfo: {
        partNumber: productionData.Part_Number || 'Unknown',
        mouldNumber: productionData.Mould_Number || 'Unknown',
        unitWeight: parseFloat(productionData.Poid_Unitaire) || 0,
        theoreticalCycle: parseFloat(productionData.Cycle_Theorique) || 0,
        purgeWeight: parseFloat(productionData.Poid_Purge) || 0
      },
      orderInfo: {
        orderNumber: productionData.Ordre_Fabrication || 'Unknown',
        article: productionData.Article || 'Unknown',
        plannedQuantity: parseInt(productionData.Quantite_Planifier) || 0
      }
    };

    return await this.indexDocument('production-data', document.recordId, document);
  }

  // Machine stops indexing
  async indexMachineStop(stopData) {
    const document = {
      stopId: stopData.id || `${stopData.Machine_Name}_${stopData.Date_Insert}_${stopData.Debut_Stop}`,
      machineId: stopData.machine_id || stopData.Machine_Name,
      machineName: stopData.Machine_Name,
      machineModel: this.extractMachineModel(stopData.Machine_Name),
      stopCode: stopData.Code_Stop,
      stopDescription: this.getStopDescription(stopData.Code_Stop),
      stopCategory: this.categorizeStopCode(stopData.Code_Stop),
      severity: this.determineStopSeverity(stopData.Code_Stop),
      dateInsert: this.parseDate(stopData.Date_Insert),
      startTime: this.parseDateTime(stopData.Debut_Stop),
      endTime: this.parseDateTime(stopData.Fin_Stop_Time),
      duration: stopData.duration_minutes || this.calculateDuration(stopData.Debut_Stop, stopData.Fin_Stop_Time),
      operator: stopData.Regleur_Prenom || 'Unknown',
      shift: stopData.Shift || this.determineShift(),
      partNumber: stopData.Part_NO || 'Unknown',
      orderNumber: stopData.Ordre_Fabrication || 'Unknown',
      resolution: {
        resolved: stopData.Fin_Stop_Time ? true : false,
        resolvedBy: stopData.Resolved_By || null,
        resolvedAt: stopData.Fin_Stop_Time ? this.parseDateTime(stopData.Fin_Stop_Time) : null,
        notes: stopData.Resolution_Notes || null
      },
      maintenance: {
        required: this.isMaintenanceRequired(stopData.Code_Stop),
        type: this.getMaintenanceType(stopData.Code_Stop),
        priority: this.getMaintenancePriority(stopData.Code_Stop)
      }
    };

    return await this.indexDocument('machine-stops', document.stopId, document);
  }

  // Search production data with advanced filtering
  async searchProductionData(searchParams) {
    const {
      query,
      machineId,
      machineModel,
      dateFrom,
      dateTo,
      shift,
      operator,
      minOEE,
      maxOEE,
      page = 1,
      size = 20
    } = searchParams;

    const must = [];
    const filter = [];

    // Text search
    if (query) {
      must.push({
        multi_match: {
          query,
          fields: ['machineName^2', 'operator', 'partInfo.partNumber', 'orderInfo.article'],
          type: 'best_fields',
          fuzziness: 'AUTO'
        }
      });
    }

    // Filters
    if (machineId) filter.push({ term: { machineId } });
    if (machineModel) filter.push({ term: { machineModel } });
    if (shift) filter.push({ term: { shift } });
    if (operator) filter.push({ match: { operator: operator } });

    // Date range
    if (dateFrom || dateTo) {
      const dateRange = {};
      if (dateFrom) dateRange.gte = dateFrom;
      if (dateTo) dateRange.lte = dateTo;
      filter.push({ range: { date: dateRange } });
    }

    // OEE range
    if (minOEE !== undefined || maxOEE !== undefined) {
      const oeeRange = {};
      if (minOEE !== undefined) oeeRange.gte = minOEE;
      if (maxOEE !== undefined) oeeRange.lte = maxOEE;
      filter.push({ range: { 'performance.oee': oeeRange } });
    }

    const searchQuery = {
      query: {
        bool: {
          must: must.length > 0 ? must : [{ match_all: {} }],
          filter
        }
      },
      sort: [{ date: { order: 'desc' } }],
      from: (page - 1) * size,
      size
    };

    return await this.search('production-data', searchQuery);
  }

  // Search machine stops with advanced filtering
  async searchMachineStops(searchParams) {
    const {
      query,
      machineId,
      machineModel,
      stopCode,
      stopCategory,
      severity,
      dateFrom,
      dateTo,
      resolved,
      maintenanceRequired,
      page = 1,
      size = 20
    } = searchParams;

    const must = [];
    const filter = [];

    // Text search
    if (query) {
      must.push({
        multi_match: {
          query,
          fields: ['machineName^2', 'stopDescription^2', 'stopCode', 'operator', 'resolution.notes'],
          type: 'best_fields',
          fuzziness: 'AUTO'
        }
      });
    }

    // Filters
    if (machineId) filter.push({ term: { machineId } });
    if (machineModel) filter.push({ term: { machineModel } });
    if (stopCode) filter.push({ term: { stopCode } });
    if (stopCategory) filter.push({ term: { stopCategory } });
    if (severity) filter.push({ term: { severity } });
    if (resolved !== undefined) filter.push({ term: { 'resolution.resolved': resolved } });
    if (maintenanceRequired !== undefined) filter.push({ term: { 'maintenance.required': maintenanceRequired } });

    // Date range
    if (dateFrom || dateTo) {
      const dateRange = {};
      if (dateFrom) dateRange.gte = dateFrom;
      if (dateTo) dateRange.lte = dateTo;
      filter.push({ range: { dateInsert: dateRange } });
    }

    const searchQuery = {
      query: {
        bool: {
          must: must.length > 0 ? must : [{ match_all: {} }],
          filter
        }
      },
      sort: [{ dateInsert: { order: 'desc' } }],
      from: (page - 1) * size,
      size
    };

    return await this.search('machine-stops', searchQuery);
  }

  // Analytics and aggregations
  async getMachinePerformanceAggregation(dateFrom, dateTo) {
    const searchQuery = {
      query: {
        range: {
          timestamp: {
            gte: dateFrom,
            lte: dateTo
          }
        }
      },
      size: 0,
      aggs: {
        machines: {
          terms: {
            field: 'machineId',
            size: 100
          },
          aggs: {
            avg_trs: { avg: { field: 'performance.trs' } },
            avg_efficiency: { avg: { field: 'production.efficiency' } },
            total_production: { sum: { field: 'production.total' } },
            session_count: { value_count: { field: 'sessionId' } }
          }
        }
      }
    };

    return await this.search('machine-sessions', searchQuery);
  }

  // Helper methods
  extractTextContent(reportData) {
    let content = '';
    if (reportData.title) content += reportData.title + ' ';
    if (reportData.description) content += reportData.description + ' ';
    
    // Extract content from JSON data
    if (reportData.data) {
      try {
        const data = typeof reportData.data === 'string' ? JSON.parse(reportData.data) : reportData.data;
        content += this.flattenObjectToText(data);
      } catch (e) {
        console.error('Error parsing report data for text extraction:', e);
      }
    }
    
    return content.trim();
  }

  extractTags(reportData) {
    const tags = [];
    if (reportData.type) tags.push(reportData.type);
    if (reportData.shift) tags.push(`shift-${reportData.shift}`);
    if (reportData.machine_name) tags.push(reportData.machine_name);
    return tags;
  }

  flattenObjectToText(obj, prefix = '') {
    let text = '';
    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'object' && value !== null) {
        text += this.flattenObjectToText(value, `${prefix}${key}.`);
      } else if (typeof value === 'string' || typeof value === 'number') {
        text += `${value} `;
      }
    }
    return text;
  }

  // Helper methods for production and stops data
  extractMachineModel(machineName) {
    if (!machineName) return 'Unknown';
    // Extract model from machine name (e.g., "IPS01" from "IPS01_Machine")
    const match = machineName.match(/^([A-Z]+\d+)/);
    return match ? match[1] : machineName.split('_')[0] || 'Unknown';
  }

  parseDate(dateStr) {
    if (!dateStr) return null;
    try {
      // Handle DD/MM/YYYY format
      const parts = dateStr.split('/');
      if (parts.length === 3) {
        return `${parts[2]}-${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}`;
      }
      return dateStr;
    } catch (e) {
      return null;
    }
  }

  parseDateTime(dateTimeStr) {
    if (!dateTimeStr) return null;
    try {
      // Handle DD/MM/YYYY HH:mm:ss format
      const [datePart, timePart] = dateTimeStr.split(' ');
      if (datePart && timePart) {
        const parts = datePart.split('/');
        if (parts.length === 3) {
          return `${parts[2]}-${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}T${timePart}`;
        }
      }
      return dateTimeStr;
    } catch (e) {
      return null;
    }
  }

  calculateDuration(startTime, endTime) {
    if (!startTime || !endTime) return 0;
    try {
      const start = new Date(this.parseDateTime(startTime));
      const end = new Date(this.parseDateTime(endTime));
      return Math.round((end - start) / (1000 * 60)); // minutes
    } catch (e) {
      return 0;
    }
  }

  determineShift() {
    const hour = new Date().getHours();
    if (hour >= 6 && hour < 14) return 'morning';
    if (hour >= 14 && hour < 22) return 'afternoon';
    return 'night';
  }

  getStopDescription(stopCode) {
    const stopDescriptions = {
      'MAINTENANCE': 'Maintenance préventive ou corrective',
      'CHANGEMENT': 'Changement de série ou d\'outillage',
      'REGLAGE': 'Réglage machine ou paramètres',
      'PROBLEME': 'Problème technique ou défaillance',
      'ARRET_PROD': 'Arrêt de production planifié',
      'PANNE': 'Panne machine ou équipement',
      'QUALITE': 'Problème qualité produit',
      'MATERIEL': 'Manque ou problème matériel',
      'NON_DECLARE': 'Arrêt non déclaré'
    };
    return stopDescriptions[stopCode] || stopCode || 'Arrêt non spécifié';
  }

  categorizeStopCode(stopCode) {
    if (!stopCode) return 'unknown';
    const code = stopCode.toUpperCase();

    if (code.includes('MAINTENANCE') || code.includes('PANNE')) return 'maintenance';
    if (code.includes('CHANGEMENT') || code.includes('REGLAGE')) return 'setup';
    if (code.includes('QUALITE')) return 'quality';
    if (code.includes('MATERIEL')) return 'material';
    if (code.includes('ARRET_PROD')) return 'planned';
    if (code.includes('NON_DECLARE')) return 'undeclared';

    return 'operational';
  }

  determineStopSeverity(stopCode) {
    if (!stopCode) return 'low';
    const code = stopCode.toUpperCase();

    if (code.includes('PANNE') || code.includes('EMERGENCY')) return 'high';
    if (code.includes('MAINTENANCE') || code.includes('QUALITE')) return 'medium';
    if (code.includes('NON_DECLARE')) return 'high';

    return 'low';
  }

  isMaintenanceRequired(stopCode) {
    if (!stopCode) return false;
    const code = stopCode.toUpperCase();
    return code.includes('MAINTENANCE') || code.includes('PANNE') || code.includes('PROBLEME');
  }

  getMaintenanceType(stopCode) {
    if (!stopCode) return null;
    const code = stopCode.toUpperCase();

    if (code.includes('MAINTENANCE')) return 'preventive';
    if (code.includes('PANNE') || code.includes('PROBLEME')) return 'corrective';

    return null;
  }

  getMaintenancePriority(stopCode) {
    if (!stopCode) return 'low';
    const code = stopCode.toUpperCase();

    if (code.includes('PANNE') || code.includes('EMERGENCY')) return 'high';
    if (code.includes('MAINTENANCE')) return 'medium';

    return 'low';
  }

  // SQL Fallback methods when Elasticsearch is unavailable
  async searchProductionDataSQL(searchParams, db) {
    const {
      query,
      machineId,
      machineModel,
      dateFrom,
      dateTo,
      shift,
      operator,
      minOEE,
      maxOEE,
      page = 1,
      size = 20
    } = searchParams;

    let sqlQuery = `
      SELECT * FROM daily_table_mould
      WHERE 1=1
    `;
    const queryParams = [];

    // Add search conditions
    if (query) {
      sqlQuery += ` AND (Machine_Name LIKE ? OR Regleur_Prenom LIKE ? OR Part_Number LIKE ?)`;
      const searchTerm = `%${query}%`;
      queryParams.push(searchTerm, searchTerm, searchTerm);
    }

    if (machineId) {
      sqlQuery += ` AND Machine_Name = ?`;
      queryParams.push(machineId);
    }

    if (machineModel) {
      sqlQuery += ` AND Machine_Name LIKE ?`;
      queryParams.push(`${machineModel}%`);
    }

    if (dateFrom) {
      sqlQuery += ` AND Date_Insert_Day >= ?`;
      queryParams.push(dateFrom);
    }

    if (dateTo) {
      sqlQuery += ` AND Date_Insert_Day <= ?`;
      queryParams.push(dateTo);
    }

    if (operator) {
      sqlQuery += ` AND Regleur_Prenom LIKE ?`;
      queryParams.push(`%${operator}%`);
    }

    if (minOEE !== undefined) {
      sqlQuery += ` AND OEE_Day >= ?`;
      queryParams.push(minOEE);
    }

    if (maxOEE !== undefined) {
      sqlQuery += ` AND OEE_Day <= ?`;
      queryParams.push(maxOEE);
    }

    // Add pagination
    const offset = (page - 1) * size;
    sqlQuery += ` ORDER BY Date_Insert_Day DESC LIMIT ? OFFSET ?`;
    queryParams.push(size, offset);

    try {
      const [results] = await db.execute(sqlQuery, queryParams);

      // Get total count
      let countQuery = sqlQuery.replace(/SELECT \*/, 'SELECT COUNT(*) as total')
                               .replace(/ORDER BY.*$/, '');
      const countParams = queryParams.slice(0, -2); // Remove LIMIT and OFFSET
      const [countResult] = await db.execute(countQuery, countParams);

      return {
        production: results,
        total: countResult[0]?.total || 0,
        page,
        size,
        totalPages: Math.ceil((countResult[0]?.total || 0) / size)
      };
    } catch (error) {
      console.error('SQL fallback search failed:', error);
      throw error;
    }
  }

  async searchMachineStopsSQL(searchParams, db) {
    const {
      query,
      machineId,
      machineModel,
      stopCode,
      stopCategory,
      dateFrom,
      dateTo,
      resolved,
      page = 1,
      size = 20
    } = searchParams;

    let sqlQuery = `
      SELECT * FROM arrets_table
      WHERE 1=1
    `;
    const queryParams = [];

    // Add search conditions
    if (query) {
      sqlQuery += ` AND (Machine_Name LIKE ? OR Code_Stop LIKE ? OR Regleur_Prenom LIKE ?)`;
      const searchTerm = `%${query}%`;
      queryParams.push(searchTerm, searchTerm, searchTerm);
    }

    if (machineId) {
      sqlQuery += ` AND Machine_Name = ?`;
      queryParams.push(machineId);
    }

    if (machineModel) {
      sqlQuery += ` AND Machine_Name LIKE ?`;
      queryParams.push(`${machineModel}%`);
    }

    if (stopCode) {
      sqlQuery += ` AND Code_Stop = ?`;
      queryParams.push(stopCode);
    }

    if (dateFrom) {
      sqlQuery += ` AND Date_Insert >= ?`;
      queryParams.push(dateFrom);
    }

    if (dateTo) {
      sqlQuery += ` AND Date_Insert <= ?`;
      queryParams.push(dateTo);
    }

    if (resolved !== undefined) {
      if (resolved) {
        sqlQuery += ` AND Fin_Stop_Time IS NOT NULL`;
      } else {
        sqlQuery += ` AND Fin_Stop_Time IS NULL`;
      }
    }

    // Add pagination
    const offset = (page - 1) * size;
    sqlQuery += ` ORDER BY Date_Insert DESC LIMIT ? OFFSET ?`;
    queryParams.push(size, offset);

    try {
      const [results] = await db.execute(sqlQuery, queryParams);

      // Get total count
      let countQuery = sqlQuery.replace(/SELECT \*/, 'SELECT COUNT(*) as total')
                               .replace(/ORDER BY.*$/, '');
      const countParams = queryParams.slice(0, -2); // Remove LIMIT and OFFSET
      const [countResult] = await db.execute(countQuery, countParams);

      return {
        stops: results,
        total: countResult[0]?.total || 0,
        page,
        size,
        totalPages: Math.ceil((countResult[0]?.total || 0) / size)
      };
    } catch (error) {
      console.error('SQL fallback search failed:', error);
      throw error;
    }
  }
}

export default new ElasticsearchService();
