import React, { useEffect, useRef, useState } from 'react';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, LineElement, PointElement } from 'chart.js';
import { Line } from 'react-chartjs-2';
import { Spin, Empty, Button, Space, Row, Col } from 'antd';
import { LineChartOutlined, ClockCircleOutlined } from '@ant-design/icons';

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, LineElement, PointElement);

const ArretTimePatternChart = ({ data = [], loading = false }) => {
  const chartRef = useRef();
  const [viewMode, setViewMode] = useState('count'); // 'count', 'duration', or 'both'
  // Process data to analyze time patterns with outlier filtering
  const processTimePatterns = (stopsData) => {
    if (!Array.isArray(stopsData) || stopsData.length === 0) {
      return { labels: [], stopCounts: [], avgDurations: [] };
    }

    // Configuration for outlier filtering
    const MAX_REASONABLE_DURATION = 480; // 8 hours - anything longer is likely maintenance
    const MIN_REASONABLE_DURATION = 1;   // 1 minute minimum
    
    // Group by hour of day
    const hourlyStats = {};
    let outlierCount = 0;
    let totalStopsProcessed = 0;
    
    // Initialize all hours
    for (let hour = 0; hour < 24; hour++) {
      hourlyStats[hour] = {
        count: 0,
        totalDuration: 0,
        avgDuration: 0,
        durations: [], // Track individual durations for debugging
        outliers: []   // Track outliers separately
      };
    }
    
    // Helper function to parse date in DD/MM/YYYY HH:mm format
    const parseCustomDate = (dateStr) => {
      if (!dateStr) return null;
      try {
        // Handle format like "30/09/2024 23:16" or " 3/12/2024 09:55:38" or "29/04/2025  14:06:41"
        const trimmed = dateStr.trim();
        
        // Split by spaces, but handle multiple spaces
        const parts = trimmed.split(/\s+/).filter(part => part.length > 0);
        
        if (parts.length >= 2) {
          const datePart = parts[0];
          const timePart = parts[1];
          
          const dateComponents = datePart.split('/');
          const timeComponents = timePart.split(':');
          
          // Validate we have all required components
          if (dateComponents.length === 3 && timeComponents.length >= 2) {
            const [day, month, year] = dateComponents;
            const [hour, minute, second] = timeComponents;
            
            // Ensure all components exist before using padStart
            if (day && month && year && hour && minute) {
              // Create date in ISO format (YYYY-MM-DD HH:mm:ss)
              const isoDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}T${hour.padStart(2, '0')}:${minute.padStart(2, '0')}:${(second || '00').padStart(2, '0')}`;
              const parsedDate = new Date(isoDate);
              
              if (!isNaN(parsedDate.getTime())) {
                return parsedDate;
              }
            }
          }
        }
      } catch (error) {
        console.warn('Error parsing date:', dateStr, error);
      }
      return null;
    };
    
    // Debug logging
    console.log('🕐 Processing time patterns for', stopsData.length, 'stops');
    
    stopsData.forEach(stop => {
      if (stop.Debut_Stop) {
        try {
          const startTime = parseCustomDate(stop.Debut_Stop);
          if (startTime && !isNaN(startTime.getTime())) {
            const hour = startTime.getHours();
            totalStopsProcessed++;
            
            if (hourlyStats[hour]) {
              // Calculate duration - prefer pre-calculated duration_minutes from GraphQL
              let duration = 0;
              
              if (stop.duration_minutes !== undefined && stop.duration_minutes !== null) {
                // Use pre-calculated duration from GraphQL resolver
                duration = parseFloat(stop.duration_minutes);
              } else if (stop.Fin_Stop_Time) {
                // Fallback to calculating from start/end times
                const endTime = parseCustomDate(stop.Fin_Stop_Time);
                if (endTime && !isNaN(endTime.getTime())) {
                  duration = (endTime - startTime) / (1000 * 60); // Duration in minutes
                }
              }
              
              if (duration > 0) {
                // Check if duration is within reasonable bounds
                if (duration >= MIN_REASONABLE_DURATION && duration <= MAX_REASONABLE_DURATION) {
                  // Normal operational stop
                  hourlyStats[hour].count += 1;
                  hourlyStats[hour].totalDuration += duration;
                  hourlyStats[hour].durations.push(duration);
                } else {
                  // Outlier - track but don't include in average
                  hourlyStats[hour].outliers.push(duration);
                  outlierCount++;
                  console.warn(`🚨 Outlier detected at hour ${hour}: ${duration.toFixed(1)}min (${duration > MAX_REASONABLE_DURATION ? 'too long' : 'too short'})`);
                }
              }
            }
          }
        } catch (error) {
          console.warn('Error parsing time:', error);
        }
      }
    });

    // Calculate averages and add detailed logging
    Object.keys(hourlyStats).forEach(hour => {
      const stats = hourlyStats[hour];
      stats.avgDuration = stats.count > 0 ? stats.totalDuration / stats.count : 0;
      
      // Debug logging for hours with significant activity
      if (stats.count > 0 || stats.outliers.length > 0) {
        console.log(`⏰ Hour ${hour}:00 - ${stats.count} normal stops, ${stats.outliers.length} outliers`);
        console.log(`   Normal: total ${stats.totalDuration.toFixed(1)}min, avg: ${stats.avgDuration.toFixed(1)}min`);
        
        if (stats.outliers.length > 0) {
          console.log(`   Outliers: [${stats.outliers.map(d => d.toFixed(1)).join(', ')}]min`);
        }
      }
    });

    const labels = Object.keys(hourlyStats).map(hour => {
      const h = parseInt(hour);
      return `${h.toString().padStart(2, '0')}:00`;
    });
    
    const stopCounts = Object.values(hourlyStats).map(stats => stats.count);
    const avgDurations = Object.values(hourlyStats).map(stats => Math.round(stats.avgDuration));

    // Final debug log with data quality summary
    console.log('📊 Time Pattern Processing Summary:', {
      totalStopsProcessed,
      outlierCount,
      outlierPercentage: ((outlierCount / totalStopsProcessed) * 100).toFixed(1) + '%',
      totalHoursWithData: stopCounts.filter(count => count > 0).length,
      maxStopsInHour: Math.max(...stopCounts),
      maxAvgDuration: Math.max(...avgDurations),
      avgDurationsFiltered: avgDurations.filter(d => d > 0)
    });

    return {
      labels,
      stopCounts,
      avgDurations
    };
  };

  const chartData = processTimePatterns(data);

  const getDatasets = () => {
    if (viewMode === 'count') {
      return [
        {
          label: 'Nombre d\'Arrêts par Heure',
          data: chartData.stopCounts,
          borderColor: 'rgba(54, 162, 235, 1)',
          backgroundColor: 'rgba(54, 162, 235, 0.1)',
          borderWidth: 3,
          fill: true,
          tension: 0.4,
          pointRadius: 6,
          pointHoverRadius: 8,
          pointBackgroundColor: 'rgba(54, 162, 235, 1)',
          pointBorderColor: 'rgba(255, 255, 255, 2)',
          pointBorderWidth: 2
        }
      ];
    } else if (viewMode === 'duration') {
      return [
        {
          label: 'Durée Moyenne (min)',
          data: chartData.avgDurations,
          borderColor: 'rgba(255, 99, 132, 1)',
          backgroundColor: 'rgba(255, 99, 132, 0.1)',
          borderWidth: 3,
          fill: true,
          tension: 0.4,
          pointRadius: 6,
          pointHoverRadius: 8,
          pointBackgroundColor: 'rgba(255, 99, 132, 1)',
          pointBorderColor: 'rgba(255, 255, 255, 2)',
          pointBorderWidth: 2
        }
      ];
    } else {
      // both mode
      return [
        {
          label: 'Nombre d\'Arrêts par Heure',
          data: chartData.stopCounts,
          borderColor: 'rgba(54, 162, 235, 1)',
          backgroundColor: 'rgba(54, 162, 235, 0.1)',
          borderWidth: 3,
          fill: true,
          tension: 0.4,
          pointRadius: 6,
          pointHoverRadius: 8,
          pointBackgroundColor: 'rgba(54, 162, 235, 1)',
          pointBorderColor: 'rgba(255, 255, 255, 2)',
          pointBorderWidth: 2,
          yAxisID: 'y'
        },
        {
          label: 'Durée Moyenne (min)',
          data: chartData.avgDurations,
          borderColor: 'rgba(255, 99, 132, 1)',
          backgroundColor: 'rgba(255, 99, 132, 0.1)',
          borderWidth: 3,
          fill: true,
          tension: 0.4,
          pointRadius: 6,
          pointHoverRadius: 8,
          pointBackgroundColor: 'rgba(255, 99, 132, 1)',
          pointBorderColor: 'rgba(255, 255, 255, 2)',
          pointBorderWidth: 2,
          yAxisID: 'y1'
        }
      ];
    }
  };  const getOptions = () => {
    const baseOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
          labels: {
            usePointStyle: true,
            padding: 20,
            font: {
              size: 12,
              weight: '500'
            }
          }
        },
        title: {
          display: true,
          text: viewMode === 'count' ? 'Nombre d\'Arrêts par Heure' : 
                viewMode === 'duration' ? 'Durée Moyenne des Arrêts par Heure' : 
                'Motifs Temporels des Arrêts',
          font: {
            size: 16,
            weight: 'bold'
          },
          padding: 20
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: 'white',
          bodyColor: 'white',
          borderColor: 'rgba(255, 255, 255, 0.1)',
          borderWidth: 1,
          cornerRadius: 8,
          displayColors: true,
          callbacks: {
            title: function(context) {
              return `Heure: ${context[0].label}`;
            },
            label: function(context) {
              const label = context.dataset.label || '';
              const value = context.parsed.y;
              
              if (label.includes('Nombre')) {
                return `${label}: ${value} arrêts`;
              } else {
                return `${label}: ${value} minutes`;
              }
            }
          }
        }
      },
      scales: {
        x: {
          grid: {
            display: true,
            color: 'rgba(0, 0, 0, 0.05)'
          },
          title: {
            display: true,
            text: 'Heure de la Journée',
            font: {
              size: 12,
              weight: 'bold'
            }
          },
          ticks: {
            font: {
              size: 11
            },
            maxRotation: 45,
            minRotation: 0
          }
        }
      },
      interaction: {
        intersect: false,
        mode: 'index'
      },
      animation: {
        duration: 1000,
        easing: 'easeInOutQuart'
      }
    };

    if (viewMode === 'both') {
      // Dual axis for both mode
      baseOptions.scales.y = {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: 'Nombre d\'Arrêts',
          font: {
            size: 12,
            weight: 'bold'
          }
        },
        grid: {
          color: 'rgba(54, 162, 235, 0.1)'
        },
        ticks: {
          beginAtZero: true,
          font: {
            size: 11
          }
        }
      };
      baseOptions.scales.y1 = {
        type: 'linear',
        display: true,
        position: 'right',
        title: {
          display: true,
          text: 'Durée Moyenne (min)',
          font: {
            size: 12,
            weight: 'bold'
          }
        },
        grid: {
          drawOnChartArea: false,
        },
        ticks: {
          beginAtZero: true,
          font: {
            size: 11
          }
        }
      };
    } else {
      // Single axis
      baseOptions.scales.y = {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: viewMode === 'count' ? 'Nombre d\'Arrêts' : 'Durée Moyenne (min)',
          font: {
            size: 12,
            weight: 'bold'
          }
        },
        grid: {
          color: viewMode === 'count' ? 'rgba(54, 162, 235, 0.1)' : 'rgba(255, 99, 132, 0.1)'
        },
        ticks: {
          beginAtZero: true,
          font: {
            size: 11
          }
        }
      };
    }

    return baseOptions;
  };

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100%',
        background: 'linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)',
        borderRadius: '12px'
      }}>
        <Spin size="large" tip="Chargement des motifs temporels..." />
      </div>
    );
  }

  if (!chartData.labels || chartData.labels.length === 0) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100%',
        background: 'linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)',
        borderRadius: '12px'
      }}>
        <Empty 
          description="Aucune donnée temporelle disponible"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </div>
    );
  }
  return (
    <div style={{ height: '100%', width: '100%' }}>
      {/* View Mode Toggle */}
      <Row style={{ marginBottom: '12px' }}>
        <Col span={24}>
          <Space size="small" style={{ width: '100%', justifyContent: 'center' }}>
            <Button
              type={viewMode === 'count' ? 'primary' : 'default'}
              icon={<LineChartOutlined />}
              onClick={() => setViewMode('count')}
              size="small"
            >
              Arrêts
            </Button>
            <Button
              type={viewMode === 'duration' ? 'primary' : 'default'}
              icon={<ClockCircleOutlined />}
              onClick={() => setViewMode('duration')}
              size="small"
            >
              Durée
            </Button>
            <Button
              type={viewMode === 'both' ? 'primary' : 'default'}
              onClick={() => setViewMode('both')}
              size="small"
            >
              Les deux
            </Button>
          </Space>
        </Col>
      </Row>

      {/* Chart */}
      <div style={{ 
        height: 'calc(100% - 70px)',
        background: 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',
        borderRadius: '12px',
        padding: '16px'
      }}>
        <Line 
          ref={chartRef} 
          data={{
            labels: chartData.labels,
            datasets: getDatasets()
          }} 
          options={getOptions()} 
        />
      </div>
    </div>
  );
};

export default ArretTimePatternChart;
