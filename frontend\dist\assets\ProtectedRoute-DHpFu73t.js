import{A as l,R as e,ag as c,bh as a,Z as u}from"./index-gs31pxOi.js";const s=({allowedRoles:r})=>{const{isAuthenticated:n,user:t,loading:i}=l();return i?e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh"}},e.createElement(c,{size:"large",tip:"Vérification de l'authentification..."})):n?r&&(!(t!=null&&t.role)||!r.includes(t.role))?e.createElement(a,{to:"/unauthorized",replace:!0}):e.createElement(u,null):e.createElement(a,{to:"/login",replace:!0})};export{s as default};
