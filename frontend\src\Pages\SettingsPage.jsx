import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Switch,
  Select,
  Slider,
  Input,
  InputNumber,
  Button,
  Space,
  Typography,
  Divider,
  Alert,
  Spin,
  notification,
  Row,
  Col,
  Tag,
  Tooltip
} from 'antd';
import {
  SettingOutlined,
  EyeOutlined,
  ReloadOutlined,
  SaveOutlined,
  BulbOutlined,
  TableOutlined,
  BarChartOutlined,
  BellOutlined,
  MailOutlined,
  FileTextOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import { useSettings } from '../hooks/useSettings';
import { useTheme } from '../theme-context';
import SettingsPreview from '../Components/SettingsPreview';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

/**
 * Comprehensive Settings Page with Real-time Preview
 * Provides immediate visual feedback for all setting changes
 */
function SettingsPage() {
  // Use MainLayout theme system for consistency
  const { darkMode, toggleDarkMode } = useTheme();

  const {
    settings,
    loading,
    error,
    updating,
    updateError,
    updateSetting,
    resetSettings,
    theme: settingsTheme,
    tables,
    charts,
    refresh,
    notifications,
    email,
    reports,
    performance
  } = useSettings();

  const [activeTab, setActiveTab] = useState('theme');
  const [previewMode, setPreviewMode] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Show notification for setting changes
  const showSettingChangeNotification = (settingName, value) => {
    notification.success({
      message: 'Setting Updated',
      description: `${settingName} has been ${typeof value === 'boolean' ? (value ? 'enabled' : 'disabled') : `set to ${value}`}`,
      duration: 2,
      placement: 'topRight'
    });
  };

  // Handle setting change with immediate effect
  const handleSettingChange = (path, value, settingName) => {
    updateSetting(path, value);
    showSettingChangeNotification(settingName, value);
    setHasUnsavedChanges(true);
  };

  // Reset all settings
  const handleResetSettings = async () => {
    try {
      await resetSettings();
      notification.success({
        message: 'Settings Reset',
        description: 'All settings have been reset to their default values',
        duration: 3
      });
      setHasUnsavedChanges(false);
    } catch (error) {
      notification.error({
        message: 'Reset Failed',
        description: error.message,
        duration: 5
      });
    }
  };

  // Loading state
  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '400px' 
      }}>
        <Spin size="large" tip="Loading settings..." />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <Alert
        message="Settings Load Error"
        description={error}
        type="error"
        showIcon
        style={{ margin: '20px' }}
      />
    );
  }

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      {/* Header */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <SettingOutlined /> Settings
        </Title>
        <Paragraph>
          Customize your experience with real-time preview. Changes are applied immediately.
        </Paragraph>
        
        {/* Action Bar */}
        <Space style={{ marginBottom: '16px' }}>
          <Button
            type="primary"
            icon={<EyeOutlined />}
            onClick={() => setPreviewMode(!previewMode)}
          >
            {previewMode ? 'Exit Preview' : 'Preview Mode'}
          </Button>
          
          <Button
            icon={<ReloadOutlined />}
            onClick={handleResetSettings}
            loading={updating}
            danger
          >
            Reset to Defaults
          </Button>
          
          {hasUnsavedChanges && (
            <Tag color="orange">
              <SaveOutlined /> Changes Applied
            </Tag>
          )}
        </Space>

        {/* Update Error Alert */}
        {updateError && (
          <Alert
            message="Update Error"
            description={updateError}
            type="error"
            closable
            style={{ marginBottom: '16px' }}
          />
        )}
      </div>

      {/* Settings Tabs */}
      <Tabs 
        activeKey={activeTab} 
        onChange={setActiveTab}
        type="card"
        size="large"
      >
        {/* Theme & Display Settings */}
        <TabPane 
          tab={<span><BulbOutlined /> Theme & Display</span>} 
          key="theme"
        >
          <Card title="Visual Appearance" style={{ marginBottom: '16px' }}>
            <Row gutter={[24, 16]}>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Dark Mode</Text>
                  <br />
                  <Text type="secondary">Switch between light and dark themes</Text>
                  <br />
                  <Switch
                    checked={darkMode}
                    onChange={(value) => {
                      toggleDarkMode();
                      showSettingChangeNotification('Dark Mode', value);
                    }}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>
              
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Compact Mode</Text>
                  <br />
                  <Text type="secondary">Reduce spacing for more content</Text>
                  <br />
                  <Switch
                    checked={settingsTheme.compactMode}
                    onChange={(value) => handleSettingChange('theme.compactMode', value, 'Compact Mode')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>
              
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Animations</Text>
                  <br />
                  <Text type="secondary">Enable smooth transitions and animations</Text>
                  <br />
                  <Switch
                    checked={settingsTheme.animationsEnabled}
                    onChange={(value) => handleSettingChange('theme.animationsEnabled', value, 'Animations')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>
              
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Chart Animations</Text>
                  <br />
                  <Text type="secondary">Animate chart transitions and updates</Text>
                  <br />
                  <Switch
                    checked={settingsTheme.chartAnimations}
                    onChange={(value) => handleSettingChange('theme.chartAnimations', value, 'Chart Animations')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>
            </Row>
          </Card>
        </TabPane>

        {/* Table Settings */}
        <TabPane 
          tab={<span><TableOutlined /> Tables</span>} 
          key="tables"
        >
          <Card title="Data Display Settings" style={{ marginBottom: '16px' }}>
            <Row gutter={[24, 16]}>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Default Page Size</Text>
                  <br />
                  <Text type="secondary">Number of rows per page</Text>
                  <br />
                  <Select
                    value={tables.defaultPageSize}
                    onChange={(value) => handleSettingChange('tables.defaultPageSize', value, 'Page Size')}
                    style={{ width: '100%', marginTop: '8px' }}
                  >
                    {tables.pageSizeOptions.map(size => (
                      <Option key={size} value={size}>{size} rows</Option>
                    ))}
                  </Select>
                </div>
              </Col>
              
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Virtualization Threshold</Text>
                  <br />
                  <Text type="secondary">Enable virtualization for large datasets</Text>
                  <br />
                  <InputNumber
                    value={tables.virtualizationThreshold}
                    onChange={(value) => handleSettingChange('tables.virtualizationThreshold', value, 'Virtualization Threshold')}
                    min={50}
                    max={1000}
                    step={50}
                    style={{ width: '100%', marginTop: '8px' }}
                    addonAfter="rows"
                  />
                </div>
              </Col>
              
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Quick Jumper</Text>
                  <br />
                  <Text type="secondary">Show page jump input</Text>
                  <br />
                  <Switch
                    checked={tables.showQuickJumper}
                    onChange={(value) => handleSettingChange('tables.showQuickJumper', value, 'Quick Jumper')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>
            </Row>
          </Card>
        </TabPane>

        {/* Chart Settings */}
        <TabPane 
          tab={<span><BarChartOutlined /> Charts</span>} 
          key="charts"
        >
          <Card title="Chart Configuration" style={{ marginBottom: '16px' }}>
            <Row gutter={[24, 16]}>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Default Chart Type</Text>
                  <br />
                  <Text type="secondary">Default visualization type</Text>
                  <br />
                  <Select
                    value={charts.defaultType}
                    onChange={(value) => handleSettingChange('charts.defaultType', value, 'Chart Type')}
                    style={{ width: '100%', marginTop: '8px' }}
                  >
                    <Option value="bar">Bar Chart</Option>
                    <Option value="line">Line Chart</Option>
                    <Option value="pie">Pie Chart</Option>
                    <Option value="area">Area Chart</Option>
                  </Select>
                </div>
              </Col>
              
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Color Scheme</Text>
                  <br />
                  <Text type="secondary">Chart color palette</Text>
                  <br />
                  <Select
                    value={charts.colorScheme}
                    onChange={(value) => handleSettingChange('charts.colorScheme', value, 'Color Scheme')}
                    style={{ width: '100%', marginTop: '8px' }}
                  >
                    <Option value="somipem">SOMIPEM</Option>
                    <Option value="blue">Blue</Option>
                    <Option value="green">Green</Option>
                    <Option value="red">Red</Option>
                  </Select>
                </div>
              </Col>
              
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Show Legend</Text>
                  <br />
                  <Text type="secondary">Display chart legends</Text>
                  <br />
                  <Switch
                    checked={charts.showLegend}
                    onChange={(value) => handleSettingChange('charts.showLegend', value, 'Chart Legend')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>
              
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Performance Mode</Text>
                  <br />
                  <Text type="secondary">Optimize for large datasets</Text>
                  <br />
                  <Switch
                    checked={charts.performanceMode}
                    onChange={(value) => handleSettingChange('charts.performanceMode', value, 'Performance Mode')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>
            </Row>
          </Card>

          {/* Chart Layout & Size Settings */}
          <Card title="Chart Layout & Size" style={{ marginBottom: '16px' }}>
            <Row gutter={[24, 16]}>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Default Chart Height</Text>
                  <br />
                  <Text type="secondary">Default height for charts (pixels)</Text>
                  <br />
                  <InputNumber
                    value={charts.layout?.defaultHeight || 300}
                    onChange={(value) => handleSettingChange('charts.layout.defaultHeight', value, 'Chart Height')}
                    min={200}
                    max={800}
                    step={50}
                    style={{ width: '100%', marginTop: '8px' }}
                    addonAfter="px"
                  />
                </div>
              </Col>

              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Compact Chart Mode</Text>
                  <br />
                  <Text type="secondary">Smaller charts for more data density</Text>
                  <br />
                  <Switch
                    checked={charts.layout?.compactMode || false}
                    onChange={(value) => handleSettingChange('charts.layout.compactMode', value, 'Compact Chart Mode')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>

              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Chart Aspect Ratio</Text>
                  <br />
                  <Text type="secondary">Preferred aspect ratio for charts</Text>
                  <br />
                  <Select
                    value={charts.layout?.aspectRatio || 'auto'}
                    onChange={(value) => handleSettingChange('charts.layout.aspectRatio', value, 'Chart Aspect Ratio')}
                    style={{ width: '100%', marginTop: '8px' }}
                  >
                    <Option value="auto">Auto</Option>
                    <Option value="16:9">16:9 (Widescreen)</Option>
                    <Option value="4:3">4:3 (Standard)</Option>
                    <Option value="1:1">1:1 (Square)</Option>
                  </Select>
                </div>
              </Col>

              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Chart Margins</Text>
                  <br />
                  <Text type="secondary">Spacing around charts</Text>
                  <br />
                  <Select
                    value={charts.layout?.marginSize || 'standard'}
                    onChange={(value) => handleSettingChange('charts.layout.marginSize', value, 'Chart Margins')}
                    style={{ width: '100%', marginTop: '8px' }}
                  >
                    <Option value="compact">Compact</Option>
                    <Option value="standard">Standard</Option>
                    <Option value="spacious">Spacious</Option>
                  </Select>
                </div>
              </Col>
            </Row>
          </Card>

          {/* Data Display Options */}
          <Card title="Data Display Options" style={{ marginBottom: '16px' }}>
            <Row gutter={[24, 16]}>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Show Data Labels</Text>
                  <br />
                  <Text type="secondary">Display values on chart elements</Text>
                  <br />
                  <Switch
                    checked={charts.dataDisplay?.showDataLabels || false}
                    onChange={(value) => handleSettingChange('charts.dataDisplay.showDataLabels', value, 'Show Data Labels')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>

              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Show Grid Lines</Text>
                  <br />
                  <Text type="secondary">Display chart grid lines</Text>
                  <br />
                  <Switch
                    checked={charts.dataDisplay?.showGridLines !== false}
                    onChange={(value) => handleSettingChange('charts.dataDisplay.showGridLines', value, 'Show Grid Lines')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>

              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Show Data Points</Text>
                  <br />
                  <Text type="secondary">Show individual points on line charts</Text>
                  <br />
                  <Switch
                    checked={charts.dataDisplay?.showDataPoints !== false}
                    onChange={(value) => handleSettingChange('charts.dataDisplay.showDataPoints', value, 'Show Data Points')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>

              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Zero-Based Y-Axis</Text>
                  <br />
                  <Text type="secondary">Start Y-axis from zero</Text>
                  <br />
                  <Switch
                    checked={charts.dataDisplay?.zeroBasedAxis !== false}
                    onChange={(value) => handleSettingChange('charts.dataDisplay.zeroBasedAxis', value, 'Zero-Based Y-Axis')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>
            </Row>
          </Card>

          {/* Chart Interaction Settings */}
          <Card title="Chart Interaction" style={{ marginBottom: '16px' }}>
            <Row gutter={[24, 16]}>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Hover Effects</Text>
                  <br />
                  <Text type="secondary">Highlight elements on hover</Text>
                  <br />
                  <Switch
                    checked={charts.interactions?.hoverEffects !== false}
                    onChange={(value) => handleSettingChange('charts.interactions.hoverEffects', value, 'Hover Effects')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>

              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Click to Expand</Text>
                  <br />
                  <Text type="secondary">Click charts to open in modal</Text>
                  <br />
                  <Switch
                    checked={charts.interactions?.clickToExpand !== false}
                    onChange={(value) => handleSettingChange('charts.interactions.clickToExpand', value, 'Click to Expand')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>

              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Tooltip Style</Text>
                  <br />
                  <Text type="secondary">Chart tooltip detail level</Text>
                  <br />
                  <Select
                    value={charts.interactions?.tooltipStyle || 'standard'}
                    onChange={(value) => handleSettingChange('charts.interactions.tooltipStyle', value, 'Tooltip Style')}
                    style={{ width: '100%', marginTop: '8px' }}
                  >
                    <Option value="minimal">Minimal</Option>
                    <Option value="standard">Standard</Option>
                    <Option value="detailed">Detailed</Option>
                  </Select>
                </div>
              </Col>
            </Row>
          </Card>
        </TabPane>

        {/* Refresh Settings */}
        <TabPane
          tab={<span><ReloadOutlined /> Refresh</span>}
          key="refresh"
        >
          <Card title="Auto-Refresh Configuration" style={{ marginBottom: '16px' }}>
            <Row gutter={[24, 16]}>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Auto Refresh</Text>
                  <br />
                  <Text type="secondary">Automatically refresh data</Text>
                  <br />
                  <Switch
                    checked={refresh.autoRefreshEnabled}
                    onChange={(value) => handleSettingChange('refresh.autoRefreshEnabled', value, 'Auto Refresh')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>

              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Background Refresh</Text>
                  <br />
                  <Text type="secondary">Refresh data when tab is not active</Text>
                  <br />
                  <Switch
                    checked={refresh.backgroundRefresh}
                    onChange={(value) => handleSettingChange('refresh.backgroundRefresh', value, 'Background Refresh')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>

              <Col span={24}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Dashboard Refresh Interval</Text>
                  <br />
                  <Text type="secondary">How often to refresh dashboard data (seconds)</Text>
                  <br />
                  <Slider
                    value={refresh.dashboardInterval}
                    onChange={(value) => handleSettingChange('refresh.dashboardInterval', value, 'Dashboard Interval')}
                    min={30}
                    max={3600}
                    step={30}
                    marks={{
                      30: '30s',
                      300: '5m',
                      900: '15m',
                      1800: '30m',
                      3600: '1h'
                    }}
                    style={{ marginTop: '16px' }}
                  />
                </div>
              </Col>

              <Col span={24}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Real-time Data Interval</Text>
                  <br />
                  <Text type="secondary">How often to refresh real-time data (seconds)</Text>
                  <br />
                  <Slider
                    value={refresh.realtimeInterval}
                    onChange={(value) => handleSettingChange('refresh.realtimeInterval', value, 'Real-time Interval')}
                    min={10}
                    max={600}
                    step={10}
                    marks={{
                      10: '10s',
                      60: '1m',
                      300: '5m',
                      600: '10m'
                    }}
                    style={{ marginTop: '16px' }}
                  />
                </div>
              </Col>
            </Row>
          </Card>
        </TabPane>

        {/* Notification Settings */}
        <TabPane
          tab={<span><BellOutlined /> Notifications</span>}
          key="notifications"
        >
          <Card title="Notification Preferences" style={{ marginBottom: '16px' }}>
            <Row gutter={[24, 16]}>
              <Col span={24}>
                <Text strong>Notification Categories</Text>
                <br />
                <Text type="secondary">Choose which types of notifications to receive</Text>
                <br />
                <div style={{ marginTop: '12px' }}>
                  {Object.entries(notifications.categories).map(([category, enabled]) => (
                    <div key={category} style={{ marginBottom: '8px' }}>
                      <Switch
                        checked={enabled}
                        onChange={(value) => handleSettingChange(`notifications.categories.${category}`, value, `${category} notifications`)}
                        style={{ marginRight: '8px' }}
                      />
                      <Text>{category.replace('_', ' ').toUpperCase()}</Text>
                    </div>
                  ))}
                </div>
              </Col>

              <Col span={24}>
                <Divider />
                <Text strong>Priority Levels</Text>
                <br />
                <Text type="secondary">Choose which priority levels to show</Text>
                <br />
                <div style={{ marginTop: '12px' }}>
                  {Object.entries(notifications.priorities).map(([priority, enabled]) => (
                    <div key={priority} style={{ marginBottom: '8px' }}>
                      <Switch
                        checked={enabled}
                        onChange={(value) => handleSettingChange(`notifications.priorities.${priority}`, value, `${priority} priority notifications`)}
                        style={{ marginRight: '8px' }}
                      />
                      <Text>{priority.toUpperCase()}</Text>
                    </div>
                  ))}
                </div>
              </Col>

              <Col span={24}>
                <Divider />
                <Text strong>Notification Behavior</Text>
                <br />
                <Row gutter={[16, 16]} style={{ marginTop: '12px' }}>
                  <Col span={8}>
                    <Text>Sound Notifications</Text>
                    <br />
                    <Switch
                      checked={notifications.behavior.sound}
                      onChange={(value) => handleSettingChange('notifications.behavior.sound', value, 'Sound Notifications')}
                    />
                  </Col>

                  <Col span={8}>
                    <Text>Auto Close</Text>
                    <br />
                    <Switch
                      checked={notifications.behavior.autoClose}
                      onChange={(value) => handleSettingChange('notifications.behavior.autoClose', value, 'Auto Close')}
                    />
                  </Col>

                  <Col span={8}>
                    <Text>Max Visible</Text>
                    <br />
                    <InputNumber
                      value={notifications.behavior.maxVisible}
                      onChange={(value) => handleSettingChange('notifications.behavior.maxVisible', value, 'Max Visible Notifications')}
                      min={1}
                      max={20}
                      style={{ width: '100%' }}
                    />
                  </Col>
                </Row>
              </Col>
            </Row>
          </Card>
        </TabPane>

        {/* Email Settings */}
        <TabPane
          tab={<span><MailOutlined /> Email</span>}
          key="email"
        >
          <Card title="Email Notification Settings" style={{ marginBottom: '16px' }}>
            <Row gutter={[24, 16]}>
              <Col span={24}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Enable Email Notifications</Text>
                  <br />
                  <Text type="secondary">Receive notifications via email</Text>
                  <br />
                  <Switch
                    checked={email.enabled}
                    onChange={(value) => handleSettingChange('email.enabled', value, 'Email Notifications')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>

              {email.enabled && (
                <>
                  <Col span={12}>
                    <div style={{ marginBottom: '16px' }}>
                      <Text strong>Email Frequency</Text>
                      <br />
                      <Text type="secondary">How often to send email notifications</Text>
                      <br />
                      <Select
                        value={email.frequency}
                        onChange={(value) => handleSettingChange('email.frequency', value, 'Email Frequency')}
                        style={{ width: '100%', marginTop: '8px' }}
                      >
                        <Option value="immediate">Immediate</Option>
                        <Option value="hourly_batch">Hourly Batch</Option>
                        <Option value="daily_digest">Daily Digest</Option>
                      </Select>
                    </div>
                  </Col>

                  <Col span={12}>
                    <div style={{ marginBottom: '16px' }}>
                      <Text strong>Email Template</Text>
                      <br />
                      <Text type="secondary">Choose email template style</Text>
                      <br />
                      <Select
                        value={email.template}
                        onChange={(value) => handleSettingChange('email.template', value, 'Email Template')}
                        style={{ width: '100%', marginTop: '8px' }}
                      >
                        <Option value="minimal">Minimal</Option>
                        <Option value="standard">Standard</Option>
                        <Option value="detailed">Detailed</Option>
                      </Select>
                    </div>
                  </Col>

                  <Col span={24}>
                    <Divider />
                    <Text strong>Email Notification Categories</Text>
                    <br />
                    <Text type="secondary">Choose which categories to receive via email</Text>
                    <br />
                    <div style={{ marginTop: '12px' }}>
                      {email.notifications && email.notifications.categories && Object.entries(email.notifications.categories).map(([category, enabled]) => (
                        <div key={category} style={{ marginBottom: '8px' }}>
                          <Switch
                            checked={enabled}
                            onChange={(value) => handleSettingChange(`email.notifications.categories.${category}`, value, `Email ${category} notifications`)}
                            style={{ marginRight: '8px' }}
                          />
                          <Text>{category.replace('_', ' ').toUpperCase()}</Text>
                        </div>
                      ))}
                    </div>
                  </Col>

                  <Col span={24}>
                    <Divider />
                    <Text strong>Email Priority Levels</Text>
                    <br />
                    <Text type="secondary">Choose which priority levels to receive via email</Text>
                    <br />
                    <div style={{ marginTop: '12px' }}>
                      {email.notifications && email.notifications.priorities && Object.entries(email.notifications.priorities).map(([priority, enabled]) => (
                        <div key={priority} style={{ marginBottom: '8px' }}>
                          <Switch
                            checked={enabled}
                            onChange={(value) => handleSettingChange(`email.notifications.priorities.${priority}`, value, `Email ${priority} priority notifications`)}
                            style={{ marginRight: '8px' }}
                          />
                          <Text>{priority.toUpperCase()}</Text>
                        </div>
                      ))}
                    </div>
                  </Col>

                  {email.quietHours && (
                    <Col span={24}>
                      <Divider />
                      <Text strong>Quiet Hours</Text>
                      <br />
                      <Text type="secondary">Disable email notifications during specific hours</Text>
                      <br />
                      <Row gutter={[16, 16]} style={{ marginTop: '12px' }}>
                        <Col span={8}>
                          <Text>Enable Quiet Hours</Text>
                          <br />
                          <Switch
                            checked={email.quietHours.enabled}
                            onChange={(value) => handleSettingChange('email.quietHours.enabled', value, 'Quiet Hours')}
                          />
                        </Col>

                        {email.quietHours.enabled && (
                          <>
                            <Col span={8}>
                              <Text>Start Time</Text>
                              <br />
                              <Select
                                value={email.quietHours.start}
                                onChange={(value) => handleSettingChange('email.quietHours.start', value, 'Quiet Hours Start')}
                                style={{ width: '100%' }}
                              >
                                {Array.from({ length: 24 }, (_, i) => (
                                  <Option key={i} value={`${i.toString().padStart(2, '0')}:00`}>
                                    {`${i.toString().padStart(2, '0')}:00`}
                                  </Option>
                                ))}
                              </Select>
                            </Col>

                            <Col span={8}>
                              <Text>End Time</Text>
                              <br />
                              <Select
                                value={email.quietHours.end}
                                onChange={(value) => handleSettingChange('email.quietHours.end', value, 'Quiet Hours End')}
                                style={{ width: '100%' }}
                              >
                                {Array.from({ length: 24 }, (_, i) => (
                                  <Option key={i} value={`${i.toString().padStart(2, '0')}:00`}>
                                    {`${i.toString().padStart(2, '0')}:00`}
                                  </Option>
                                ))}
                              </Select>
                            </Col>
                          </>
                        )}
                      </Row>
                    </Col>
                  )}

                  {email.batchSettings && (
                    <Col span={24}>
                      <Divider />
                      <Text strong>Batch Settings</Text>
                      <br />
                      <Text type="secondary">Configure email batching and digest options</Text>
                      <br />
                      <Row gutter={[16, 16]} style={{ marginTop: '12px' }}>
                        <Col span={12}>
                          <Text>Hourly Batch</Text>
                          <br />
                          <Switch
                            checked={email.batchSettings.hourlyBatch?.enabled}
                            onChange={(value) => handleSettingChange('email.batchSettings.hourlyBatch.enabled', value, 'Hourly Batch')}
                          />
                          {email.batchSettings.hourlyBatch?.enabled && (
                            <div style={{ marginTop: '8px' }}>
                              <Text>Max Notifications per Hour</Text>
                              <br />
                              <InputNumber
                                value={email.batchSettings.hourlyBatch.maxNotifications}
                                onChange={(value) => handleSettingChange('email.batchSettings.hourlyBatch.maxNotifications', value, 'Max Hourly Notifications')}
                                min={1}
                                max={100}
                                style={{ width: '100%' }}
                              />
                            </div>
                          )}
                        </Col>

                        <Col span={12}>
                          <Text>Daily Digest</Text>
                          <br />
                          <Switch
                            checked={email.batchSettings.dailyDigest?.enabled}
                            onChange={(value) => handleSettingChange('email.batchSettings.dailyDigest.enabled', value, 'Daily Digest')}
                          />
                          {email.batchSettings.dailyDigest?.enabled && (
                            <div style={{ marginTop: '8px' }}>
                              <Text>Digest Time</Text>
                              <br />
                              <Select
                                value={email.batchSettings.dailyDigest.time}
                                onChange={(value) => handleSettingChange('email.batchSettings.dailyDigest.time', value, 'Daily Digest Time')}
                                style={{ width: '100%' }}
                              >
                                {Array.from({ length: 24 }, (_, i) => (
                                  <Option key={i} value={`${i.toString().padStart(2, '0')}:00`}>
                                    {`${i.toString().padStart(2, '0')}:00`}
                                  </Option>
                                ))}
                              </Select>
                            </div>
                          )}
                        </Col>
                      </Row>
                    </Col>
                  )}
                </>
              )}
            </Row>
          </Card>

          {/* Advanced Email Settings */}
          <Card title="Advanced Email Settings" style={{ marginBottom: '16px' }}>
            <Row gutter={[24, 16]}>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Email Format</Text>
                  <br />
                  <Text type="secondary">Email content format preference</Text>
                  <br />
                  <Select
                    value={email.format || 'html'}
                    onChange={(value) => handleSettingChange('email.format', value, 'Email Format')}
                    style={{ width: '100%', marginTop: '8px' }}
                  >
                    <Option value="html">HTML (Rich formatting)</Option>
                    <Option value="text">Plain Text</Option>
                    <Option value="both">Both HTML and Text</Option>
                  </Select>
                </div>
              </Col>

              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Email Language</Text>
                  <br />
                  <Text type="secondary">Language for email notifications</Text>
                  <br />
                  <Select
                    value={email.language || 'fr'}
                    onChange={(value) => handleSettingChange('email.language', value, 'Email Language')}
                    style={{ width: '100%', marginTop: '8px' }}
                  >
                    <Option value="fr">Français</Option>
                    <Option value="en">English</Option>
                    <Option value="es">Español</Option>
                  </Select>
                </div>
              </Col>

              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Include Attachments</Text>
                  <br />
                  <Text type="secondary">Attach relevant files to emails</Text>
                  <br />
                  <Switch
                    checked={email.attachments?.enabled || false}
                    onChange={(value) => handleSettingChange('email.attachments.enabled', value, 'Include Attachments')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>

              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Email Signature</Text>
                  <br />
                  <Text type="secondary">Include custom signature</Text>
                  <br />
                  <Switch
                    checked={email.signature?.enabled || false}
                    onChange={(value) => handleSettingChange('email.signature.enabled', value, 'Email Signature')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>

              {email.signature?.enabled && (
                <Col span={24}>
                  <div style={{ marginBottom: '16px' }}>
                    <Text strong>Custom Signature</Text>
                    <br />
                    <Text type="secondary">Your custom email signature</Text>
                    <br />
                    <Input.TextArea
                      value={email.signature?.text || ''}
                      onChange={(e) => handleSettingChange('email.signature.text', e.target.value, 'Email Signature Text')}
                      placeholder="Enter your custom email signature..."
                      rows={3}
                      style={{ marginTop: '8px' }}
                    />
                  </div>
                </Col>
              )}
            </Row>
          </Card>

          {/* Email Filtering & Rules */}
          <Card title="Email Filtering & Rules" style={{ marginBottom: '16px' }}>
            <Row gutter={[24, 16]}>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Minimum Priority Level</Text>
                  <br />
                  <Text type="secondary">Only send emails for this priority and above</Text>
                  <br />
                  <Select
                    value={email.filtering?.minPriority || 'low'}
                    onChange={(value) => handleSettingChange('email.filtering.minPriority', value, 'Minimum Priority Level')}
                    style={{ width: '100%', marginTop: '8px' }}
                  >
                    <Option value="low">Low and above</Option>
                    <Option value="medium">Medium and above</Option>
                    <Option value="high">High and above</Option>
                    <Option value="critical">Critical only</Option>
                  </Select>
                </div>
              </Col>

              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Maximum Emails per Day</Text>
                  <br />
                  <Text type="secondary">Limit daily email notifications</Text>
                  <br />
                  <InputNumber
                    value={email.filtering?.maxPerDay || 50}
                    onChange={(value) => handleSettingChange('email.filtering.maxPerDay', value, 'Max Emails per Day')}
                    min={1}
                    max={200}
                    style={{ width: '100%', marginTop: '8px' }}
                    addonAfter="emails"
                  />
                </div>
              </Col>

              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Duplicate Prevention</Text>
                  <br />
                  <Text type="secondary">Prevent duplicate notifications</Text>
                  <br />
                  <Switch
                    checked={email.filtering?.preventDuplicates !== false}
                    onChange={(value) => handleSettingChange('email.filtering.preventDuplicates', value, 'Duplicate Prevention')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>

              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Smart Grouping</Text>
                  <br />
                  <Text type="secondary">Group similar notifications together</Text>
                  <br />
                  <Switch
                    checked={email.filtering?.smartGrouping || false}
                    onChange={(value) => handleSettingChange('email.filtering.smartGrouping', value, 'Smart Grouping')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>
            </Row>
          </Card>

          {/* Email Delivery Options */}
          <Card title="Email Delivery Options" style={{ marginBottom: '16px' }}>
            <Row gutter={[24, 16]}>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Delivery Method</Text>
                  <br />
                  <Text type="secondary">How to deliver email notifications</Text>
                  <br />
                  <Select
                    value={email.delivery?.method || 'immediate'}
                    onChange={(value) => handleSettingChange('email.delivery.method', value, 'Delivery Method')}
                    style={{ width: '100%', marginTop: '8px' }}
                  >
                    <Option value="immediate">Immediate</Option>
                    <Option value="scheduled">Scheduled</Option>
                    <Option value="batch">Batch Processing</Option>
                  </Select>
                </div>
              </Col>

              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Retry Failed Emails</Text>
                  <br />
                  <Text type="secondary">Retry sending failed emails</Text>
                  <br />
                  <Switch
                    checked={email.delivery?.retryFailed !== false}
                    onChange={(value) => handleSettingChange('email.delivery.retryFailed', value, 'Retry Failed Emails')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>

              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Read Receipts</Text>
                  <br />
                  <Text type="secondary">Request read receipts for emails</Text>
                  <br />
                  <Switch
                    checked={email.delivery?.readReceipts || false}
                    onChange={(value) => handleSettingChange('email.delivery.readReceipts', value, 'Read Receipts')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>

              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Email Tracking</Text>
                  <br />
                  <Text type="secondary">Track email opens and clicks</Text>
                  <br />
                  <Switch
                    checked={email.delivery?.tracking || false}
                    onChange={(value) => handleSettingChange('email.delivery.tracking', value, 'Email Tracking')}
                    style={{ marginTop: '8px' }}

                  />
                </div>
              </Col>
            </Row>
          </Card>
        </TabPane>

        {/* Reports Settings */}
        <TabPane
          tab={<span><FileTextOutlined /> Reports</span>}
          key="reports"
        >
          <Card title="Report Generation Settings" style={{ marginBottom: '16px' }}>
            <Row gutter={[24, 16]}>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Auto Generate Reports</Text>
                  <br />
                  <Text type="secondary">Automatically generate reports</Text>
                  <br />
                  <Switch
                    checked={reports.generation?.autoGenerate}
                    onChange={(value) => handleSettingChange('reports.generation.autoGenerate', value, 'Auto Generate Reports')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>

              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Report Format</Text>
                  <br />
                  <Text type="secondary">Default format for generated reports</Text>
                  <br />
                  <Select
                    value={reports.generation?.format}
                    onChange={(value) => handleSettingChange('reports.generation.format', value, 'Report Format')}
                    style={{ width: '100%', marginTop: '8px' }}
                  >
                    <Option value="pdf">PDF</Option>
                    <Option value="html">HTML</Option>
                    <Option value="excel">Excel</Option>
                  </Select>
                </div>
              </Col>

              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Report Quality</Text>
                  <br />
                  <Text type="secondary">Quality level for generated reports</Text>
                  <br />
                  <Select
                    value={reports.generation?.quality}
                    onChange={(value) => handleSettingChange('reports.generation.quality', value, 'Report Quality')}
                    style={{ width: '100%', marginTop: '8px' }}
                  >
                    <Option value="low">Low (Fast)</Option>
                    <Option value="standard">Standard</Option>
                    <Option value="high">High (Slow)</Option>
                  </Select>
                </div>
              </Col>

              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Include Charts</Text>
                  <br />
                  <Text type="secondary">Include charts in reports</Text>
                  <br />
                  <Switch
                    checked={reports.generation?.includeCharts}
                    onChange={(value) => handleSettingChange('reports.generation.includeCharts', value, 'Include Charts')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>

              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Include Tables</Text>
                  <br />
                  <Text type="secondary">Include data tables in reports</Text>
                  <br />
                  <Switch
                    checked={reports.generation?.includeTables}
                    onChange={(value) => handleSettingChange('reports.generation.includeTables', value, 'Include Tables')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>
            </Row>
          </Card>

          {reports.schedules && (
            <Card title="Report Scheduling" style={{ marginBottom: '16px' }}>
              <Row gutter={[24, 16]}>
                <Col span={8}>
                  <Text strong>Daily Reports</Text>
                  <br />
                  <Switch
                    checked={reports.schedules.daily?.enabled}
                    onChange={(value) => handleSettingChange('reports.schedules.daily.enabled', value, 'Daily Reports')}
                  />
                  {reports.schedules.daily?.enabled && (
                    <div style={{ marginTop: '8px' }}>
                      <Text>Time</Text>
                      <br />
                      <Select
                        value={reports.schedules.daily.time}
                        onChange={(value) => handleSettingChange('reports.schedules.daily.time', value, 'Daily Report Time')}
                        style={{ width: '100%' }}
                      >
                        {Array.from({ length: 24 }, (_, i) => (
                          <Option key={i} value={`${i.toString().padStart(2, '0')}:00`}>
                            {`${i.toString().padStart(2, '0')}:00`}
                          </Option>
                        ))}
                      </Select>
                    </div>
                  )}
                </Col>

                <Col span={8}>
                  <Text strong>Weekly Reports</Text>
                  <br />
                  <Switch
                    checked={reports.schedules.weekly?.enabled}
                    onChange={(value) => handleSettingChange('reports.schedules.weekly.enabled', value, 'Weekly Reports')}
                  />
                  {reports.schedules.weekly?.enabled && (
                    <div style={{ marginTop: '8px' }}>
                      <Text>Day</Text>
                      <br />
                      <Select
                        value={reports.schedules.weekly.day}
                        onChange={(value) => handleSettingChange('reports.schedules.weekly.day', value, 'Weekly Report Day')}
                        style={{ width: '100%' }}
                      >
                        <Option value="monday">Monday</Option>
                        <Option value="tuesday">Tuesday</Option>
                        <Option value="wednesday">Wednesday</Option>
                        <Option value="thursday">Thursday</Option>
                        <Option value="friday">Friday</Option>
                        <Option value="saturday">Saturday</Option>
                        <Option value="sunday">Sunday</Option>
                      </Select>
                    </div>
                  )}
                </Col>

                <Col span={8}>
                  <Text strong>Monthly Reports</Text>
                  <br />
                  <Switch
                    checked={reports.schedules.monthly?.enabled}
                    onChange={(value) => handleSettingChange('reports.schedules.monthly.enabled', value, 'Monthly Reports')}
                  />
                  {reports.schedules.monthly?.enabled && (
                    <div style={{ marginTop: '8px' }}>
                      <Text>Day of Month</Text>
                      <br />
                      <InputNumber
                        value={reports.schedules.monthly.day}
                        onChange={(value) => handleSettingChange('reports.schedules.monthly.day', value, 'Monthly Report Day')}
                        min={1}
                        max={31}
                        style={{ width: '100%' }}
                      />
                    </div>
                  )}
                </Col>
              </Row>
            </Card>
          )}
        </TabPane>

        {/* Performance Settings */}
        <TabPane
          tab={<span><ThunderboltOutlined /> Performance</span>}
          key="performance"
        >
          <Card title="Performance Optimization" style={{ marginBottom: '16px' }}>
            <Row gutter={[24, 16]}>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Enable Caching</Text>
                  <br />
                  <Text type="secondary">Cache data to improve performance</Text>
                  <br />
                  <Switch
                    checked={performance.caching.enabled}
                    onChange={(value) => handleSettingChange('performance.caching.enabled', value, 'Caching')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>

              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Cache Duration</Text>
                  <br />
                  <Text type="secondary">How long to cache data (seconds)</Text>
                  <br />
                  <InputNumber
                    value={performance.caching.duration}
                    onChange={(value) => handleSettingChange('performance.caching.duration', value, 'Cache Duration')}
                    min={60}
                    max={3600}
                    step={60}
                    style={{ width: '100%', marginTop: '8px' }}
                    addonAfter="seconds"
                  />
                </div>
              </Col>

              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Lazy Loading</Text>
                  <br />
                  <Text type="secondary">Load content as needed</Text>
                  <br />
                  <Switch
                    checked={performance.optimization.lazyLoading}
                    onChange={(value) => handleSettingChange('performance.optimization.lazyLoading', value, 'Lazy Loading')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>

              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Virtualization</Text>
                  <br />
                  <Text type="secondary">Optimize large lists and tables</Text>
                  <br />
                  <Switch
                    checked={performance.optimization.virtualization}
                    onChange={(value) => handleSettingChange('performance.optimization.virtualization', value, 'Virtualization')}
                    style={{ marginTop: '8px' }}
                  />
                </div>
              </Col>
            </Row>
          </Card>
        </TabPane>

        {/* Live Preview Tab */}
        <TabPane
          tab={<span><EyeOutlined /> Live Preview</span>}
          key="preview"
        >
          <SettingsPreview />
        </TabPane>
      </Tabs>
    </div>
  );
}

export default SettingsPage;
