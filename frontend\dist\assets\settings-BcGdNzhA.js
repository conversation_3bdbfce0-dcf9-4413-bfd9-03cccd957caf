import{r as i,aP as Tt,a as ye,bp as gt,bq as vt,e as A,w as jt,b as he,f as Vt,br as qt,aK as Ce,c as K,_ as ht,bs as zt,bt as Lt,aO as Gt,bu as je,b1 as Wt,bv as Ht,aQ as Ut,j as Kt,m as Xt,bw as Qt,bx as Yt,aq as Ve,by as Et,bz as Jt,k as _,bA as Zt,bB as en,bC as tn,bD as nn,bE as an,bF as rn,bG as ln,b7 as sn,t as on,aX as cn,bH as un,an as dn,aE as mn,bd as fn,bI as pn,bJ as st,q as ot,bK as gn,bL as vn,bM as hn,I as Xe,bN as En,ah as G,R as e,ag as bn,V as bt,ac as qe,N as Z,F as $e,ab as Y,U as Ee,aH as yt,a8 as B,a9 as N,ad as ct,W as yn,ae as Sn,al as ze,a1 as St,G as be,aa as we,ak as J,bO as Nn,v as In,aF as S,a6 as Nt,T as ge,J as xn}from"./index-gs31pxOi.js";import{S as R}from"./index-DSyC_Xzr.js";import{R as wn}from"./WarningOutlined-X6B97o_R.js";import{R as $n}from"./ClockCircleOutlined-CiulfqLg.js";import{R as Rn}from"./FileTextOutlined-C7OWCm18.js";import{R as Cn}from"./SaveOutlined-DaSDvyJi.js";var kn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"}}]},name:"up",theme:"outlined"},On=function(n,a){return i.createElement(Tt,ye({},n,{ref:a,icon:kn}))},Mn=i.forwardRef(On);function Ge(){return typeof BigInt=="function"}function It(t){return!t&&t!==0&&!Number.isNaN(t)||!String(t).trim()}function re(t){var n=t.trim(),a=n.startsWith("-");a&&(n=n.slice(1)),n=n.replace(/(\.\d*[^0])0*$/,"$1").replace(/\.0*$/,"").replace(/^0+/,""),n.startsWith(".")&&(n="0".concat(n));var r=n||"0",l=r.split("."),o=l[0]||"0",g=l[1]||"0";o==="0"&&g==="0"&&(a=!1);var f=a?"-":"";return{negative:a,negativeStr:f,trimStr:r,integerStr:o,decimalStr:g,fullStr:"".concat(f).concat(r)}}function Qe(t){var n=String(t);return!Number.isNaN(Number(n))&&n.includes("e")}function ae(t){var n=String(t);if(Qe(t)){var a=Number(n.slice(n.indexOf("e-")+2)),r=n.match(/\.(\d+)/);return r!=null&&r[1]&&(a+=r[1].length),a}return n.includes(".")&&Ye(n)?n.length-n.indexOf(".")-1:0}function ke(t){var n=String(t);if(Qe(t)){if(t>Number.MAX_SAFE_INTEGER)return String(Ge()?BigInt(t).toString():Number.MAX_SAFE_INTEGER);if(t<Number.MIN_SAFE_INTEGER)return String(Ge()?BigInt(t).toString():Number.MIN_SAFE_INTEGER);n=t.toFixed(ae(n))}return re(n).fullStr}function Ye(t){return typeof t=="number"?!Number.isNaN(t):t?/^\s*-?\d+(\.\d+)?\s*$/.test(t)||/^\s*-?\d+\.\s*$/.test(t)||/^\s*-?\.\d+\s*$/.test(t):!1}var An=function(){function t(n){if(vt(this,t),A(this,"origin",""),A(this,"negative",void 0),A(this,"integer",void 0),A(this,"decimal",void 0),A(this,"decimalLen",void 0),A(this,"empty",void 0),A(this,"nan",void 0),It(n)){this.empty=!0;return}if(this.origin=String(n),n==="-"||Number.isNaN(n)){this.nan=!0;return}var a=n;if(Qe(a)&&(a=Number(a)),a=typeof a=="string"?a:ke(a),Ye(a)){var r=re(a);this.negative=r.negative;var l=r.trimStr.split(".");this.integer=BigInt(l[0]);var o=l[1]||"0";this.decimal=BigInt(o),this.decimalLen=o.length}else this.nan=!0}return gt(t,[{key:"getMark",value:function(){return this.negative?"-":""}},{key:"getIntegerStr",value:function(){return this.integer.toString()}},{key:"getDecimalStr",value:function(){return this.decimal.toString().padStart(this.decimalLen,"0")}},{key:"alignDecimal",value:function(a){var r="".concat(this.getMark()).concat(this.getIntegerStr()).concat(this.getDecimalStr().padEnd(a,"0"));return BigInt(r)}},{key:"negate",value:function(){var a=new t(this.toString());return a.negative=!a.negative,a}},{key:"cal",value:function(a,r,l){var o=Math.max(this.getDecimalStr().length,a.getDecimalStr().length),g=this.alignDecimal(o),f=a.alignDecimal(o),p=r(g,f).toString(),h=l(o),d=re(p),E=d.negativeStr,y=d.trimStr,I="".concat(E).concat(y.padStart(h+1,"0"));return new t("".concat(I.slice(0,-h),".").concat(I.slice(-h)))}},{key:"add",value:function(a){if(this.isInvalidate())return new t(a);var r=new t(a);return r.isInvalidate()?this:this.cal(r,function(l,o){return l+o},function(l){return l})}},{key:"multi",value:function(a){var r=new t(a);return this.isInvalidate()||r.isInvalidate()?new t(NaN):this.cal(r,function(l,o){return l*o},function(l){return l*2})}},{key:"isEmpty",value:function(){return this.empty}},{key:"isNaN",value:function(){return this.nan}},{key:"isInvalidate",value:function(){return this.isEmpty()||this.isNaN()}},{key:"equals",value:function(a){return this.toString()===(a==null?void 0:a.toString())}},{key:"lessEquals",value:function(a){return this.add(a.negate().toString()).toNumber()<=0}},{key:"toNumber",value:function(){return this.isNaN()?NaN:Number(this.toString())}},{key:"toString",value:function(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;return a?this.isInvalidate()?"":re("".concat(this.getMark()).concat(this.getIntegerStr(),".").concat(this.getDecimalStr())).fullStr:this.origin}}]),t}(),Dn=function(){function t(n){if(vt(this,t),A(this,"origin",""),A(this,"number",void 0),A(this,"empty",void 0),It(n)){this.empty=!0;return}this.origin=String(n),this.number=Number(n)}return gt(t,[{key:"negate",value:function(){return new t(-this.toNumber())}},{key:"add",value:function(a){if(this.isInvalidate())return new t(a);var r=Number(a);if(Number.isNaN(r))return this;var l=this.number+r;if(l>Number.MAX_SAFE_INTEGER)return new t(Number.MAX_SAFE_INTEGER);if(l<Number.MIN_SAFE_INTEGER)return new t(Number.MIN_SAFE_INTEGER);var o=Math.max(ae(this.number),ae(r));return new t(l.toFixed(o))}},{key:"multi",value:function(a){var r=Number(a);if(this.isInvalidate()||Number.isNaN(r))return new t(NaN);var l=this.number*r;if(l>Number.MAX_SAFE_INTEGER)return new t(Number.MAX_SAFE_INTEGER);if(l<Number.MIN_SAFE_INTEGER)return new t(Number.MIN_SAFE_INTEGER);var o=Math.max(ae(this.number),ae(r));return new t(l.toFixed(o))}},{key:"isEmpty",value:function(){return this.empty}},{key:"isNaN",value:function(){return Number.isNaN(this.number)}},{key:"isInvalidate",value:function(){return this.isEmpty()||this.isNaN()}},{key:"equals",value:function(a){return this.toNumber()===(a==null?void 0:a.toNumber())}},{key:"lessEquals",value:function(a){return this.add(a.negate().toString()).toNumber()<=0}},{key:"toNumber",value:function(){return this.number}},{key:"toString",value:function(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;return a?this.isInvalidate()?"":ke(this.number):this.origin}}]),t}();function q(t){return Ge()?new An(t):new Dn(t)}function Re(t,n,a){var r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(t==="")return"";var l=re(t),o=l.negativeStr,g=l.integerStr,f=l.decimalStr,p="".concat(n).concat(f),h="".concat(o).concat(g);if(a>=0){var d=Number(f[a]);if(d>=5&&!r){var E=q(t).add("".concat(o,"0.").concat("0".repeat(a)).concat(10-d));return Re(E.toString(),n,a,r)}return a===0?h:"".concat(h).concat(n).concat(f.padEnd(a,"0").slice(0,a))}return p===".0"?h:"".concat(h).concat(p)}function Pn(t,n){return typeof Proxy<"u"&&t?new Proxy(t,{get:function(r,l){if(n[l])return n[l];var o=r[l];return typeof o=="function"?o.bind(r):o}}):t}function _n(t,n){var a=i.useRef(null);function r(){try{var o=t.selectionStart,g=t.selectionEnd,f=t.value,p=f.substring(0,o),h=f.substring(g);a.current={start:o,end:g,value:f,beforeTxt:p,afterTxt:h}}catch{}}function l(){if(t&&a.current&&n)try{var o=t.value,g=a.current,f=g.beforeTxt,p=g.afterTxt,h=g.start,d=o.length;if(o.startsWith(f))d=f.length;else if(o.endsWith(p))d=o.length-a.current.afterTxt.length;else{var E=f[h-1],y=o.indexOf(E,h-1);y!==-1&&(d=y+1)}t.setSelectionRange(d,d)}catch(I){jt(!1,"Something warning of cursor restore. Please fire issue about this: ".concat(I.message))}}return[r,l]}var Bn=function(){var n=i.useState(!1),a=he(n,2),r=a[0],l=a[1];return Vt(function(){l(qt())},[]),r},Fn=200,Tn=600;function jn(t){var n=t.prefixCls,a=t.upNode,r=t.downNode,l=t.upDisabled,o=t.downDisabled,g=t.onStep,f=i.useRef(),p=i.useRef([]),h=i.useRef();h.current=g;var d=function(){clearTimeout(f.current)},E=function(s,c){s.preventDefault(),d(),h.current(c);function x(){h.current(c),f.current=setTimeout(x,Fn)}f.current=setTimeout(x,Tn)};i.useEffect(function(){return function(){d(),p.current.forEach(function(O){return Ce.cancel(O)})}},[]);var y=Bn();if(y)return null;var I="".concat(n,"-handler"),D=K(I,"".concat(I,"-up"),A({},"".concat(I,"-up-disabled"),l)),M=K(I,"".concat(I,"-down"),A({},"".concat(I,"-down-disabled"),o)),C=function(){return p.current.push(Ce(d))},$={unselectable:"on",role:"button",onMouseUp:C,onMouseLeave:C};return i.createElement("div",{className:"".concat(I,"-wrap")},i.createElement("span",ye({},$,{onMouseDown:function(s){E(s,!0)},"aria-label":"Increase Value","aria-disabled":l,className:D}),a||i.createElement("span",{unselectable:"on",className:"".concat(n,"-handler-up-inner")})),i.createElement("span",ye({},$,{onMouseDown:function(s){E(s,!1)},"aria-label":"Decrease Value","aria-disabled":o,className:M}),r||i.createElement("span",{unselectable:"on",className:"".concat(n,"-handler-down-inner")})))}function ut(t){var n=typeof t=="number"?ke(t):re(t).fullStr,a=n.includes(".");return a?re(n.replace(/(\d)\.(\d)/g,"$1$2.")).fullStr:t+"0"}const Vn=function(){var t=i.useRef(0),n=function(){Ce.cancel(t.current)};return i.useEffect(function(){return n},[]),function(a){n(),t.current=Ce(function(){a()})}};var qn=["prefixCls","className","style","min","max","step","defaultValue","value","disabled","readOnly","upHandler","downHandler","keyboard","changeOnWheel","controls","classNames","stringMode","parser","formatter","precision","decimalSeparator","onChange","onInput","onPressEnter","onStep","changeOnBlur","domRef"],zn=["disabled","style","prefixCls","value","prefix","suffix","addonBefore","addonAfter","className","classNames"],dt=function(n,a){return n||a.isEmpty()?a.toString():a.toNumber()},mt=function(n){var a=q(n);return a.isInvalidate()?null:a},Ln=i.forwardRef(function(t,n){var a=t.prefixCls,r=t.className,l=t.style,o=t.min,g=t.max,f=t.step,p=f===void 0?1:f,h=t.defaultValue,d=t.value,E=t.disabled,y=t.readOnly,I=t.upHandler,D=t.downHandler,M=t.keyboard,C=t.changeOnWheel,$=C===void 0?!1:C,O=t.controls,s=O===void 0?!0:O;t.classNames;var c=t.stringMode,x=t.parser,F=t.formatter,P=t.precision,T=t.decimalSeparator,ee=t.onChange,z=t.onInput,W=t.onPressEnter,H=t.onStep,U=t.changeOnBlur,ue=U===void 0?!0:U,Oe=t.domRef,Me=ht(t,qn),Se="".concat(a,"-input"),X=i.useRef(null),Q=i.useState(!1),Ne=he(Q,2),ie=Ne[0],de=Ne[1],j=i.useRef(!1),te=i.useRef(!1),le=i.useRef(!1),Ae=i.useState(function(){return q(d??h)}),Ie=he(Ae,2),w=Ie[0],se=Ie[1];function wt(m){d===void 0&&se(m)}var De=i.useCallback(function(m,u){if(!u)return P>=0?P:Math.max(ae(m),ae(p))},[P,p]),Pe=i.useCallback(function(m){var u=String(m);if(x)return x(u);var b=u;return T&&(b=b.replace(T,".")),b.replace(/[^\w.-]+/g,"")},[x,T]),_e=i.useRef(""),Je=i.useCallback(function(m,u){if(F)return F(m,{userTyping:u,input:String(_e.current)});var b=typeof m=="number"?ke(m):m;if(!u){var v=De(b,u);if(Ye(b)&&(T||v>=0)){var L=T||".";b=Re(b,L,v)}}return b},[F,De,T]),$t=i.useState(function(){var m=h??d;return w.isInvalidate()&&["string","number"].includes(Gt(m))?Number.isNaN(m)?"":m:Je(w.toString(),!1)}),Ze=he($t,2),me=Ze[0],et=Ze[1];_e.current=me;function fe(m,u){et(Je(m.isInvalidate()?m.toString(!1):m.toString(!u),u))}var oe=i.useMemo(function(){return mt(g)},[g,P]),ce=i.useMemo(function(){return mt(o)},[o,P]),tt=i.useMemo(function(){return!oe||!w||w.isInvalidate()?!1:oe.lessEquals(w)},[oe,w]),nt=i.useMemo(function(){return!ce||!w||w.isInvalidate()?!1:w.lessEquals(ce)},[ce,w]),Rt=_n(X.current,ie),at=he(Rt,2),Ct=at[0],kt=at[1],rt=function(u){return oe&&!u.lessEquals(oe)?oe:ce&&!ce.lessEquals(u)?ce:null},Be=function(u){return!rt(u)},xe=function(u,b){var v=u,L=Be(v)||v.isEmpty();if(!v.isEmpty()&&!b&&(v=rt(v)||v,L=!0),!y&&!E&&L){var pe=v.toString(),Te=De(pe,b);return Te>=0&&(v=q(Re(pe,".",Te)),Be(v)||(v=q(Re(pe,".",Te,!0)))),v.equals(w)||(wt(v),ee==null||ee(v.isEmpty()?null:dt(c,v)),d===void 0&&fe(v,b)),v}return w},Ot=Vn(),it=function m(u){if(Ct(),_e.current=u,et(u),!te.current){var b=Pe(u),v=q(b);v.isNaN()||xe(v,!0)}z==null||z(u),Ot(function(){var L=u;x||(L=u.replace(/。/g,".")),L!==u&&m(L)})},Mt=function(){te.current=!0},At=function(){te.current=!1,it(X.current.value)},Dt=function(u){it(u.target.value)},Fe=function(u){var b;if(!(u&&tt||!u&&nt)){j.current=!1;var v=q(le.current?ut(p):p);u||(v=v.negate());var L=(w||q(0)).add(v.toString()),pe=xe(L,!1);H==null||H(dt(c,pe),{offset:le.current?ut(p):p,type:u?"up":"down"}),(b=X.current)===null||b===void 0||b.focus()}},lt=function(u){var b=q(Pe(me)),v;b.isNaN()?v=xe(w,u):v=xe(b,u),d!==void 0?fe(w,!1):v.isNaN()||fe(v,!1)},Pt=function(){j.current=!0},_t=function(u){var b=u.key,v=u.shiftKey;j.current=!0,le.current=v,b==="Enter"&&(te.current||(j.current=!1),lt(!1),W==null||W(u)),M!==!1&&!te.current&&["Up","ArrowUp","Down","ArrowDown"].includes(b)&&(Fe(b==="Up"||b==="ArrowUp"),u.preventDefault())},Bt=function(){j.current=!1,le.current=!1};i.useEffect(function(){if($&&ie){var m=function(v){Fe(v.deltaY<0),v.preventDefault()},u=X.current;if(u)return u.addEventListener("wheel",m,{passive:!1}),function(){return u.removeEventListener("wheel",m)}}});var Ft=function(){ue&&lt(!1),de(!1),j.current=!1};return je(function(){w.isInvalidate()||fe(w,!1)},[P,F]),je(function(){var m=q(d);se(m);var u=q(Pe(me));(!m.equals(u)||!j.current||F)&&fe(m,j.current)},[d]),je(function(){F&&kt()},[me]),i.createElement("div",{ref:Oe,className:K(a,r,A(A(A(A(A({},"".concat(a,"-focused"),ie),"".concat(a,"-disabled"),E),"".concat(a,"-readonly"),y),"".concat(a,"-not-a-number"),w.isNaN()),"".concat(a,"-out-of-range"),!w.isInvalidate()&&!Be(w))),style:l,onFocus:function(){de(!0)},onBlur:Ft,onKeyDown:_t,onKeyUp:Bt,onCompositionStart:Mt,onCompositionEnd:At,onBeforeInput:Pt},s&&i.createElement(jn,{prefixCls:a,upNode:I,downNode:D,upDisabled:tt,downDisabled:nt,onStep:Fe}),i.createElement("div",{className:"".concat(Se,"-wrap")},i.createElement("input",ye({autoComplete:"off",role:"spinbutton","aria-valuemin":o,"aria-valuemax":g,"aria-valuenow":w.isInvalidate()?null:w.toString(),step:p},Me,{ref:Wt(X,n),className:Se,value:me,onChange:Dt,disabled:E,readOnly:y}))))}),Gn=i.forwardRef(function(t,n){var a=t.disabled,r=t.style,l=t.prefixCls,o=l===void 0?"rc-input-number":l,g=t.value,f=t.prefix,p=t.suffix,h=t.addonBefore,d=t.addonAfter,E=t.className,y=t.classNames,I=ht(t,zn),D=i.useRef(null),M=i.useRef(null),C=i.useRef(null),$=function(s){C.current&&Lt(C.current,s)};return i.useImperativeHandle(n,function(){return Pn(C.current,{focus:$,nativeElement:D.current.nativeElement||M.current})}),i.createElement(zt,{className:E,triggerFocus:$,prefixCls:o,value:g,disabled:a,style:r,prefix:f,suffix:p,addonAfter:d,addonBefore:h,classNames:y,components:{affixWrapper:"div",groupWrapper:"div",wrapper:"div",groupAddon:"div"},ref:D},i.createElement(Ln,ye({prefixCls:o,disabled:a,ref:C,domRef:M,className:y==null?void 0:y.input},I)))});const Wn=t=>{var n;const a=(n=t.handleVisible)!==null&&n!==void 0?n:"auto",r=t.controlHeightSM-t.lineWidth*2;return Object.assign(Object.assign({},Ht(t)),{controlWidth:90,handleWidth:r,handleFontSize:t.fontSize/2,handleVisible:a,handleActiveBg:t.colorFillAlter,handleBg:t.colorBgContainer,filledHandleBg:new Ut(t.colorFillSecondary).onBackground(t.colorBgContainer).toHexString(),handleHoverColor:t.colorPrimary,handleBorderColor:t.colorBorder,handleOpacity:a===!0?1:0,handleVisibleWidth:a===!0?r:0})},ft=({componentCls:t,borderRadiusSM:n,borderRadiusLG:a},r)=>{const l=r==="lg"?a:n;return{[`&-${r}`]:{[`${t}-handler-wrap`]:{borderStartEndRadius:l,borderEndEndRadius:l},[`${t}-handler-up`]:{borderStartEndRadius:l},[`${t}-handler-down`]:{borderEndEndRadius:l}}}},Hn=t=>{const{componentCls:n,lineWidth:a,lineType:r,borderRadius:l,inputFontSizeSM:o,inputFontSizeLG:g,controlHeightLG:f,controlHeightSM:p,colorError:h,paddingInlineSM:d,paddingBlockSM:E,paddingBlockLG:y,paddingInlineLG:I,colorIcon:D,motionDurationMid:M,handleHoverColor:C,handleOpacity:$,paddingInline:O,paddingBlock:s,handleBg:c,handleActiveBg:x,colorTextDisabled:F,borderRadiusSM:P,borderRadiusLG:T,controlWidth:ee,handleBorderColor:z,filledHandleBg:W,lineHeightLG:H,calc:U}=t;return[{[n]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Ve(t)),Et(t)),{display:"inline-block",width:ee,margin:0,padding:0,borderRadius:l}),Jt(t,{[`${n}-handler-wrap`]:{background:c,[`${n}-handler-down`]:{borderBlockStart:`${_(a)} ${r} ${z}`}}})),Zt(t,{[`${n}-handler-wrap`]:{background:W,[`${n}-handler-down`]:{borderBlockStart:`${_(a)} ${r} ${z}`}},"&:focus-within":{[`${n}-handler-wrap`]:{background:c}}})),en(t,{[`${n}-handler-wrap`]:{background:c,[`${n}-handler-down`]:{borderBlockStart:`${_(a)} ${r} ${z}`}}})),tn(t)),{"&-rtl":{direction:"rtl",[`${n}-input`]:{direction:"rtl"}},"&-lg":{padding:0,fontSize:g,lineHeight:H,borderRadius:T,[`input${n}-input`]:{height:U(f).sub(U(a).mul(2)).equal(),padding:`${_(y)} ${_(I)}`}},"&-sm":{padding:0,fontSize:o,borderRadius:P,[`input${n}-input`]:{height:U(p).sub(U(a).mul(2)).equal(),padding:`${_(E)} ${_(d)}`}},"&-out-of-range":{[`${n}-input-wrap`]:{input:{color:h}}},"&-group":Object.assign(Object.assign(Object.assign({},Ve(t)),an(t)),{"&-wrapper":Object.assign(Object.assign(Object.assign({display:"inline-block",textAlign:"start",verticalAlign:"top",[`${n}-affix-wrapper`]:{width:"100%"},"&-lg":{[`${n}-group-addon`]:{borderRadius:T,fontSize:t.fontSizeLG}},"&-sm":{[`${n}-group-addon`]:{borderRadius:P}}},rn(t)),ln(t)),{[`&:not(${n}-compact-first-item):not(${n}-compact-last-item)${n}-compact-item`]:{[`${n}, ${n}-group-addon`]:{borderRadius:0}},[`&:not(${n}-compact-last-item)${n}-compact-first-item`]:{[`${n}, ${n}-group-addon`]:{borderStartEndRadius:0,borderEndEndRadius:0}},[`&:not(${n}-compact-first-item)${n}-compact-last-item`]:{[`${n}, ${n}-group-addon`]:{borderStartStartRadius:0,borderEndStartRadius:0}}})}),[`&-disabled ${n}-input`]:{cursor:"not-allowed"},[n]:{"&-input":Object.assign(Object.assign(Object.assign(Object.assign({},Ve(t)),{width:"100%",padding:`${_(s)} ${_(O)}`,textAlign:"start",backgroundColor:"transparent",border:0,borderRadius:l,outline:0,transition:`all ${M} linear`,appearance:"textfield",fontSize:"inherit"}),nn(t.colorTextPlaceholder)),{'&[type="number"]::-webkit-inner-spin-button, &[type="number"]::-webkit-outer-spin-button':{margin:0,appearance:"none"}})},[`&:hover ${n}-handler-wrap, &-focused ${n}-handler-wrap`]:{width:t.handleWidth,opacity:1}})},{[n]:Object.assign(Object.assign(Object.assign({[`${n}-handler-wrap`]:{position:"absolute",insetBlockStart:0,insetInlineEnd:0,width:t.handleVisibleWidth,opacity:$,height:"100%",borderStartStartRadius:0,borderStartEndRadius:l,borderEndEndRadius:l,borderEndStartRadius:0,display:"flex",flexDirection:"column",alignItems:"stretch",transition:`all ${M}`,overflow:"hidden",[`${n}-handler`]:{display:"flex",alignItems:"center",justifyContent:"center",flex:"auto",height:"40%",[`
              ${n}-handler-up-inner,
              ${n}-handler-down-inner
            `]:{marginInlineEnd:0,fontSize:t.handleFontSize}}},[`${n}-handler`]:{height:"50%",overflow:"hidden",color:D,fontWeight:"bold",lineHeight:0,textAlign:"center",cursor:"pointer",borderInlineStart:`${_(a)} ${r} ${z}`,transition:`all ${M} linear`,"&:active":{background:x},"&:hover":{height:"60%",[`
              ${n}-handler-up-inner,
              ${n}-handler-down-inner
            `]:{color:C}},"&-up-inner, &-down-inner":Object.assign(Object.assign({},sn()),{color:D,transition:`all ${M} linear`,userSelect:"none"})},[`${n}-handler-up`]:{borderStartEndRadius:l},[`${n}-handler-down`]:{borderEndEndRadius:l}},ft(t,"lg")),ft(t,"sm")),{"&-disabled, &-readonly":{[`${n}-handler-wrap`]:{display:"none"},[`${n}-input`]:{color:"inherit"}},[`
          ${n}-handler-up-disabled,
          ${n}-handler-down-disabled
        `]:{cursor:"not-allowed"},[`
          ${n}-handler-up-disabled:hover &-handler-up-inner,
          ${n}-handler-down-disabled:hover &-handler-down-inner
        `]:{color:F}})}]},Un=t=>{const{componentCls:n,paddingBlock:a,paddingInline:r,inputAffixPadding:l,controlWidth:o,borderRadiusLG:g,borderRadiusSM:f,paddingInlineLG:p,paddingInlineSM:h,paddingBlockLG:d,paddingBlockSM:E,motionDurationMid:y}=t;return{[`${n}-affix-wrapper`]:Object.assign(Object.assign({[`input${n}-input`]:{padding:`${_(a)} 0`}},Et(t)),{position:"relative",display:"inline-flex",alignItems:"center",width:o,padding:0,paddingInlineStart:r,"&-lg":{borderRadius:g,paddingInlineStart:p,[`input${n}-input`]:{padding:`${_(d)} 0`}},"&-sm":{borderRadius:f,paddingInlineStart:h,[`input${n}-input`]:{padding:`${_(E)} 0`}},[`&:not(${n}-disabled):hover`]:{zIndex:1},"&-focused, &:focus":{zIndex:1},[`&-disabled > ${n}-disabled`]:{background:"transparent"},[`> div${n}`]:{width:"100%",border:"none",outline:"none",[`&${n}-focused`]:{boxShadow:"none !important"}},"&::before":{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'},[`${n}-handler-wrap`]:{zIndex:2},[n]:{position:"static",color:"inherit","&-prefix, &-suffix":{display:"flex",flex:"none",alignItems:"center",pointerEvents:"none"},"&-prefix":{marginInlineEnd:l},"&-suffix":{insetBlockStart:0,insetInlineEnd:0,height:"100%",marginInlineEnd:r,marginInlineStart:l,transition:`margin ${y}`}},[`&:hover ${n}-handler-wrap, &-focused ${n}-handler-wrap`]:{width:t.handleWidth,opacity:1},[`&:not(${n}-affix-wrapper-without-controls):hover ${n}-suffix`]:{marginInlineEnd:t.calc(t.handleWidth).add(r).equal()}})}},Kn=Kt("InputNumber",t=>{const n=Xt(t,Qt(t));return[Hn(n),Un(n),Yt(n)]},Wn,{unitless:{handleOpacity:!0}});var Xn=function(t,n){var a={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&n.indexOf(r)<0&&(a[r]=t[r]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,r=Object.getOwnPropertySymbols(t);l<r.length;l++)n.indexOf(r[l])<0&&Object.prototype.propertyIsEnumerable.call(t,r[l])&&(a[r[l]]=t[r[l]]);return a};const xt=i.forwardRef((t,n)=>{const{getPrefixCls:a,direction:r}=i.useContext(on),l=i.useRef(null);i.useImperativeHandle(n,()=>l.current);const{className:o,rootClassName:g,size:f,disabled:p,prefixCls:h,addonBefore:d,addonAfter:E,prefix:y,suffix:I,bordered:D,readOnly:M,status:C,controls:$,variant:O}=t,s=Xn(t,["className","rootClassName","size","disabled","prefixCls","addonBefore","addonAfter","prefix","suffix","bordered","readOnly","status","controls","variant"]),c=a("input-number",h),x=cn(c),[F,P,T]=Kn(c,x),{compactSize:ee,compactItemClassnames:z}=un(c,r);let W=i.createElement(Mn,{className:`${c}-handler-up-inner`}),H=i.createElement(gn,{className:`${c}-handler-down-inner`});const U=typeof $=="boolean"?$:void 0;typeof $=="object"&&(W=typeof $.upIcon>"u"?W:i.createElement("span",{className:`${c}-handler-up-inner`},$.upIcon),H=typeof $.downIcon>"u"?H:i.createElement("span",{className:`${c}-handler-down-inner`},$.downIcon));const{hasFeedback:ue,status:Oe,isFormItemInput:Me,feedbackIcon:Se}=i.useContext(dn),X=vn(Oe,C),Q=mn(w=>{var se;return(se=f??ee)!==null&&se!==void 0?se:w}),Ne=i.useContext(fn),ie=p??Ne,[de,j]=pn("inputNumber",O,D),te=ue&&i.createElement(i.Fragment,null,Se),le=K({[`${c}-lg`]:Q==="large",[`${c}-sm`]:Q==="small",[`${c}-rtl`]:r==="rtl",[`${c}-in-form-item`]:Me},P),Ae=`${c}-group`,Ie=i.createElement(Gn,Object.assign({ref:l,disabled:ie,className:K(T,x,o,g,z),upHandler:W,downHandler:H,prefixCls:c,readOnly:M,controls:U,prefix:y,suffix:te||I,addonBefore:d&&i.createElement(ot,{form:!0,space:!0},d),addonAfter:E&&i.createElement(ot,{form:!0,space:!0},E),classNames:{input:le,variant:K({[`${c}-${de}`]:j},st(c,X,ue)),affixWrapper:K({[`${c}-affix-wrapper-sm`]:Q==="small",[`${c}-affix-wrapper-lg`]:Q==="large",[`${c}-affix-wrapper-rtl`]:r==="rtl",[`${c}-affix-wrapper-without-controls`]:$===!1||ie},P),wrapper:K({[`${Ae}-rtl`]:r==="rtl"},P),groupWrapper:K({[`${c}-group-wrapper-sm`]:Q==="small",[`${c}-group-wrapper-lg`]:Q==="large",[`${c}-group-wrapper-rtl`]:r==="rtl",[`${c}-group-wrapper-${de}`]:j},st(`${c}-group-wrapper`,X,ue),P)}},s));return F(Ie)}),We=xt,Qn=t=>i.createElement(hn,{theme:{components:{InputNumber:{handleVisible:!0}}}},i.createElement(xt,Object.assign({},t)));We._InternalPanelDoNotUseOrYouWillBeFired=Qn;var Yn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M834.1 469.2A347.49 347.49 0 00751.2 354l-29.1-26.7a8.09 8.09 0 00-13 3.3l-13 37.3c-8.1 23.4-23 47.3-44.1 70.8-1.4 1.5-3 1.9-4.1 2-1.1.1-2.8-.1-4.3-1.5-1.4-1.2-2.1-3-2-4.8 3.7-60.2-14.3-128.1-53.7-202C555.3 171 510 123.1 453.4 89.7l-41.3-24.3c-5.4-3.2-12.3 1-12 7.3l2.2 48c1.5 32.8-2.3 61.8-11.3 85.9-11 29.5-26.8 56.9-47 81.5a295.64 295.64 0 01-47.5 46.1 352.6 352.6 0 00-100.3 121.5A347.75 347.75 0 00160 610c0 47.2 9.3 92.9 27.7 136a349.4 349.4 0 0075.5 110.9c32.4 32 70 57.2 111.9 74.7C418.5 949.8 464.5 959 512 959s93.5-9.2 136.9-27.3A348.6 348.6 0 00760.8 857c32.4-32 57.8-69.4 75.5-110.9a344.2 344.2 0 0027.7-136c0-48.8-10-96.2-29.9-140.9zM713 808.5c-53.7 53.2-125 82.4-201 82.4s-147.3-29.2-201-82.4c-53.5-53.1-83-123.5-83-198.4 0-43.5 9.8-85.2 29.1-124 18.8-37.9 46.8-71.8 80.8-97.9a349.6 349.6 0 0058.6-56.8c25-30.5 44.6-64.5 58.2-101a240 240 0 0012.1-46.5c24.1 22.2 44.3 49 61.2 80.4 33.4 62.6 48.8 118.3 45.8 165.7a74.01 74.01 0 0024.4 59.8 73.36 73.36 0 0053.4 18.8c19.7-1 37.8-9.7 51-24.4 13.3-14.9 24.8-30.1 34.4-45.6 14 17.9 25.7 37.4 35 58.4 15.9 35.8 24 73.9 24 113.1 0 74.9-29.5 145.4-83 198.4z"}}]},name:"fire",theme:"outlined"},Jn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M866.9 169.9L527.1 54.1C523 52.7 517.5 52 512 52s-11 .7-15.1 2.1L157.1 169.9c-8.3 2.8-15.1 12.4-15.1 21.2v482.4c0 8.8 5.7 20.4 12.6 25.9L499.3 968c3.5 2.7 8 4.1 12.6 4.1s9.2-1.4 12.6-4.1l344.7-268.6c6.9-5.4 12.6-17 12.6-25.9V191.1c.2-8.8-6.6-18.3-14.9-21.2zM810 654.3L512 886.5 214 654.3V226.7l298-101.6 298 101.6v427.6zM402.9 528.8l-77.5 77.5a8.03 8.03 0 000 11.3l34 34c3.1 3.1 8.2 3.1 11.3 0l77.5-77.5c55.7 35.1 130.1 28.4 178.6-20.1 56.3-56.3 56.3-147.5 0-203.8-56.3-56.3-147.5-56.3-203.8 0-48.5 48.5-55.2 123-20.1 178.6zm65.4-133.3c31.3-31.3 82-31.3 113.2 0 31.3 31.3 31.3 82 0 113.2-31.3 31.3-82 31.3-113.2 0s-31.3-81.9 0-113.2z"}}]},name:"security-scan",theme:"outlined"},Zn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M931.4 498.9L94.9 79.5c-3.4-1.7-7.3-2.1-11-1.2a15.99 15.99 0 00-11.7 19.3l86.2 352.2c1.3 5.3 5.2 9.6 10.4 11.3l147.7 50.7-147.6 50.7c-5.2 1.8-9.1 6-10.3 11.3L72.2 926.5c-.9 3.7-.5 7.6 1.2 10.9 3.9 7.9 13.5 11.1 21.5 7.2l836.5-417c3.1-1.5 5.6-4.1 7.2-7.1 3.9-8 .7-17.6-7.2-21.6zM170.8 826.3l50.3-205.6 295.2-101.3c2.3-.8 4.2-2.6 5-5 1.4-4.2-.8-8.7-5-10.2L221.1 403 171 198.2l628 314.9-628.2 313.2z"}}]},name:"send",theme:"outlined"};function He(){return He=Object.assign?Object.assign.bind():function(t){for(var n=1;n<arguments.length;n++){var a=arguments[n];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(t[r]=a[r])}return t},He.apply(this,arguments)}const ea=(t,n)=>i.createElement(Xe,He({},t,{ref:n,icon:Yn})),ta=i.forwardRef(ea);function Ue(){return Ue=Object.assign?Object.assign.bind():function(t){for(var n=1;n<arguments.length;n++){var a=arguments[n];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(t[r]=a[r])}return t},Ue.apply(this,arguments)}const na=(t,n)=>i.createElement(Xe,Ue({},t,{ref:n,icon:Jn})),aa=i.forwardRef(na);function Ke(){return Ke=Object.assign?Object.assign.bind():function(t){for(var n=1;n<arguments.length;n++){var a=arguments[n];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(t[r]=a[r])}return t},Ke.apply(this,arguments)}const ra=(t,n)=>i.createElement(Xe,Ke({},t,{ref:n,icon:Zn})),ia=i.forwardRef(ra),{Title:la,Text:ve,Paragraph:Le}=bt,{Option:pa}=J,sa=()=>{const{createAuthRequest:t}=En(),[n,a]=i.useState(!0),[r,l]=i.useState(!1),[o,g]=i.useState(null),[f,p]=i.useState(!1),h=[{key:"machine_alert",label:"Alertes Machine",description:"Alertes critiques et avertissements des machines",icon:e.createElement(wn,{style:{color:"#ff4d4f"}}),critical:!0},{key:"production",label:"Production",description:"Changements d'équipe, objectifs, jalons de production",icon:e.createElement(be,{style:{color:"#1890ff"}})},{key:"quality",label:"Contrôle Qualité",description:"Alertes de défauts et contrôle qualité",icon:e.createElement(ze,{style:{color:"#faad14"}})},{key:"maintenance",label:"Maintenance",description:"Notifications de maintenance et réparations",icon:e.createElement(be,{style:{color:"#52c41a"}})},{key:"alert",label:"Alertes Système",description:"Alertes générales du système",icon:e.createElement($e,{style:{color:"#722ed1"}})},{key:"update",label:"Mises à jour",description:"Mises à jour système et nouvelles fonctionnalités",icon:e.createElement(we,{style:{color:"#13c2c2"}})},{key:"info",label:"Informations",description:"Informations générales du système",icon:e.createElement(we,{style:{color:"#8c8c8c"}})}],d=[{key:"critical",label:"Critique",description:"Alertes de sécurité critiques (ne peut pas être désactivé)",icon:e.createElement(ta,{style:{color:"#ff4d4f"}}),disabled:!0,color:"#ff4d4f"},{key:"high",label:"Élevée",description:"Notifications importantes nécessitant une attention immédiate",icon:e.createElement(ze,{style:{color:"#fa8c16"}}),color:"#fa8c16"},{key:"medium",label:"Moyenne",description:"Notifications standard",icon:e.createElement(we,{style:{color:"#1890ff"}}),color:"#1890ff"},{key:"low",label:"Faible",description:"Notifications informatives",icon:e.createElement(we,{style:{color:"#52c41a"}}),color:"#52c41a"}],E=[{value:"immediate",label:"Immédiat",description:"Recevoir les emails immédiatement"},{value:"hourly_batch",label:"Groupé par heure",description:"Recevoir un résumé toutes les heures"},{value:"daily_digest",label:"Résumé quotidien",description:"Recevoir un résumé quotidien"}];i.useEffect(()=>{y()},[]);const y=async()=>{try{a(!0);const s=await t("get","/api/notification-preferences");s.success?(g(s.data),p(!1)):G.error("Erreur lors du chargement des préférences")}catch(s){console.error("Error loading preferences:",s),G.error("Erreur lors du chargement des préférences")}finally{a(!1)}},I=async()=>{try{l(!0),(await t("put","/api/notification-preferences",o)).success?(G.success("Préférences sauvegardées avec succès"),p(!1)):G.error("Erreur lors de la sauvegarde des préférences")}catch(s){console.error("Error saving preferences:",s),G.error("Erreur lors de la sauvegarde des préférences")}finally{l(!1)}},D=async()=>{try{l(!0);const s=await t("post","/api/notification-preferences/reset");s.success?(g(s.data),p(!1),G.success("Préférences réinitialisées aux valeurs par défaut")):G.error("Erreur lors de la réinitialisation")}catch(s){console.error("Error resetting preferences:",s),G.error("Erreur lors de la réinitialisation")}finally{l(!1)}},M=(s,c)=>{g(x=>({...x,notification_categories:{...x.notification_categories,[s]:c}})),p(!0)},C=(s,c)=>{if(s==="critical"&&!c){G.warning("Les notifications critiques ne peuvent pas être désactivées pour des raisons de sécurité");return}g(x=>({...x,priority_thresholds:{...x.priority_thresholds,[s]:c}})),p(!0)},$=s=>{g(c=>({...c,email_frequency:s})),p(!0)},O=s=>{g(c=>({...c,email_enabled:s})),p(!0)};return n?e.createElement("div",{style:{textAlign:"center",padding:"50px"}},e.createElement(bn,{size:"large"}),e.createElement("div",{style:{marginTop:16}},e.createElement(ve,null,"Chargement des préférences de notification..."))):o?e.createElement("div",{style:{maxWidth:"1200px",margin:"0 auto"}},e.createElement("div",{style:{marginBottom:"24px"}},e.createElement(la,{level:3},e.createElement($e,{style:{marginRight:"8px"}}),"Préférences de Notification"),e.createElement(Le,null,"Configurez vos préférences de notification pour recevoir les alertes importantes selon vos besoins. Les notifications critiques de sécurité ne peuvent pas être désactivées.")),e.createElement(Y,{title:e.createElement(Ee,null,e.createElement(yt,null),e.createElement("span",null,"Paramètres Email")),style:{marginBottom:"24px"}},e.createElement(B,{gutter:[24,16]},e.createElement(N,{xs:24,md:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(ve,{strong:!0},"Notifications par email"),e.createElement("div",{style:{marginTop:"8px"}},e.createElement(R,{checked:o.email_enabled,onChange:O,checkedChildren:"Activé",unCheckedChildren:"Désactivé"})))),e.createElement(N,{xs:24,md:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(ve,{strong:!0},"Fréquence d'envoi"),e.createElement("div",{style:{marginTop:"8px"}},e.createElement(ct.Group,{value:o.email_frequency,onChange:s=>$(s.target.value),disabled:!o.email_enabled},E.map(s=>e.createElement(ct.Button,{key:s.value,value:s.value},e.createElement(yn,{title:s.description},s.label))))))))),e.createElement(Y,{title:e.createElement(Ee,null,e.createElement($e,null),e.createElement("span",null,"Catégories de Notification")),style:{marginBottom:"24px"}},e.createElement(B,{gutter:[16,16]},h.map(s=>e.createElement(N,{xs:24,md:12,lg:8,key:s.key},e.createElement(Y,{size:"small",style:{height:"100%"}},e.createElement("div",{style:{display:"flex",alignItems:"flex-start",gap:"12px"}},e.createElement("div",{style:{fontSize:"20px",marginTop:"4px"}},s.icon),e.createElement("div",{style:{flex:1}},e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"8px"}},e.createElement(ve,{strong:!0},s.label),s.critical&&e.createElement(Sn,{count:"Critique",style:{backgroundColor:"#ff4d4f"}})),e.createElement(Le,{style:{fontSize:"12px",margin:"0 0 12px 0",color:"#666"}},s.description),e.createElement(R,{checked:o.notification_categories[s.key],onChange:c=>M(s.key,c),size:"small",checkedChildren:"ON",unCheckedChildren:"OFF"})))))))),e.createElement(Y,{title:e.createElement(Ee,null,e.createElement(ze,null),e.createElement("span",null,"Seuils de Priorité")),style:{marginBottom:"24px"}},e.createElement(qe,{message:"Information",description:"Les notifications critiques ne peuvent pas être désactivées pour des raisons de sécurité et de conformité.",type:"info",showIcon:!0,style:{marginBottom:"16px"}}),e.createElement(B,{gutter:[16,16]},d.map(s=>e.createElement(N,{xs:24,md:12,lg:6,key:s.key},e.createElement(Y,{size:"small",style:{height:"100%",borderColor:s.color}},e.createElement("div",{style:{textAlign:"center"}},e.createElement("div",{style:{fontSize:"24px",marginBottom:"8px"}},s.icon),e.createElement(ve,{strong:!0,style:{color:s.color}},s.label),e.createElement(Le,{style:{fontSize:"12px",margin:"8px 0",color:"#666"}},s.description),e.createElement(R,{checked:o.priority_thresholds[s.key],onChange:c=>C(s.key,c),disabled:s.disabled,size:"small",checkedChildren:"ON",unCheckedChildren:"OFF"}))))))),e.createElement(Y,null,e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"}},e.createElement(Z,{icon:e.createElement(St,null),onClick:D,loading:r},"Réinitialiser aux valeurs par défaut"),e.createElement(Ee,null,e.createElement(Z,{onClick:y,disabled:r},"Annuler"),e.createElement(Z,{type:"primary",icon:e.createElement(be,null),onClick:I,loading:r,disabled:!f},"Sauvegarder les préférences"))),f&&e.createElement(qe,{message:"Modifications non sauvegardées",description:"Vous avez des modifications non sauvegardées. N'oubliez pas de sauvegarder vos préférences.",type:"warning",showIcon:!0,style:{marginTop:"16px"}}))):e.createElement(qe,{message:"Erreur",description:"Impossible de charger les préférences de notification",type:"error",showIcon:!0,action:e.createElement(Z,{size:"small",onClick:y},"Réessayer")})},{Title:V,Text:pt}=bt,{TabPane:ne}=Nt,{Option:k}=J,ga=()=>{const{settings:t,loading:n,updateSetting:a,updateSettings:r,testEmailSettings:l,loadEmailSettings:o,loadShiftSettings:g,loadReportSettings:f}=Nn(),{darkMode:p,toggleDarkMode:h}=In(),[d]=S.useForm(),[E,y]=i.useState("interface"),[I,D]=i.useState(!1),[M,C]=i.useState(!1);i.useEffect(()=>{n||d.setFieldsValue(t)},[d,t,n]),i.useEffect(()=>{E==="email"?o():E==="shift"?g():E==="reports"&&f()},[E,o,g,f]);const $=x=>{y(x)},O=async x=>{D(!0);try{await r(x)&&G.success("Paramètres enregistrés avec succès")}finally{D(!1)}},s=async()=>{C(!0);try{await l()}finally{C(!1)}},c=x=>{h(),a("darkMode",x)};return n?e.createElement(Y,{loading:!0,style:{margin:"24px"}},e.createElement("div",{style:{height:"400px"}})):e.createElement(Y,{title:e.createElement(Ee,null,e.createElement(be,null),e.createElement("span",null,"Paramètres")),style:{margin:"24px"}},e.createElement(S,{form:d,layout:"vertical",initialValues:t,onFinish:O},e.createElement(Nt,{activeKey:E,onChange:$},e.createElement(ne,{tab:e.createElement("span",null,e.createElement(be,null)," Interface"),key:"interface"},e.createElement(V,{level:4},"Apparence et comportement"),e.createElement(B,{gutter:24},e.createElement(N,{xs:24,md:12},e.createElement(S.Item,{name:"darkMode",label:"Mode sombre",valuePropName:"checked"},e.createElement(R,{checked:p,onChange:c}))),e.createElement(N,{xs:24,md:12},e.createElement(S.Item,{name:"compactMode",label:"Mode compact",valuePropName:"checked"},e.createElement(R,null)))),e.createElement(B,{gutter:24},e.createElement(N,{xs:24,md:12},e.createElement(S.Item,{name:"animationsEnabled",label:"Animations de l'interface",valuePropName:"checked"},e.createElement(R,null))),e.createElement(N,{xs:24,md:12},e.createElement(S.Item,{name:"chartAnimations",label:"Animations des graphiques",valuePropName:"checked"},e.createElement(R,null)))),e.createElement(ge,null),e.createElement(V,{level:4},"Affichage des données"),e.createElement(B,{gutter:24},e.createElement(N,{xs:24,md:12},e.createElement(S.Item,{name:"dataDisplayMode",label:"Mode d'affichage par défaut"},e.createElement(J,null,e.createElement(k,{value:"chart"},"Graphiques"),e.createElement(k,{value:"table"},"Tableaux"),e.createElement(k,{value:"mixed"},"Mixte")))),e.createElement(N,{xs:24,md:12},e.createElement(S.Item,{name:"dashboardRefreshRate",label:"Taux de rafraîchissement du tableau de bord (secondes)"},e.createElement(We,{min:10,max:300})))),e.createElement(B,{gutter:24},e.createElement(N,{xs:24,md:12},e.createElement(S.Item,{name:"defaultView",label:"Vue par défaut"},e.createElement(J,null,e.createElement(k,{value:"dashboard"},"Tableau de bord"),e.createElement(k,{value:"production"},"Production"),e.createElement(k,{value:"arrets"},"Arrêts"),e.createElement(k,{value:"reports"},"Rapports")))),e.createElement(N,{xs:24,md:12},e.createElement(S.Item,{name:"tableRowsPerPage",label:"Lignes par page dans les tableaux"},e.createElement(J,null,e.createElement(k,{value:10},"10"),e.createElement(k,{value:20},"20"),e.createElement(k,{value:50},"50"),e.createElement(k,{value:100},"100")))))),e.createElement(ne,{tab:e.createElement("span",null,e.createElement($e,null)," Notifications"),key:"notifications"},e.createElement(sa,null)),e.createElement(ne,{tab:e.createElement("span",null,e.createElement(yt,null)," Email"),key:"email"},e.createElement(V,{level:4},"Notifications par email"),e.createElement(S.Item,{name:"emailNotifications",label:"Activer les notifications par email",valuePropName:"checked"},e.createElement(R,null)),e.createElement(B,{gutter:24},e.createElement(N,{xs:24,md:12},e.createElement(S.Item,{name:"emailFormat",label:"Format des emails"},e.createElement(J,null,e.createElement(k,{value:"html"},"HTML"),e.createElement(k,{value:"text"},"Texte brut")))),e.createElement(N,{xs:24,md:12},e.createElement(S.Item,{name:"emailDigest",label:"Recevoir un résumé quotidien",valuePropName:"checked"},e.createElement(R,null)))),e.createElement(ge,null),e.createElement(Z,{type:"primary",icon:e.createElement(ia,null),onClick:s,loading:M},"Tester les paramètres d'email")),e.createElement(ne,{tab:e.createElement("span",null,e.createElement($n,null)," Rapports de quart"),key:"shift"},e.createElement(V,{level:4},"Paramètres des rapports de quart"),e.createElement(B,{gutter:24},e.createElement(N,{xs:24,md:12},e.createElement(S.Item,{name:"defaultShift",label:"Quart par défaut"},e.createElement(J,null,e.createElement(k,{value:"Matin"},"Matin (06:00 - 14:00)"),e.createElement(k,{value:"Après-midi"},"Après-midi (14:00 - 22:00)"),e.createElement(k,{value:"Nuit"},"Nuit (22:00 - 06:00)")))),e.createElement(N,{xs:24,md:12},e.createElement(S.Item,{name:"shiftReportNotifications",label:"Notifications pour les rapports de quart",valuePropName:"checked"},e.createElement(R,null)))),e.createElement(S.Item,{name:"shiftReportEmails",label:"Recevoir les rapports de quart par email",valuePropName:"checked"},e.createElement(R,null)),e.createElement(ge,null),e.createElement(V,{level:4},"Paramètres par quart"),e.createElement(B,{gutter:24},e.createElement(N,{xs:24,md:8},e.createElement(V,{level:5},"Matin"),e.createElement(S.Item,{name:"shift1Notifications",label:"Notifications",valuePropName:"checked"},e.createElement(R,null)),e.createElement(S.Item,{name:"shift1Emails",label:"Emails",valuePropName:"checked"},e.createElement(R,null))),e.createElement(N,{xs:24,md:8},e.createElement(V,{level:5},"Après-midi"),e.createElement(S.Item,{name:"shift2Notifications",label:"Notifications",valuePropName:"checked"},e.createElement(R,null)),e.createElement(S.Item,{name:"shift2Emails",label:"Emails",valuePropName:"checked"},e.createElement(R,null))),e.createElement(N,{xs:24,md:8},e.createElement(V,{level:5},"Nuit"),e.createElement(S.Item,{name:"shift3Notifications",label:"Notifications",valuePropName:"checked"},e.createElement(R,null)),e.createElement(S.Item,{name:"shift3Emails",label:"Emails",valuePropName:"checked"},e.createElement(R,null))))),e.createElement(ne,{tab:e.createElement("span",null,e.createElement(Rn,null)," Rapports"),key:"reports"},e.createElement(V,{level:4},"Paramètres des rapports"),e.createElement(B,{gutter:24},e.createElement(N,{xs:24,md:12},e.createElement(S.Item,{name:"defaultReportFormat",label:"Format de rapport par défaut"},e.createElement(J,null,e.createElement(k,{value:"pdf"},"PDF"),e.createElement(k,{value:"excel"},"Excel"),e.createElement(k,{value:"csv"},"CSV")))),e.createElement(N,{xs:24,md:12},e.createElement(S.Item,{name:"reportAutoDownload",label:"Téléchargement automatique des rapports",valuePropName:"checked"},e.createElement(R,null))))),e.createElement(ne,{tab:e.createElement("span",null,e.createElement(aa,null)," Sécurité"),key:"security"},e.createElement(V,{level:4},"Paramètres de sécurité"),e.createElement(B,{gutter:24},e.createElement(N,{xs:24,md:12},e.createElement(S.Item,{name:"sessionTimeout",label:"Délai d'expiration de session (minutes)"},e.createElement(We,{min:5,max:240}))),e.createElement(N,{xs:24,md:12},e.createElement(S.Item,{name:"loginNotifications",label:"Notifications de connexion",valuePropName:"checked"},e.createElement(R,null)))),e.createElement(S.Item,{name:"twoFactorAuth",label:"Authentification à deux facteurs",valuePropName:"checked"},e.createElement(R,null)),e.createElement(pt,{type:"secondary"},"L'authentification à deux facteurs ajoute une couche de sécurité supplémentaire à votre compte.")),e.createElement(ne,{tab:e.createElement("span",null,e.createElement(xn,null)," Profil"),key:"profile"},e.createElement(V,{level:4},"Paramètres du profil"),e.createElement(pt,null,"Les paramètres du profil sont gérés dans la page de profil utilisateur."),e.createElement(ge,null),e.createElement(Z,{type:"primary",href:"/profile"},"Accéder à mon profil"))),e.createElement(ge,null),e.createElement(B,{justify:"end",gutter:16},e.createElement(N,null,e.createElement(Z,{icon:e.createElement(St,null),onClick:()=>d.resetFields()},"Réinitialiser")),e.createElement(N,null,e.createElement(Z,{type:"primary",icon:e.createElement(Cn,null),htmlType:"submit",loading:I},"Enregistrer")))))};export{ga as default};
