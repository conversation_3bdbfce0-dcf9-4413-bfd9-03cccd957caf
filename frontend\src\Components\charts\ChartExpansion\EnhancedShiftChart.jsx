import React, { memo, useMemo } from "react";
import {
  <PERSON>sponsive<PERSON><PERSON>r,
  Bar<PERSON>hart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
} from "recharts";
import { Empty } from "antd";
import { useDynamicChartConfig } from "../../../hooks/useUnifiedChartConfig";
import SOMIPEM_COLORS from "../../../styles/brand-colors";

/**
 * Enhanced Dynamic Shift Chart - Can render as Bar or Line based on settings
 * Replaces EnhancedShiftBarChart with dynamic chart type support
 */
const EnhancedShiftChart = memo(({
  data,
  title,
  dataKey,
  color,
  label = "Valeur",
  tooltipLabel,
  isKg = false,
  chartType: propChartType, // Override setting if provided
  allowedTypes = ['bar', 'line'], // Restrict which types are allowed
  enhanced = false,
  expanded = false,
  isModal = false,
  height = 300,
  ...otherProps
}) => {
  // Get unified chart configuration with dynamic type support
  const chartConfig = useDynamicChartConfig({
    fallbackType: 'bar',
    allowedTypes,
    propChartType
  });

  // Extract chart type from unified config
  const chartType = chartConfig.chartType;

  // Process and validate data
  const processedData = useMemo(() => {
    if (!Array.isArray(data) || data.length === 0) {
      return [];
    }

    return data.map(item => {
      // Handle different data structures
      const shiftValue = item.Shift || item.shift || item.name || item.label || 'N/A';
      const dataValue = item[dataKey] || 0;
      
      return {
        ...item,
        shift: shiftValue,
        [dataKey]: Number(dataValue) || 0,
        // Preserve original data structure
        originalData: item
      };
    }).filter(item => item[dataKey] !== undefined && item[dataKey] !== null);
  }, [data, dataKey]);

  // Custom tooltip formatter using unified config
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      const value = data.value;
      const formattedValue = isKg ? `${value.toLocaleString()} kg` : value.toLocaleString();
      const tooltipConfig = chartConfig.tooltipConfig;

      return (
        <div style={tooltipConfig.contentStyle}>
          <p style={{
            margin: 0,
            fontWeight: 'bold',
            color: chartConfig.getTextColor()
          }}>
            {`Équipe: ${label}`}
          </p>
          <p style={{
            margin: 0,
            color: color || chartConfig.getPrimaryColor()
          }}>
            {`${tooltipLabel || label}: ${formattedValue}`}
          </p>
        </div>
      );
    }
    return null;
  };

  // Prepare chart props using unified config
  const chartProps = {
    data: processedData,
    margin: chartConfig.margins,
    ...otherProps
  };

  // Render appropriate chart type using unified config
  const renderChart = () => {
    const finalColor = color || chartConfig.getPrimaryColor();

    switch (chartType) {
      case 'line':
        return (
          <LineChart {...chartProps}>
            <CartesianGrid {...chartConfig.gridConfig} />
            <XAxis
              dataKey="shift"
              {...chartConfig.axisConfig}
              tick={{ fontSize: 11 }}
            />
            <YAxis
              {...chartConfig.axisConfig}
              tick={{ fontSize: 11 }}
              label={{
                value: isKg ? 'Quantité (kg)' : 'Valeur',
                angle: -90,
                position: 'insideLeft',
                style: { textAnchor: 'middle' }
              }}
            />
            <RechartsTooltip content={<CustomTooltip />} />
            {chartConfig.displayConfig.showLegend && <Legend {...chartConfig.legendConfig} />}
            <Line
              type="monotone"
              dataKey={dataKey}
              name={tooltipLabel || title}
              {...chartConfig.getLineElementConfig(finalColor)}
            />
          </LineChart>
        );

      case 'bar':
      default:
        return (
          <BarChart {...chartProps}>
            <CartesianGrid {...chartConfig.gridConfig} />
            <XAxis
              dataKey="shift"
              {...chartConfig.axisConfig}
              tick={{ fontSize: 11 }}
            />
            <YAxis
              {...chartConfig.axisConfig}
              tick={{ fontSize: 11 }}
              label={{
                value: isKg ? 'Quantité (kg)' : 'Valeur',
                angle: -90,
                position: 'insideLeft',
                style: { textAnchor: 'middle' }
              }}
            />
            <RechartsTooltip content={<CustomTooltip />} />
            {chartConfig.displayConfig.showLegend && <Legend {...chartConfig.legendConfig} />}
            <Bar
              dataKey={dataKey}
              name={tooltipLabel || title}
              {...chartConfig.getBarElementConfig(finalColor)}
              maxBarSize={enhanced || expanded ? 60 : 40}
            />
          </BarChart>
        );
    }
  };

  // Handle loading state
  if (!processedData || processedData.length === 0) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: height,
        width: '100%'
      }}>
        <Empty 
          description="Aucune donnée disponible pour les équipes"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </div>
    );
  }

  return (
    <div style={{ width: '100%', height: height }}>
      <ResponsiveContainer {...chartConfig.responsiveContainerProps}>
        {renderChart()}
      </ResponsiveContainer>
    </div>
  );
});

EnhancedShiftChart.displayName = 'EnhancedShiftChart';

export default EnhancedShiftChart;
