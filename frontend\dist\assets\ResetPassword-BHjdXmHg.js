import{aF as s,aZ as T,y as C,r as n,A as P,v as V,R as e,ag as z,N as l,ab as S,V as F,az as g,M as y}from"./index-gs31pxOi.js";/* empty css              */import{R as v}from"./index-CpioM9_i.js";const{Title:I,Text:L}=F,j=()=>{const[E]=s.useForm(),{token:o}=T(),c=C(),[k,d]=n.useState(!1),[b,m]=n.useState(!0),[h,u]=n.useState(null),[w,x]=n.useState(!1),{resetPassword:N,verifyResetToken:p}=P(),{darkMode:t}=V();n.useEffect(()=>{o&&(async()=>{m(!0);try{const r=await p(o);u(r.success)}catch(r){console.error("Error verifying token:",r),u(!1)}finally{m(!1)}})()},[o,p]);const R=async i=>{d(!0);try{(await N(o,i.password)).success&&x(!0)}finally{d(!1)}},a={container:{background:t?"linear-gradient(135deg, #1f1f1f 0%, #141414 100%)":"linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)"},card:{backgroundColor:t?"#1f1f1f":"#ffffff",boxShadow:t?"0 12px 40px rgba(0, 0, 0, 0.5)":"0 12px 40px rgba(0, 0, 0, 0.15)"},title:{color:t?"rgba(255, 255, 255, 0.85)":"#2c3e50"},input:{backgroundColor:t?"#141414":"#ffffff",borderColor:t?"#434343":"#e8e8e8",color:t?"rgba(255, 255, 255, 0.85)":"rgba(0, 0, 0, 0.85)"}};return b?e.createElement("div",{className:`login-container ${t?"dark":"light"}`,style:a.container},e.createElement("div",{className:"centered-wrapper",style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh"}},e.createElement(z,{size:"large",tip:"Vérification du lien de réinitialisation..."}))):w?e.createElement("div",{className:`login-container ${t?"dark":"light"}`,style:a.container},e.createElement("div",{className:"centered-wrapper"},e.createElement(v,{status:"success",title:"Réinitialisation du mot de passe réussie!",subTitle:"Vous pouvez maintenant vous connecter avec votre nouveau mot de passe.",extra:[e.createElement(l,{type:"primary",key:"login",onClick:()=>c("/login")},"Aller à la page de connexion")]}))):h===!1?e.createElement("div",{className:`login-container ${t?"dark":"light"}`,style:a.container},e.createElement("div",{className:"centered-wrapper"},e.createElement(v,{status:"error",title:"Lien invalide ou expiré",subTitle:"Le lien de réinitialisation du mot de passe est invalide ou a expiré.",extra:[e.createElement(l,{type:"primary",key:"login",onClick:()=>c("/login")},"Retour à la page de connexion")]}))):e.createElement("div",{className:`login-container ${t?"dark":"light"}`,style:a.container},e.createElement("div",{className:"centered-wrapper"},e.createElement(S,{className:"login-card",style:a.card,hoverable:!0},e.createElement("div",{className:"decorative-line"}),e.createElement(I,{level:3,style:a.title},"Réinitialisation du mot de passe"),e.createElement(L,{type:"secondary",style:{display:"block",marginBottom:24}},"Veuillez entrer votre nouveau mot de passe"),e.createElement(s,{form:E,name:"resetPassword",onFinish:R,layout:"vertical",size:"large"},e.createElement(s.Item,{name:"password",rules:[{required:!0,message:"Veuillez entrer votre nouveau mot de passe"},{min:8,message:"Le mot de passe doit contenir au moins 8 caractères"}],hasFeedback:!0},e.createElement(g.Password,{prefix:e.createElement(y,null),placeholder:"Nouveau mot de passe",style:a.input})),e.createElement(s.Item,{name:"confirmPassword",dependencies:["password"],hasFeedback:!0,rules:[{required:!0,message:"Veuillez confirmer votre mot de passe"},({getFieldValue:i})=>({validator(r,f){return!f||i("password")===f?Promise.resolve():Promise.reject(new Error("Les deux mots de passe ne correspondent pas"))}})]},e.createElement(g.Password,{prefix:e.createElement(y,null),placeholder:"Confirmer le mot de passe",style:a.input})),e.createElement(s.Item,null,e.createElement(l,{type:"primary",htmlType:"submit",block:!0,loading:k},"Réinitialiser le mot de passe"))))))};export{j as default};
