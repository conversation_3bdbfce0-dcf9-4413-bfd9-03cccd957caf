import React, { memo } from 'react';
import { Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, Line, XAxis, <PERSON>A<PERSON><PERSON>, CartesianGrid, Toolt<PERSON> } from 'recharts';
import { Empty, Spin, Typography } from 'antd';
import { useEnhancedRechartsConfig } from '../../../utils/enhancedRechartsConfig';
import { useSettings } from '../../../hooks/useSettings';

const { Text } = Typography;

const CHART_COLORS = {
  primary: "#1890ff",
  secondary: "#13c2c2", 
  success: "#52c41a",
  warning: "#faad14",
  danger: "#f5222d",
  purple: "#722ed1",
  pink: "#eb2f96",
  orange: "#fa8c16",
  cyan: "#13c2c2",
  lime: "#a0d911",
};

const ArretLineChart = memo(({ data, loading = false }) => {
  // Get settings for enhanced chart configuration
  const { settings, charts, theme } = useSettings();

  // Enhanced chart configuration
  const enhancedChartConfig = useEnhancedRechartsConfig({
    charts,
    theme
  });

  console.log('📈 ArretLineChart received data:', {
    dataType: typeof data,
    isArray: Array.isArray(data),
    dataLength: Array.isArray(data) ? data.length : 'N/A',
    firstItem: Array.isArray(data) && data[0] ? data[0] : 'N/A',
    data: data
  });

  // ENHANCED DEBUG LOGGING
  console.log('🔍 ArretLineChart DETAILED DEBUG:', {
    rawDataType: typeof data,
    rawDataValue: data,
    isUndefined: data === undefined,
    isNull: data === null,
    isEmptyArray: Array.isArray(data) && data.length === 0,
    hasData: Array.isArray(data) && data.length > 0,
    firstItemStructure: Array.isArray(data) && data[0] ? Object.keys(data[0]) : 'N/A'
  });

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100%',
        flexDirection: 'column',
        gap: '16px'
      }}>
        <Spin size="large" />
        <Text type="secondary">Chargement de l'évolution des arrêts...</Text>
      </div>
    );}
  
  // Check if we have proper evolution data (time-series format)
  const hasEvolutionData = Array.isArray(data) && data.length > 0;
  
  console.log('📈 ArretLineChart validation:', {
    isArray: Array.isArray(data),
    hasLength: Array.isArray(data) ? data.length > 0 : false,
    hasDateField: Array.isArray(data) && data[0] ? !!data[0].date : false,
    hasStopsField: Array.isArray(data) && data[0] ? !!data[0].stops : false,
    hasDisplayDateField: Array.isArray(data) && data[0] ? !!data[0].displayDate : false,
    hasEvolutionData,
    sampleData: Array.isArray(data) ? data.slice(0, 2) : null
  });

  

  if (!hasEvolutionData || data.length === 0) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100%' 
      }}>
        <Empty 
          description="Aucune donnée d'évolution disponible"
          style={{ color: '#8c8c8c' }}
        />
      </div>
    );
  }

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div style={{
          backgroundColor: '#fff',
          border: '1px solid #f0f0f0',
          borderRadius: '6px',
          padding: '12px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
        }}>          <p style={{ margin: 0, fontWeight: 'bold', color: '#262626' }}>
            {`Date: ${label}`}
          </p>
          {payload.map((entry, index) => (
            <p key={index} style={{ margin: '4px 0', color: entry.color }}>
              {`${entry.name}: ${entry.value}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };
  return (
    <div style={{ height: '100%', width: '100%' }}>
      <div style={{ 
        textAlign: 'center', 
        marginBottom: '16px',
        fontSize: '16px',
        fontWeight: '600',
        color: CHART_COLORS.primary
      }}>
        Évolution du Nombre d'Arrêts
      </div>
      
      <div style={{ height: 'calc(100% - 40px)' }}>
        <ResponsiveContainer {...enhancedChartConfig.getResponsiveContainerProps()}>
          <LineChart data={data} margin={enhancedChartConfig.getChartMargins()}>
            <CartesianGrid {...enhancedChartConfig.getGridConfig()} />
            <XAxis
              dataKey="displayDate"
              {...enhancedChartConfig.getAxisConfig()}
              tick={{ fill: "#666", fontSize: 10, angle: -45, textAnchor: "end" }}
              height={60}
              interval={0}
            />
            <YAxis
              {...enhancedChartConfig.getAxisConfig()}
              stroke="#666"
              fontSize={10}
              label={{
                value: "Nombre d'Arrêts",
                angle: -90,
                position: "insideLeft",
                style: { fill: "#666", fontSize: 12 }
              }}
            />
            <Tooltip {...enhancedChartConfig.getTooltipConfig()} content={<CustomTooltip />} />
            <Line
              type="monotone"
              dataKey="stops"
              name="Nombre d'arrêts"
              {...enhancedChartConfig.getLineElementConfig(CHART_COLORS.primary)}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
});

ArretLineChart.displayName = 'ArretLineChart';

export default ArretLineChart;
