import React, { memo } from 'react';
import { Spin, Empty } from 'antd';
import { Respons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Pie, Cell, Tooltip } from 'recharts';
import { AlertOutlined } from '@ant-design/icons';
import SOMIPEM_COLORS from '../../../styles/brand-colors';
import { formatFrenchNumber, formatFrenchInteger, formatFrenchPercentage } from '../../../utils/numberFormatter';
import { useUnifiedChartConfig } from '../../../hooks/useUnifiedChartConfig';

const COLORS = [
  SOMIPEM_COLORS.PRIMARY_BLUE,    // #1E3A8A - Primary Blue for most critical
  SOMIPEM_COLORS.SECONDARY_BLUE,  // #3B82F6 - Secondary Blue for important
  SOMIPEM_COLORS.DARK_GRAY,       // #1F2937 - Dark Gray for significant
  SOMIPEM_COLORS.LIGHT_GRAY,      // #6B7280 - <PERSON> Gray for moderate
  "#9CA3AF",                       // Medium Gray (between light and dark)
  "#D1D5DB",                       // Lighter Gray for low priority
  "#E5E7EB",                       // Very Light Gray
  "#F3F4F6",                       // Subtle Gray
  "#60A5FA",                       // Light Blue variant
  "#1D4ED8"                        // Darker Blue variant
];

const ArretPieChart = memo(({ data = [], loading }) => {
  // Get unified chart configuration
  const chartConfig = useUnifiedChartConfig({
    chartType: 'pie'
  });

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <Spin size="large" />
      </div>
    );
  }  // Ensure data is an array - handle both direct arrays and response objects
  const safeData = Array.isArray(data) ? data : (data?.data || []);

  // Check if data is empty
  if (!safeData || safeData.length === 0) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', flexDirection: 'column' }}>
        <Empty 
          description="Aucune donnée d'arrêts disponible" 
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </div>
    );
  }  // Calculate total and percentages using the correct safeData
  const total = safeData.reduce((sum, item) => sum + (item.count || 0), 0);
  
  if (total === 0) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', flexDirection: 'column' }}>
        <Empty 
          description="Aucun arrêt enregistré" 
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </div>
    );
  }  const processedData = safeData.map((item, index) => ({
    ...item,
    percentage: ((item.count / total) * 100),
    color: chartConfig.colors[index % chartConfig.colors.length],
    name: item.reason || item.stopName || item.Stop_Reason || 'Type non défini',
    count: item.count || item.frequency || 0
  })).sort((a, b) => b.count - a.count);

  const criticalThreshold = 30; // Percentage threshold for critical types
  const criticalTypes = processedData.filter(item => item.percentage >= criticalThreshold);  return (
    <div style={{ height: '100%', padding: '16px', background: 'transparent' }}>
      {/* Title */}
      <div style={{ 
        textAlign: 'center', 
        marginBottom: '20px',
        fontSize: '18px',
        fontWeight: '600',
        color: SOMIPEM_COLORS.PRIMARY_BLUE, // Updated to SOMIPEM Primary Blue
        letterSpacing: '0.3px'
      }}>
        Top 5 Causes d'Arrêts
      </div>
      
      {/* Main Container - Responsive Grid Layout */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: '1fr 350px',
        gap: '24px',
        height: 'calc(100% - 60px)',
        alignItems: 'stretch'
      }}>
        {/* Left: Pie Chart */}
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          position: 'relative',
          background: '#ffffff',
          borderRadius: '16px',
          padding: '20px',
          boxShadow: '0 4px 16px rgba(0,0,0,0.06)',
          border: '1px solid #f0f0f0'
        }}>
          <ResponsiveContainer {...chartConfig.responsiveContainerProps}>
            <PieChart>
              <Pie
                data={processedData}
                dataKey="count"
                nameKey="name"
                cx="50%"
                cy="50%"
                outerRadius={120}
                innerRadius={55}
                paddingAngle={2}
                stroke="#fff"
                strokeWidth={2}
                label={chartConfig.displayConfig.showDataLabels ? ({ name, percentage }) => `${name}: ${formatFrenchPercentage(percentage / 100)}` : false}
                labelLine={false}
                {...chartConfig.animationConfig}
              >
                {processedData.map((entry, index) => {
                  const hoverConfig = chartConfig.hoverEffectsConfig;
                  return (
                    <Cell
                      key={`cell-${index}`}
                      fill={entry.color}
                      style={{
                        filter: 'drop-shadow(0 1px 3px rgba(0,0,0,0.1))',
                        cursor: hoverConfig.cursor,
                        transition: 'all 0.2s ease'
                      }}
                    />
                  );
                })}
              </Pie>
              <Tooltip
                {...chartConfig.tooltipConfig}
                contentStyle={{
                  ...chartConfig.tooltipConfig.contentStyle,
                  backgroundColor: '#fff',
                  border: `1px solid ${chartConfig.getPrimaryColor()}`,
                  borderRadius: '8px',
                  boxShadow: '0 4px 16px rgba(0,0,0,0.1)',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: chartConfig.getTextColor()
                }}
                formatter={(value, name, props) => [
                  [`${formatFrenchInteger(value)} arrêts (${formatFrenchPercentage(props.payload.percentage / 100)})`, ''],
                  name
                ]}
              />
            </PieChart>
          </ResponsiveContainer>
          
          {/* Center Total - Adjusted Size */}
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            textAlign: 'center',
            backgroundColor: '#fff',
            borderRadius: '50%',
            width: '110px',
            height: '110px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            boxShadow: '0 4px 16px rgba(0,0,0,0.1)',
            border: `2px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}20` // SOMIPEM Primary Blue with transparency
          }}>
            <div style={{ 
              fontSize: '28px', 
              fontWeight: '700', 
              color: SOMIPEM_COLORS.PRIMARY_BLUE, // Updated to SOMIPEM Primary Blue
              lineHeight: '1'
            }}>
              {total}
            </div>
            <div style={{ 
              fontSize: '12px', 
              color: SOMIPEM_COLORS.LIGHT_GRAY, // Updated to SOMIPEM Light Gray
              marginTop: '2px',
              fontWeight: '600',
              letterSpacing: '0.5px'
            }}>
              TOTAL
            </div>
          </div>
        </div>

        {/* Right: Legend & Alert */}
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '16px',
          height: '100%'
        }}>
          {/* Legend */}
          <div style={{
            background: '#ffffff',
            borderRadius: '12px',
            padding: '16px',
            border: '1px solid #f0f0f0',
            boxShadow: '0 2px 8px rgba(0,0,0,0.04)',
            flex: '1'
          }}>
            <h3 style={{
              margin: '0 0 16px 0',
              fontSize: '14px',
              fontWeight: '600',
              color: SOMIPEM_COLORS.DARK_GRAY, // Updated to SOMIPEM Dark Gray
              display: 'flex',
              alignItems: 'center',
              gap: '6px'
            }}>
              <div style={{
                width: '3px',
                height: '16px',
                background: `linear-gradient(135deg, ${SOMIPEM_COLORS.PRIMARY_BLUE}, ${SOMIPEM_COLORS.SECONDARY_BLUE})`, // SOMIPEM gradient
                borderRadius: '2px'
              }}></div>
              Répartition
            </h3>
            
            <div style={{ 
              display: 'flex',
              flexDirection: 'column',
              gap: '8px',
              maxHeight: '320px',
              overflowY: 'auto'
            }}>
              {processedData.map((item, index) => (
                <div 
                  key={index}
                  style={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    justifyContent: 'space-between',
                    padding: '12px',
                    background: '#fafafa',
                    borderRadius: '8px',
                    border: '1px solid #f0f0f0',
                    transition: 'all 0.2s ease',
                    cursor: 'pointer'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.background = '#f0f9ff';
                    e.target.style.borderColor = SOMIPEM_COLORS.PRIMARY_BLUE; // SOMIPEM Primary Blue hover
                    e.target.style.transform = 'translateY(-1px)';
                    e.target.style.boxShadow = `0 2px 8px ${SOMIPEM_COLORS.PRIMARY_BLUE}20`; // SOMIPEM Primary Blue shadow
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.background = '#fafafa';
                    e.target.style.borderColor = '#f0f0f0';
                    e.target.style.transform = 'translateY(0)';
                    e.target.style.boxShadow = 'none';
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', flex: '1' }}>
                    <div style={{
                      width: '10px',
                      height: '10px',
                      backgroundColor: item.color,
                      borderRadius: '50%',
                      marginRight: '10px',
                      boxShadow: `0 0 0 2px ${item.color}20`,
                      border: '1px solid #fff'
                    }} />
                    <div>
                      <div style={{ 
                        fontSize: '13px',
                        color: SOMIPEM_COLORS.DARK_GRAY, // Updated to SOMIPEM Dark Gray
                        fontWeight: '500',
                        marginBottom: '1px'
                      }}>
                        {item.name}
                      </div>
                      <div style={{ 
                        fontSize: '11px',
                        color: SOMIPEM_COLORS.LIGHT_GRAY // Updated to SOMIPEM Light Gray
                      }}>
                        {item.count} arrêts
                      </div>
                    </div>
                  </div>
                  
                  <div style={{
                    fontSize: '14px',
                    color: SOMIPEM_COLORS.DARK_GRAY, // Updated to SOMIPEM Dark Gray
                    fontWeight: '600'
                  }}>
                    {formatFrenchPercentage(item.percentage / 100)}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Critical Alert - Compact */}
          {criticalTypes.length > 0 && (
            <div style={{
              background: 'linear-gradient(135deg, #fff2f0, #ffebe8)',
              borderRadius: '12px',
              padding: '16px',
              border: '1px solid #ffccc7',
              boxShadow: '0 2px 8px rgba(245, 34, 45, 0.08)'
            }}>
              <div style={{ 
                display: 'flex', 
                alignItems: 'center',
                marginBottom: '8px'
              }}>
                <div style={{
                  width: '28px',
                  height: '28px',
                  borderRadius: '50%',
                  background: 'linear-gradient(135deg, #f5222d, #ff4d4f)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: '10px',
                  boxShadow: '0 2px 8px rgba(245, 34, 45, 0.25)'
                }}>
                  <AlertOutlined style={{ color: '#fff', fontSize: '14px' }} />
                </div>
                <div>
                  <h4 style={{
                    margin: 0,
                    fontSize: '13px',
                    fontWeight: '600',
                    color: '#f5222d'
                  }}>
                    Alerte Critique
                  </h4>
                  <p style={{
                    margin: '1px 0 0 0',
                    fontSize: '11px',
                    color: '#8c1b1b',
                    opacity: 0.8
                  }}>
                    {criticalTypes.length} type(s)  30%
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
});

export default ArretPieChart;
