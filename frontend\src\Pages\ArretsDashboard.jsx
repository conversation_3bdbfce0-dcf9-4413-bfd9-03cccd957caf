import React, { useCallback, useEffect, useRef } from 'react';
import { Layout, Typography } from 'antd';
import { ArretQueuedProvider, useArretQueuedContext } from '../context/arret/ArretQueuedContext.jsx';
import LazyComponentWrapper from '../Components/common/LazyComponentWrapper';
import LazyArretComponent from '../Components/common/LazyArretComponent.jsx';
import ArretHeader from '../Components/arrets/ArretHeader';
import ArretStatsCards from '../Components/arrets/ArretStatsCards';
import ArretFilters from '../Components/arrets/ArretFilters';
import ArretChartsSection from '../Components/arrets/ArretChartsSection';
import ArretDataTable from '../Components/arrets/ArretDataTable';
import ArretSearchModal from '../Components/arrets/ArretSearchModal';

import ArretPerformanceMetrics from '../Components/arrets/ArretPerformanceMetrics';
import ArretErrorBoundary from '../Components/arrets/ArretErrorBoundary';
import SOMIPEM_COLORS from '../styles/brand-colors';
import { useSettings } from '../hooks/useSettings';

const { Content } = Layout;

// Internal component that uses context (must be inside ArretQueuedProvider)
const ArretsDashboardContent = () => {
  // Get settings for enhanced chart configuration
  const { settings } = useSettings();

  // Get all data from context
  const context = useArretQueuedContext();
  
  // Handle filter changes - must be after context but before conditional returns
  const handleFilterChange = useCallback((newFilters) => {
    // Detect triple filter scenario for enhanced logging
    const hasAllFilters = newFilters?.model && newFilters?.machine && newFilters?.dateFilterActive;
    
    
    // Additional performance monitoring for triple filter scenario
    if (hasAllFilters) {
      
      // Set a timeout to detect if the page freezes
      const freezeDetectionTimeout = setTimeout(() => {
        console.error('🚨 Potential freeze detected - page unresponsive for 10 seconds with triple filters');
        
        // No notification needed as requested by user
        console.warn('Performance issue detected with triple filters');
      }, 10000);
      
      // Clear timeout when component updates (indicates no freeze)
      const clearFreezeDetection = () => {
        clearTimeout(freezeDetectionTimeout);
      };
      
      // Store cleanup function
      window.clearFreezeDetection = clearFreezeDetection;
      
      // For triple filters, ensure the GraphQL cache is primed
      if (context && context.graphQL) {
        // Check cache stats to see if we have a cache for this filter combination
        const cacheStats = context.graphQL.getCacheStats && context.graphQL.getCacheStats();
      }
    }
    
    // Filter changes are handled by the context itself
    // Context already handles all filter state updates through its own handlers
  }, [context]);

  if (!context) {
    return <div>Chargement du contexte...</div>;
  }  const { 
    loading,
    essentialLoading,
    detailedLoading,
    complexFilterLoading, // New loading state for triple filters
    error,
    totalStops,
    undeclaredStops,
    avgDuration,
    totalDuration,
    sidebarStats,
    arretStats,
    topStopsData,
    arretsByRange,
    stopReasons,
    stopsData,
    selectedMachine,
    selectedMachineModel,
    selectedDate,
    dateRangeType,
    dateFilterActive,
    handleRefresh
  } = context;

  // Debug logging for filter state with enhanced triple filter detection
  React.useEffect(() => {
    const hasAllFilters = selectedMachineModel && selectedMachine && selectedDate;
    
  
    
    // Performance monitoring for triple filter scenario
    if (hasAllFilters) {
      
      // Clear any existing freeze detection
      if (window.clearFreezeDetection) {
        window.clearFreezeDetection();
      }
      
      // Monitor for responsiveness
      const startTime = performance.now();
      
      setTimeout(() => {
        const renderTime = performance.now() - startTime;
        if (renderTime > 1000) {
          console.warn(`🐌 Slow render detected: ${renderTime.toFixed(2)}ms with triple filters`);
          
          // Log cache stats if available
          if (context.graphQL && context.graphQL.getCacheStats) {
            const cacheStats = context.graphQL.getCacheStats();
          }
        }
      }, 0);
    }
  }, [selectedMachineModel, selectedMachine, selectedDate, dateRangeType, dateFilterActive, stopsData?.length, loading, essentialLoading, detailedLoading, complexFilterLoading, context.graphQL]);

  // Show error message if there's an error
  if (error) {
    return (
      <Layout style={{ minHeight: '100vh', background: '#f0f2f5' }}>
        <Content style={{ padding: '24px' }}>
          <div style={{ maxWidth: '1400px', margin: '0 auto', textAlign: 'center', paddingTop: '50px' }}>
            <div style={{ 
              backgroundColor: '#FFFFFF', // White background
              border: `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}`, // Primary Blue border
              borderRadius: '8px', 
              padding: '24px',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)'
            }}>
              <h3 style={{ 
                color: SOMIPEM_COLORS.DARK_GRAY, // Dark Gray for title
                marginBottom: '16px'
              }}>Erreur de chargement</h3>
              <p style={{ 
                color: SOMIPEM_COLORS.LIGHT_GRAY, // Light Gray for description
                marginBottom: '20px'
              }}>{error}</p>
              <button 
                onClick={handleRefresh} 
                style={{ 
                  marginTop: '10px',
                  backgroundColor: SOMIPEM_COLORS.PRIMARY_BLUE, // Primary Blue button
                  color: '#FFFFFF',
                  border: 'none',
                  padding: '8px 16px',
                  borderRadius: '6px',
                  cursor: 'pointer'
                }}
              >
                Réessayer
              </button>
            </div>
          </div>        </Content>
      </Layout>
    );
  }
    // Use progressive loading states from context with triple filter awareness
  const coreLoading = essentialLoading || loading;
  const hasAllFilters = selectedMachineModel && selectedMachine && selectedDate;
  const isComplexScenario = hasAllFilters || complexFilterLoading;

  return (
    <Layout style={{ minHeight: '100vh', background: '#f0f2f5' }}>
      <Content style={{ padding: '24px' }}>
        <div style={{ maxWidth: '1400px', margin: '0 auto' }}>
          {/* Show special loading indicator for complex scenarios */}
          {isComplexScenario && (complexFilterLoading || loading) && (
            <div style={{ 
              position: 'fixed', 
              top: '20px', 
              right: '20px', 
              background: '#FFFFFF', // White background
              padding: '12px 20px', 
              borderRadius: '8px', 
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              zIndex: 1000,
              border: `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}` // Primary Blue border
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <div style={{ 
                  width: '16px', 
                  height: '16px', 
                  border: '2px solid #f3f3f3',
                  borderTop: `2px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}`, // Primary Blue spinner
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }}></div>
                <span style={{ 
                  fontSize: '14px', 
                  color: SOMIPEM_COLORS.DARK_GRAY // Dark Gray text
                }}>
                  Processing complex filters...
                </span>
              </div>
            </div>
          )}
          
          {/* Header Section - Priority 0 (immediate) */}
          <LazyArretComponent priority={0}>
            <ArretHeader />
          </LazyArretComponent>
          
          {/* Filters Section - Priority 0 (immediate) */}
          <LazyArretComponent priority={0}>
            <ArretFilters onFilterChange={handleFilterChange} />
          </LazyArretComponent>
          
          {/* Stats Cards - Priority 1 (fast) */}
          <LazyArretComponent 
            priority={1} 
            delay={100}
            height={120}
            loadingType="skeleton"
            title="Loading statistics..."
          >
            <ArretStatsCards loading={essentialLoading} />
          </LazyArretComponent>
          
          {/* Performance Metrics - Priority 2 (medium) */}
          {selectedMachine && (
            <LazyArretComponent 
              priority={2} 
              delay={200}
              height={180}
              loadingType="skeleton"
              title="Loading performance metrics..."
            >
              <ArretPerformanceMetrics loading={coreLoading} />
            </LazyArretComponent>
          )}
          
          {/* Charts Section - Priority 3 (slower, heavy component) */}
          <LazyArretComponent 
            priority={3} 
            delay={300}
            height={400}
            loadingType="skeleton"
            title="Loading charts..."
          >
            <ArretChartsSection loading={detailedLoading || (isComplexScenario && loading)} />
          </LazyArretComponent>
            
          {/* Data Table - Priority 4 (slowest, heaviest component) */}
          <LazyArretComponent 
            priority={4} 
            delay={400}
            height={500}
            loadingType="skeleton"
            title="Loading data table..."
          >
            <ArretDataTable loading={detailedLoading || (isComplexScenario && loading)} />
          </LazyArretComponent>
          
          {/* Search Modal */}
          <ArretSearchModal />
          

        </div>
      </Content>
      
      {/* Add CSS for spinner animation */}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </Layout>
  );
};

// Main component wrapper
const ArretsDashboard = () => {
  return (
    <ArretErrorBoundary>
      <ArretQueuedProvider>
        <ArretsDashboardContent />
      </ArretQueuedProvider>
    </ArretErrorBoundary>
  );
};

export default ArretsDashboard;
