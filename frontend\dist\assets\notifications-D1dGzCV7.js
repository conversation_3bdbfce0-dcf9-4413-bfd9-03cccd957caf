import{bj as Z,A as ee,v as te,r as s,$ as E,ah as i,R as t,ab as ae,U as w,ad as u,W as p,N as y,bk as M,a1 as re,F as ne,ae as b,bl as ie,bm as oe,bn as ce,ag as se,a5 as v,aA as R,b0 as le,V as ue,aj as S,a2 as z,aa as de,D as T,am as $,E as fe,bo as me}from"./index-gs31pxOi.js";import{u as pe}from"./useMobile-CILNziEN.js";import{R as ye}from"./ClockCircleOutlined-CiulfqLg.js";S.extend(me);S.locale("fr");const{Title:ke,Text:g}=ue,Ae=()=>{const{notifications:_,unreadCount:q,connectionStatus:Ee,connectionStats:ge,markAsRead:B,acknowledgeNotification:he,connect:j,isConnected:n,isConnecting:o,hasError:C,optimisticDeleteNotification:D,optimisticMarkAsRead:xe,optimisticMarkAllAsRead:P}=Z(),{user:we}=ee(),{darkMode:l}=te(),h=pe(),[d,F]=s.useState("all"),[L,k]=s.useState(!1),[be,A]=s.useState(null),[U,f]=s.useState([]),c=s.useCallback(async()=>{var e,r;k(!0),A(null);try{const a=await E.get("/api/notifications").withCredentials().timeout(3e4).retry(2);a.body&&Array.isArray(a.body)?f(a.body):(f([]),console.warn("Unexpected response structure from /api/notifications:",a.body))}catch(a){A(((r=(e=a==null?void 0:a.response)==null?void 0:e.data)==null?void 0:r.message)||a.message||"Failed to fetch notifications"),i.error("Erreur lors du chargement des notifications")}finally{k(!1)}},[]);s.useEffect(()=>{!n&&!o&&c()},[n,o,c]);const m=e=>!!(e.read_at||e.read),I=n?_:U,x=n?q:I.filter(e=>!m(e)).length;s.useEffect(()=>{C&&!o&&i.error("Connexion aux notifications interrompue. Tentative de reconnexion...")},[C,o]);const W=async e=>{try{n?await B(e):(f(r=>r.map(a=>a.id===e?{...a,read_at:new Date().toISOString(),read:!0}:a)),await E.patch(`/api/notifications/${e}/read`).withCredentials().send({}).set("withCredentials",!0).retry(2),i.success("Notification marquée comme lue"))}catch(r){console.error("Error marking notification as read:",r),i.error("Erreur lors de la mise à jour de la notification"),n||c()}},G=async()=>{try{n?P():f(e=>e.map(r=>({...r,read_at:new Date().toISOString(),read:!0}))),await E.patch("/api/notifications/read-all").withCredentials().send({}).set("withCredentials",!0).retry(2),i.success("Toutes les notifications ont été marquées comme lues"),n||c()}catch(e){console.error("Error marking all notifications as read:",e),i.error("Erreur lors de la mise à jour des notifications"),n||c()}},O=async e=>{try{n?D(e):f(r=>r.filter(a=>a.id!==e)),await E.delete(`/api/notifications/${e}`).set("withCredentials",!0).retry(2),i.success("Notification supprimée")}catch(r){console.error("Error deleting notification:",r),i.error("Erreur lors de la suppression de la notification"),n||c()}},Q=()=>{!n&&!o&&j()},V=()=>{c()},H=(e,r)=>{const a=J(r);switch(e){case"alert":case"machine_alert":return t.createElement(T,{style:a});case"maintenance":return t.createElement(fe,{style:a});case"update":return t.createElement($,{style:a});case"production":return t.createElement($,{style:a});case"quality":return t.createElement(T,{style:a});case"info":default:return t.createElement(de,{style:a})}},J=e=>{switch(e){case"critical":return{color:"#ff4d4f",fontSize:"18px"};case"high":return{color:"#fa8c16",fontSize:"16px"};case"medium":return{color:"#1890ff",fontSize:"16px"};case"low":return{color:"#52c41a",fontSize:"16px"};default:return{color:"#1890ff",fontSize:"16px"}}},K=(e,r)=>{switch(r){case"critical":return"error";case"high":return"warning";case"medium":return"processing";case"low":return"success";default:switch(e){case"alert":case"machine_alert":return"error";case"maintenance":return"warning";case"update":case"production":return"processing";case"quality":return"warning";case"info":default:return"success"}}},X=e=>{switch(e){case"critical":return"Critique";case"high":return"Élevée";case"medium":return"Moyenne";case"low":return"Faible";default:return"Moyenne"}},Y=e=>{switch(e){case"alert":return"Alerte";case"machine_alert":return"Alerte Machine";case"maintenance":return"Maintenance";case"update":return"Mise à jour";case"production":return"Production";case"quality":return"Qualité";case"info":default:return"Information"}},N=I.filter(e=>d==="all"?!0:d==="unread"?!m(e):d==="critical"?e.priority==="critical":e.category===d);return t.createElement("div",{className:"notifications-page"},t.createElement(ae,{title:t.createElement(w,null,t.createElement(ne,null),t.createElement("span",null,"Notifications"),x>0&&t.createElement(b,{count:x,style:{backgroundColor:"#1890ff"}}),n?t.createElement(p,{title:"Connecté en temps réel"},t.createElement(ie,{style:{color:"#52c41a"}})):o?t.createElement(p,{title:"Connexion en cours..."},t.createElement(oe,{style:{color:"#1890ff"}})):t.createElement(p,{title:"Déconnecté - Cliquez pour reconnecter"},t.createElement(y,{type:"text",size:"small",icon:t.createElement(ce,{style:{color:"#ff4d4f"}}),onClick:Q}))),extra:t.createElement(w,{wrap:!0},t.createElement(u.Group,{value:d,onChange:e=>F(e.target.value),optionType:"button",buttonStyle:"solid",size:h?"small":"middle"},t.createElement(u.Button,{value:"all"},"Toutes"),t.createElement(u.Button,{value:"unread"},"Non lues"),t.createElement(u.Button,{value:"critical"},"Critiques"),t.createElement(u.Button,{value:"machine_alert"},"Machines"),t.createElement(u.Button,{value:"maintenance"},"Maintenance")),t.createElement(p,{title:"Marquer tout comme lu"},t.createElement(y,{icon:t.createElement(M,null),onClick:G,disabled:x===0,size:h?"small":"middle"})),t.createElement(p,{title:"Recharger les notifications"},t.createElement(y,{icon:t.createElement(re,null),onClick:V,disabled:o,size:h?"small":"middle"}))),style:{background:l?"#141414":"#fff",boxShadow:l?"0 1px 4px rgba(0,0,0,0.15)":"0 1px 4px rgba(0,0,0,0.05)"}},L?t.createElement("div",{style:{textAlign:"center",padding:"40px 0"}},t.createElement(se,{size:"large"})):N.length===0?t.createElement(v,{description:"Aucune notification",image:v.PRESENTED_IMAGE_SIMPLE}):t.createElement(R,{itemLayout:"horizontal",dataSource:N,renderItem:e=>t.createElement(R.Item,{key:e.id,actions:[t.createElement(y,{key:"delete",type:"text",icon:t.createElement(le,null),onClick:()=>O(e.id)}),!m(e)&&t.createElement(y,{key:"markAsRead",type:"text",icon:t.createElement(M,null),onClick:()=>W(e.id)})],style:{background:m(e)?"transparent":e.priority==="critical"?l?"#2a1215":"#fff2f0":e.priority==="high"?l?"#2b1d11":"#fff7e6":l?"#111b26":"#f0f7ff",padding:"12px",borderRadius:"4px",marginBottom:"8px",border:e.priority==="critical"?l?"1px solid #a8071a":"1px solid #ff7875":"none"}},t.createElement(R.Item.Meta,{avatar:H(e.category,e.priority),title:t.createElement(w,{wrap:!0},t.createElement(g,{strong:!0,style:{color:e.priority==="critical"?"#ff4d4f":"inherit"}},e.title),t.createElement(z,{color:K(e.category,e.priority),style:{fontWeight:e.priority==="critical"?"bold":"normal"}},X(e.priority)),t.createElement(z,{size:"small"},Y(e.category)),!m(e)&&t.createElement(b,{status:"processing"}),(e.acknowledged_at||e.acknowledged)&&t.createElement(b,{status:"success",text:"Acquittée"})),description:t.createElement(t.Fragment,null,t.createElement("div",{style:{marginBottom:"8px"}},e.message),t.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",flexWrap:"wrap",gap:"8px"}},t.createElement("div",null,t.createElement(ye,{style:{marginRight:4}}),t.createElement(g,{type:"secondary"},S(e.created_at||e.timestamp).fromNow())," "),e.machine_id&&t.createElement(g,{type:"secondary",style:{fontSize:"12px"}},"Machine: ",e.machine_id),e.source&&t.createElement(g,{type:"secondary",style:{fontSize:"12px"}},"Source: ",e.source)))}))})))};export{Ae as default};
