import { useCallback, useRef } from 'react'
import { DEBOUNCE_DELAYS } from './constants.jsx'
import { 
  calculateAvailabilityTrendData, 
  calculateMTTRCalendarData, 
  calculateDowntimeParetoData 
} from './performanceCalculations.jsx'

/**
 * Comprehensive date parsing function to handle multiple date formats from database
 * @param {string} dateStr - Date string from database
 * @returns {Date|null} Parsed Date object or null if invalid
 */
const parseDate = (dateStr) => {
  if (!dateStr) return null;

  try {
    const str = String(dateStr).trim();
    
    // Handle DD/MM/YYYY HH:MM:SS format (most common from GraphQL)
    if (str.includes('/')) {
      const parts = str.split(' ');
      const datePart = parts[0]; // "DD/MM/YYYY"
      const timePart = parts[1] || '00:00:00'; // "HH:MM:SS" or default
      
      const [day, month, year] = datePart.split('/');
      
      if (day && month && year && 
          day.length <= 2 && month.length <= 2 && year.length === 4) {
        
        const paddedDay = day.padStart(2, '0');
        const paddedMonth = month.padStart(2, '0');
        
        // Create Date object using year, month-1 (0-indexed), day, hours, minutes, seconds
        const timeParts = timePart.split(':');
        const hours = parseInt(timeParts[0]) || 0;
        const minutes = parseInt(timeParts[1]) || 0;
        const seconds = parseInt(timeParts[2]) || 0;
        
        const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day), hours, minutes, seconds);
        
        if (!isNaN(date.getTime())) {
          return date;
        }
      }
    }
    
    // Handle YYYY HH:MM:SS-MM-DD format (alternative database format)
    if (str.includes('-') && str.includes(' ')) {
      const spaceIndex = str.indexOf(' ');
      const year = str.substring(0, spaceIndex);
      const remaining = str.substring(spaceIndex + 1);
      
      if (remaining.includes('-')) {
        const dashIndex = remaining.lastIndexOf('-');
        const time = remaining.substring(0, dashIndex);
        const monthDay = remaining.substring(dashIndex + 1);
        
        if (monthDay.includes('-')) {
          const [month, day] = monthDay.split('-');
          if (year && month && day && time) {
            const timeParts = time.split(':');
            const hours = parseInt(timeParts[0]) || 0;
            const minutes = parseInt(timeParts[1]) || 0;
            const seconds = parseInt(timeParts[2]) || 0;
            
            const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day), hours, minutes, seconds);
            
            if (!isNaN(date.getTime())) {
              return date;
            }
          }
        }
      }
    }
    
    // Standard Date constructor fallback
    const date = new Date(str);
    if (!isNaN(date.getTime())) {
      return date;
    }
    
    return null;
  } catch (error) {
    console.warn('Date parsing error:', error, 'for date:', dateStr);
    return null;
  }
};

/**
 * Queued Data Manager Module
 * Implements progressive/queued data loading instead of parallel loading
 * Addresses the core issues:
 * 1. APIs called in queue (not parallel)
 * 2. Lazy loading with priorities
 * 3. Progressive loading states
 */
export const useQueuedDataManager = (
  graphQLInterface,
  state,
  setState,
  skeletonManager
) => {
  // Refs for optimization
  const pendingFetch = useRef(false)
  const isMounted = useRef(true)
  const lastFilterChange = useRef(Date.now())

  /**
   * Progressive/Queued data fetching system
   * Loads data in priority order with delays between each queue
   */
  const fetchDataInQueue = useCallback(async (forceRefresh = false) => {
    // Prevent multiple simultaneous fetches
    if (pendingFetch.current && !forceRefresh) {
      console.log('⏸️ Fetch already in progress, skipping...');
      return;
    }

    if (!isMounted.current) {
      console.log('⏸️ Component unmounted, skipping fetch');
      return;
    }

    pendingFetch.current = true;
    
    console.log('🚀 Starting queued data fetch with state:', {
      selectedMachineModel: state.selectedMachineModel,
      selectedMachine: state.selectedMachine, 
      selectedDate: state.selectedDate,
      dateRangeType: state.dateRangeType,
      forceRefresh
    });
    
    // Detect complex filter scenario
    const hasAllFilters = state.selectedMachineModel && state.selectedMachine && state.selectedDate;
    

    // Set complex filter loading if needed
    if (hasAllFilters) {
      setState(prev => ({ ...prev, complexFilterLoading: true }));
    }

    try {
      // Build filters with proper debugging
      const filters = {
        model: state.selectedMachineModel || null,
        machine: state.selectedMachine || null,
        date: state.selectedDate ? (typeof state.selectedDate === 'string' ? state.selectedDate : state.selectedDate.format('YYYY-MM-DD')) : null,
        startDate: state.selectedDate ? (typeof state.selectedDate === 'string' ? state.selectedDate : state.selectedDate.clone().startOf(state.dateRangeType).format('YYYY-MM-DD')) : null,
        endDate: state.selectedDate ? (typeof state.selectedDate === 'string' ? state.selectedDate : state.selectedDate.clone().endOf(state.dateRangeType).format('YYYY-MM-DD')) : null,
        dateRangeType: state.dateRangeType || 'month'
      };

      console.log('🔍 Built filters for GraphQL queries:', {
        filters,
        selectedDateType: typeof state.selectedDate,
        selectedDateValue: state.selectedDate,
        selectedMachineModel: state.selectedMachineModel,
        selectedMachine: state.selectedMachine,
        dateRangeType: state.dateRangeType
      });

      setState(prev => ({ ...prev, loading: true, essentialLoading: true }));
      
      const essentialData = await graphQLInterface.getEssentialData(filters);
      
      if (!isMounted.current) return;
      
      // Process essential data
      if (essentialData.sidecards) {
        const transformedStats = [
          { title: 'Total Arrêts', value: essentialData.sidecards.Arret_Totale || 0, icon: '🚨' },
          { title: 'Arrêts Non Déclarés', value: essentialData.sidecards.Arret_Totale_nondeclare || 0, icon: '⚠️' },
          { title: 'Durée Totale', value: 0, suffix: 'min', icon: '⏱️' },
          { title: 'Durée Moyenne', value: 0, suffix: 'min', icon: '⏱️' },
          { title: 'Interventions', value: 0, icon: '🔧' }
        ];
        
        setState(prev => ({ 
          ...prev, 
          arretStats: transformedStats,
          totalStops: essentialData.sidecards.Arret_Totale || 0,
          undeclaredStops: essentialData.sidecards.Arret_Totale_nondeclare || 0,
          essentialLoading: false
        }));
        
      }

      // Small delay to allow UI to update
      await new Promise(resolve => setTimeout(resolve, 100));

      // QUEUE 2: Performance data (Priority 2) - Only when specific machine is selected
      if (filters.machine) {
        const performanceData = await graphQLInterface.getPerformanceData(filters);
        
        if (!isMounted.current) return;
        
        setState(prev => ({ 
          ...prev, 
          mttr: performanceData.performance.mttr,
          mtbf: performanceData.performance.mtbf,
          doper: performanceData.performance.doper,
          showPerformanceMetrics: true
        }));
      } else {
        // Reset performance metrics when no specific machine is selected
        setState(prev => ({ 
          ...prev, 
          mttr: 0,
          mtbf: 0,
          doper: 0,
          showPerformanceMetrics: false
        }));
      }
      

      // Small delay before heavy operations
      await new Promise(resolve => setTimeout(resolve, 200));

      // QUEUE 3: Chart data (Priority 3)
      setState(prev => ({ ...prev, detailedLoading: true }));
      
      const chartData = await graphQLInterface.getChartData(filters);
      
      console.log('🔍 Raw backend chart data:', chartData);
      console.log('🔍 Raw machine comparison data:', chartData.machineComparison);
      
      if (!isMounted.current) return;
      
      // Process chart data
      if (chartData.topStops) {
        console.log('🔍 Processing topStops data:', chartData.topStops);
        
        const totalStops = chartData.topStops.reduce((sum, stop) => sum + stop.count, 0);
        const topStopsWithPercentage = chartData.topStops.map(stop => ({
          ...stop,
          percentage: totalStops > 0 ? ((stop.count / totalStops) * 100).toFixed(1) : 0
        }));
        
        console.log('📊 topStopsWithPercentage:', topStopsWithPercentage);
        
        // Process machine comparison data from backend
        const machineComparisonFormatted = (chartData.machineComparison || []).map(machine => {
          // Map backend fields to chart format - backend uses totalStops and incidents
          const stops = machine.totalStops || machine.stops || machine.incidents || 0;
          const totalDuration = machine.totalDuration || machine.duration || 0;
          
          return {
            Machine_Name: machine.Machine_Name,  // Primary field expected by chart
            machine: machine.Machine_Name,       // Alternative field name 
            name: machine.Machine_Name,          // Another alternative
            stops: stops,
            totalStops: stops,                   // Legacy field for compatibility
            totalDuration: totalDuration,
            avgDuration: stops > 0 ? (totalDuration / stops).toFixed(1) : 0
          };
        });
        
        console.log('🎯 Formatted machine comparison data for charts:', machineComparisonFormatted);
        
        const stopReasonsFormatted = topStopsWithPercentage.map(stop => ({
          reason: stop.stopName || stop.name || 'Non défini',      // Use 'reason' field that ArretHorizontalBarChart expects
          count: stop.count || 0,          // Use 'count' field that ArretHorizontalBarChart expects
          name: stop.stopName || stop.name || 'Non défini',        // Keep 'name' for backward compatibility
          value: stop.count || 0,          // Keep 'value' for backward compatibility
          percentage: stop.percentage,
          stopName: stop.stopName || stop.name || 'Non défini'     // Keep original field
        }));
        
        console.log('🎯 Formatted stopReasons for chart:', stopReasonsFormatted);
        
        setState(prev => ({ 
          ...prev, 
          topStopsData: topStopsWithPercentage,
          machineComparison: machineComparisonFormatted,
          stopReasons: stopReasonsFormatted,
          chartData: [], // Will be populated with evolution data from table data
          durationTrend: [],
          disponibiliteTrendData: [],
          downtimeParetoData: topStopsWithPercentage,
          mttrCalendarData: [],
          disponibiliteByMachineData: []
        }));
      }
      

      // Small delay before heaviest operation
      await new Promise(resolve => setTimeout(resolve, 300));

      // QUEUE 4: Table data (Priority 4 - Heaviest)
      
      const tableData = await graphQLInterface.getTableData(filters);
      
      if (!isMounted.current) return;
      
      // Process table data and calculate machine comparison
      setState(prev => ({ ...prev, stopsData: tableData.stopsData }));
      
      // Set filteredStopsData based on current filters
      const filteredStopsData = tableData.stopsData?.filter(stop => {
        // Filter by machine if selected
        if (state.selectedMachine && stop.Machine_Name !== state.selectedMachine) {
          return false;
        }
        
        // Filter by date if selected
        if (state.selectedDate && stop.Date_Insert) {
          const stopDate = parseDate(stop.Date_Insert);  // Use our comprehensive parsing
          const filterDate = new Date(state.selectedDate);
          
          console.log('🔍 Date filtering:', {
            stopDateString: stop.Date_Insert,
            parsedStopDate: stopDate,
            filterDate: filterDate,
            dateRangeType: state.dateRangeType
          });
          
          if (!stopDate) {
            console.warn('❌ Failed to parse stop date:', stop.Date_Insert);
            return false; // Skip invalid dates
          }
          
          if (state.dateRangeType === 'day') {
            const match = stopDate.toDateString() === filterDate.toDateString();
            console.log('📅 Day filter result:', match);
            return match;
          } else if (state.dateRangeType === 'week') {
            const weekStart = new Date(filterDate);
            weekStart.setDate(filterDate.getDate() - filterDate.getDay());
            const weekEnd = new Date(weekStart);
            weekEnd.setDate(weekStart.getDate() + 6);
            
            const match = stopDate >= weekStart && stopDate <= weekEnd;
            console.log('📅 Week filter result:', match, {
              weekStart: weekStart.toDateString(),
              weekEnd: weekEnd.toDateString(),
              stopDate: stopDate.toDateString()
            });
            return match;
          } else if (state.dateRangeType === 'month') {
            const match = stopDate.getMonth() === filterDate.getMonth() && 
                         stopDate.getFullYear() === filterDate.getFullYear();
            console.log('📅 Month filter result:', match, {
              stopMonth: stopDate.getMonth(),
              stopYear: stopDate.getFullYear(),
              filterMonth: filterDate.getMonth(),
              filterYear: filterDate.getFullYear()
            });
            return match;
          }
        }
        
        return true;
      }) || [];
      
      setState(prev => ({ ...prev, filteredStopsData }));
      
      // Don't override machine comparison data - it's already properly set from chart data
      // Just update other chart data that depends on table data
        
        // Generate evolution chart data using FILTERED data (respects date and machine filters)
        const dailyStats = {};
        
        console.log('🔧 Starting evolution data generation with FILTERED data:', {
          filteredStopsDataLength: filteredStopsData?.length || 0,
          totalStopsDataLength: tableData.stopsData?.length || 0,
          sampleFilteredStops: filteredStopsData?.slice(0, 3) || [],
          sampleFilteredDates: filteredStopsData?.slice(0, 5).map(s => s.Date_Insert) || [],
          currentFilters: {
            selectedMachine: state.selectedMachine,
            selectedDate: state.selectedDate,
            dateRangeType: state.dateRangeType
          }
        });
        
        // Use filteredStopsData instead of tableData.stopsData to respect filters
        filteredStopsData.forEach(stop => {
          if (stop.Date_Insert && typeof stop.Date_Insert === 'string') {
            let date;
            let originalDate = stop.Date_Insert.toString();
            
            console.log('🔍 Processing date:', originalDate);
            
            // Handle different date formats from database
            date = parseDate(stop.Date_Insert);
            
            if (date) {
              const formattedDate = date.toISOString().split('T')[0];
              
              if (!dailyStats[formattedDate]) {
                dailyStats[formattedDate] = { date: formattedDate, stops: 0, duration: 0 };
              }
              dailyStats[formattedDate].stops++;
              
              if (stop.duration_minutes && stop.duration_minutes > 0) {
                dailyStats[formattedDate].duration += parseFloat(stop.duration_minutes);
              }
            } else {
              console.warn('❌ Invalid date format, skipping:', stop.Date_Insert);
            }
          }
        });
        
        console.log('🔧 Daily stats generated:', dailyStats);
        
        // Smart evolution data generation based on current filters
        let evolutionData = Object.values(dailyStats)
          .sort((a, b) => new Date(a.date) - new Date(b.date));
        
        // If we have a specific date filter, show data around that period
        // Otherwise, show the last 7 days of available data
        if (state.selectedDate && state.dateRangeType) {
          console.log('🎯 Using date filter for evolution chart:', {
            selectedDate: state.selectedDate,
            dateRangeType: state.dateRangeType
          });
          
          if (state.dateRangeType === 'month') {
            // For month filter, show all days in that month
            const filterDate = new Date(state.selectedDate);
            const filterMonth = filterDate.getMonth();
            const filterYear = filterDate.getFullYear();
            
            evolutionData = evolutionData.filter(item => {
              const itemDate = new Date(item.date);
              return itemDate.getMonth() === filterMonth && itemDate.getFullYear() === filterYear;
            });
          } else if (state.dateRangeType === 'week') {
            // For week filter, show 7 days around the selected date
            const filterDate = new Date(state.selectedDate);
            const weekStart = new Date(filterDate);
            weekStart.setDate(filterDate.getDate() - filterDate.getDay());
            const weekEnd = new Date(weekStart);
            weekEnd.setDate(weekStart.getDate() + 6);
            
            evolutionData = evolutionData.filter(item => {
              const itemDate = new Date(item.date);
              return itemDate >= weekStart && itemDate <= weekEnd;
            });
          } else {
            // For day filter, show data around that day (maybe ±3 days)
            const filterDate = new Date(state.selectedDate);
            const rangeStart = new Date(filterDate);
            rangeStart.setDate(filterDate.getDate() - 3);
            const rangeEnd = new Date(filterDate);
            rangeEnd.setDate(filterDate.getDate() + 3);
            
            evolutionData = evolutionData.filter(item => {
              const itemDate = new Date(item.date);
              return itemDate >= rangeStart && itemDate <= rangeEnd;
            });
          }
        } else {
          // No specific date filter, show all available data (not limited to 7 days)
          // When only model filter is active, show all data for that model
          console.log('📊 No date filter active - showing all available data for selected model');
        }
        
        // Format the display dates with better error handling
        evolutionData = evolutionData.map(item => {
          console.log('🔄 Formatting displayDate for:', item.date);
          
          // Create a proper Date object and ensure it's valid
          let dateObj;
          let isValidDate = false;
          let displayDate;
          
          // Try parsing the date
          if (item.date && typeof item.date === 'string') {
            // If it's already in ISO format (YYYY-MM-DD)
            if (item.date.match(/^\d{4}-\d{2}-\d{2}$/)) {
              dateObj = new Date(item.date);
              isValidDate = !isNaN(dateObj.getTime());
            } else {
              // Try to parse the raw database format again
              const match = item.date.match(/^(\d{4}) \d{2}:\d{2}:\d{2}-(\d{1,2})-\s*(\d{1,2})$/);
              if (match) {
                const [_, year, month, day] = match;
                const isoDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
                dateObj = new Date(isoDate);
                isValidDate = !isNaN(dateObj.getTime());
                console.log('🔧 Re-parsed date:', item.date, '->', isoDate, 'Valid:', isValidDate);
              }
            }
          }
          
          if (isValidDate && dateObj) {
            displayDate = dateObj.toLocaleDateString('fr-FR', { 
              day: '2-digit', 
              month: '2-digit' 
            });
            console.log('✅ Generated displayDate:', displayDate);
          } else {
            // Fallback: try to extract day/month from raw format
            const fallbackMatch = item.date.match(/^(\d{4}) \d{2}:\d{2}:\d{2}-(\d{1,2})-\s*(\d{1,2})$/);
            if (fallbackMatch) {
              const [_, year, month, day] = fallbackMatch;
              displayDate = `${day.padStart(2, '0')}/${month.padStart(2, '0')}`;
              console.log('🔧 Fallback displayDate:', displayDate);
            } else {
              displayDate = item.date.slice(0, 10); // Just show first 10 chars as fallback
              console.log('⚠️ Using raw fallback displayDate:', displayDate);
            }
          }
          
          return {
            ...item,
            displayDate
          };
        });
        
        
        console.log('📊 Generated evolution data:', {
          dailyStatsKeys: Object.keys(dailyStats),
          evolutionDataLength: evolutionData.length,
          evolutionData: evolutionData.slice(0, 3), // Show first 3 items
          fullEvolutionData: evolutionData
        });
        
        // If no evolution data generated, create sample data for testing
        let finalEvolutionData = evolutionData;
        if (evolutionData.length === 0) {
          console.log('⚠️ No evolution data found!');
          console.log('🔍 Debug info for empty evolution data:', {
            filteredStopsDataLength: filteredStopsData?.length || 0,
            dailyStatsCount: Object.keys(dailyStats).length,
            hasSelectedDate: !!state.selectedDate,
            hasSelectedMachine: !!state.selectedMachine,
            dateRangeType: state.dateRangeType,
            sampleFilteredStops: filteredStopsData?.slice(0, 2) || []
          });
          
          // Temporarily disabled sample data to debug the real issue
          finalEvolutionData = [];
          
          /*
          const today = new Date();
          finalEvolutionData = Array.from({ length: 7 }, (_, i) => {
            const date = new Date(today);
            date.setDate(today.getDate() - (6 - i));
            return {
              date: date.toISOString().split('T')[0],
              displayDate: date.toLocaleDateString('fr-FR', { 
                day: '2-digit', 
                month: '2-digit' 
              }),
              stops: Math.floor(Math.random() * 10) + 1,
              duration: Math.floor(Math.random() * 200) + 50
            };
          });
          console.log('🎲 Generated sample data:', finalEvolutionData);
          */
        } else {
          console.log('✅ Using real evolution data:', finalEvolutionData.length, 'items');
        }
        
        setState(prev => ({ 
          ...prev, 
          chartData: finalEvolutionData
        }));
      
      
      // Calculate operator stats from table data
      if (tableData.stopsData?.length > 0) {
        const operatorCounts = {};
        tableData.stopsData.forEach(stop => {
          const operator = stop.Regleur_Prenom || 'Non assigné';
          operatorCounts[operator] = (operatorCounts[operator] || 0) + 1;
        });
        
        const operatorStatsArray = Object.entries(operatorCounts).map(([operator, interventions]) => ({
          operator,
          interventions
        }));
        
        setState(prev => ({ ...prev, operatorStats: operatorStatsArray }));
      }
      
      // Calculate advanced analytics data (only when machine is selected)
      let disponibiliteTrendData = [];
      let mttrCalendarData = [];
      let downtimeParetoData = [];
      
      if (state.selectedMachine && tableData.stopsData?.length > 0) {
        console.log('🔧 Calculating advanced analytics for machine:', state.selectedMachine);
        console.log('🔍 Using FILTERED data for Pareto analysis:', {
          totalStopsData: tableData.stopsData?.length || 0,
          filteredStopsData: filteredStopsData?.length || 0,
          selectedMachine: state.selectedMachine,
          selectedDate: state.selectedDate,
          dateRangeType: state.dateRangeType,
          sampleFilteredStops: filteredStopsData?.slice(0, 2) || []
        });
        
        // Calculate availability trend data using FILTERED data
        disponibiliteTrendData = calculateAvailabilityTrendData(
          filteredStopsData,  // Use filtered data instead of all data
          state.dateRangeType
        );
        
        // Calculate MTTR calendar data using FILTERED data
        mttrCalendarData = calculateMTTRCalendarData(filteredStopsData);  // Use filtered data
        
        // Calculate downtime Pareto data using FILTERED data
        downtimeParetoData = calculateDowntimeParetoData(filteredStopsData);  // Use filtered data
        
        console.log('🔧 Advanced analytics calculated with FILTERED data:', {
          disponibiliteTrendData: disponibiliteTrendData.length,
          mttrCalendarData: mttrCalendarData.length,
          downtimeParetoData: downtimeParetoData.length,
          downtimeParetoSample: downtimeParetoData.slice(0, 3),
          downtimeParetoDataFull: downtimeParetoData
        });
      }
      
      // Final state updates
      setState(prev => ({ 
        ...prev, 
        loading: false, 
        detailedLoading: false,
        complexFilterLoading: false,
        // Advanced analytics data
        disponibiliteTrendData,
        mttrCalendarData,
        downtimeParetoData,
        // Set additional fields for backward compatibility
        arretsByRange: tableData.stopsData || [],
        sidebarStats: prev.arretStats
      }));

    } catch (error) {
      console.error('❌ Error in queued data fetch:', error);
      
      if (!isMounted.current) return;
      
      // Provide fallback data
      setState(prev => ({ 
        ...prev,
        loading: false,
        essentialLoading: false,
        detailedLoading: false,
        complexFilterLoading: false,
        error: error.message || 'Failed to fetch data',
        arretStats: [
          { title: 'Total Arrêts', value: 0, icon: '🚨' },
          { title: 'Arrêts Non Déclarés', value: 0, icon: '⚠️' },
          { title: 'Durée Totale', value: 0, suffix: 'min', icon: '⏱️' },
          { title: 'Durée Moyenne', value: 0, suffix: 'min', icon: '⏱️' },
          { title: 'Interventions', value: 0, icon: '🔧' }
        ]
      }));
    } finally {
      pendingFetch.current = false;
    }
  }, [state.selectedMachineModel, state.selectedMachine, state.selectedDate, state.dateRangeType, graphQLInterface, setState]);

  /**
   * Initialize machine data
   */
  const initializeMachineData = useCallback(async () => {
    try {
      
      // Fetch machine models and names
      const models = await graphQLInterface.getMachineModels();
      const names = await graphQLInterface.getMachineNames();
      
      if (isMounted.current) {
        // Transform machine names
        const transformedNames = names.map(item => {
          const machineName = item.Machine_Name;
          let model = '';
          if (machineName.startsWith('IPSO')) {
            model = 'IPSO';
          } else if (machineName.startsWith('IPS')) {
            model = 'IPS';
          } else if (machineName.startsWith('CCM')) {
            model = 'CCM';
          } else {
            model = machineName.match(/^[A-Za-z]+/)?.[0] || 'UNKNOWN';
          }
          
          return {
            name: machineName,
            model: model
          };
        });
        
        setState(prev => ({
          ...prev,
          machineModels: models,
          machineNames: transformedNames,
          selectedMachineModel: "IPS" // Default selection
        }));
        
      }
      
    } catch (error) {
      console.error('❌ Error initializing machine data:', error);
      if (isMounted.current) {
        setState(prev => ({ ...prev, error: error.message }));
      }
    }
  }, [graphQLInterface, setState]);

  /**
   * Set mounted state
   */
  const setMounted = useCallback((mounted) => {
    isMounted.current = mounted;
  }, []);

  return {
    fetchDataInQueue,
    initializeMachineData,
    setMounted,
    isMounted: isMounted.current,
    pendingFetch: pendingFetch.current
  };
};
