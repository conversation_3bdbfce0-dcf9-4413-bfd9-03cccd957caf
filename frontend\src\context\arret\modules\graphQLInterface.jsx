/**
 * GraphQL Interface Module for Queued Data Loading
 * Provides structured GraphQL queries organized by priority for progressive loading
 */
export const useGraphQLInterface = (fetchGraphQL) => {
  
  const graphQLInterface = {
    // Priority 1: Essential data (stats cards)
    async getEssentialData(filters) {
      
      const sidecardsData = await fetchGraphQL(`
        query($filters: StopFilterInput) {
          getStopSidecards(filters: $filters) {
            Arret_Totale
            Arret_Totale_nondeclare
          }
        }
      `, { filters });
      
      return {
        sidecards: sidecardsData?.getStopSidecards,
        priority: 1,
        loadingState: 'essentialLoading'
      };
    },

    // Priority 2: Performance metrics data
    async getPerformanceData(filters) {
      // Fetch stop data to calculate performance metrics
      const stopsData = await fetchGraphQL(`
        query($filters: StopFilterInput) {
          getAllMachineStops(filters: $filters) {
            Date_Insert
            Machine_Name
            Code_Stop
            duration_minutes
            Debut_Stop
            Fin_Stop_Time
          }
        }
      `, { filters });

      const stops = stopsData?.getAllMachineStops || [];
      
      // Calculate performance metrics
      let mttr = 0;
      let mtbf = 0;
      let doper = 0;
      
      if (stops.length > 0) {
        // MTTR: Average repair time (duration of stops)
        const totalDuration = stops.reduce((sum, stop) => {
          if (stop.duration_minutes && stop.duration_minutes > 0) {
            return sum + parseFloat(stop.duration_minutes);
          } else if (stop.Debut_Stop && stop.Fin_Stop_Time) {
            // Calculate duration from start and end times if duration_minutes is not available
            try {
              const parseDateTime = (dateStr) => {
                if (dateStr.includes(' ')) {
                  const [datePart, timePart] = dateStr.split(' ');
                  const [day, month, year] = datePart.split('/');
                  const [hours, minutes] = timePart.split(':');
                  return new Date(year, month - 1, day, hours, minutes);
                }
                return new Date(dateStr);
              };
              
              const startTime = parseDateTime(stop.Debut_Stop);
              const endTime = parseDateTime(stop.Fin_Stop_Time);
              
              if (!isNaN(startTime.getTime()) && !isNaN(endTime.getTime())) {
                const durationMs = endTime - startTime;
                const durationMinutes = Math.max(0, Math.floor(durationMs / (1000 * 60)));
                return sum + durationMinutes;
              }
            } catch (error) {
              console.warn('Error calculating duration for stop:', stop, error);
            }
          }
          return sum;
        }, 0);
        
        mttr = stops.length > 0 ? totalDuration / stops.length : 0;
        
        // MTBF: Simple approximation - 24 hours / number of stops per day
        // This is a simplified calculation - in reality would need operating hours data
        const totalDays = stops.length > 0 ? 30 : 1; // Assume 30-day period
        const stopsPerDay = stops.length / totalDays;
        mtbf = stopsPerDay > 0 ? (24 * 60) / stopsPerDay : 0;
        
        // DOPER: Availability - simplified calculation
        // Assume 24*60 minutes per day, subtract total downtime
        const totalDowntime = totalDuration;
        const totalTime = totalDays * 24 * 60; // Total minutes in period
        doper = totalTime > 0 ? ((totalTime - totalDowntime) / totalTime) * 100 : 0;
        
        // Ensure reasonable bounds
        mttr = Math.max(0, Math.min(mttr, 1440)); // Max 24 hours
        mtbf = Math.max(0, Math.min(mtbf, 10080)); // Max 1 week
        doper = Math.max(0, Math.min(doper, 100)); // 0-100%
      }
      
      return {
        performance: { 
          mttr: Number(mttr.toFixed(1)), 
          mtbf: Number(mtbf.toFixed(1)), 
          doper: Number(doper.toFixed(1)) 
        },
        priority: 2,
        loadingState: 'essentialLoading'
      };
    },

    // Priority 3: Chart data
    async getChartData(filters) {
      
      const [topStopsData, machineComparisonData] = await Promise.all([
        fetchGraphQL(`
          query($filters: StopFilterInput) {
            getTop5Stops(filters: $filters) {
              stopName
              count
            }
          }
        `, { filters }),
        
        fetchGraphQL(`
          query($filters: StopFilterInput) {
            getMachineStopComparison(filters: $filters) {
              Machine_Name
              stops
              totalDuration
            }
          }
        `, { filters })
      ]);
      
      return {
        topStops: topStopsData?.getTop5Stops || [],
        machineComparison: machineComparisonData?.getMachineStopComparison || [],
        priority: 3,
        loadingState: 'detailedLoading'
      };
    },

    // Priority 4: Heavy table data
    async getTableData(filters) {
      
      const tableData = await fetchGraphQL(`
        query($filters: StopFilterInput) {
          getAllMachineStops(filters: $filters) {
            Date_Insert
            Machine_Name
            Part_NO
            Code_Stop
            Debut_Stop
            Fin_Stop_Time
            Regleur_Prenom
            duration_minutes
          }
        }
      `, { filters });
      
      return {
        stopsData: tableData?.getAllMachineStops || [],
        priority: 4,
        loadingState: 'detailedLoading'
      };
    },

    // Machine metadata
    async getMachineModels() {
      const data = await fetchGraphQL(`
        query {
          getStopMachineModels {
            model
          }
        }
      `);
      return data?.getStopMachineModels || [];
    },

    async getMachineNames() {
      const data = await fetchGraphQL(`
        query {
          getStopMachineNames {
            Machine_Name
          }
        }
      `);
      return data?.getStopMachineNames || [];
    }
  };

  return graphQLInterface;
};
