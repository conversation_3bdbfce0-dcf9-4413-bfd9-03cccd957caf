import{r as i,b as ue,c0 as de,aO as X,d as re,_ as ge,a as fe,c as M,c1 as ee,W as pe,j as me,m as ve,aq as ye,k as he,c2 as Ce,aQ as be,t as $e,ax as Se,bU as xe,aW as ke,bV as Pe,c3 as we}from"./index-gs31pxOi.js";var Ee={percent:0,prefixCls:"rc-progress",strokeColor:"#2db7f5",strokeLinecap:"round",strokeWidth:1,trailColor:"#D9D9D9",trailWidth:1,gapPosition:"bottom"},Ie=function(){var t=i.useRef([]),r=i.useRef(null);return i.useEffect(function(){var o=Date.now(),n=!1;t.current.forEach(function(c){if(c){n=!0;var s=c.style;s.transitionDuration=".3s, .3s, .3s, .06s",r.current&&o-r.current<100&&(s.transitionDuration="0s, 0s")}}),n&&(r.current=Date.now())}),t.current},oe=0,Oe=de();function je(){var e;return Oe?(e=oe,oe+=1):e="TEST_OR_SSR",e}const De=function(e){var t=i.useState(),r=ue(t,2),o=r[0],n=r[1];return i.useEffect(function(){n("rc_progress_".concat(je()))},[]),e||o};var ne=function(t){var r=t.bg,o=t.children;return i.createElement("div",{style:{width:"100%",height:"100%",background:r}},o)};function ie(e,t){return Object.keys(e).map(function(r){var o=parseFloat(r),n="".concat(Math.floor(o*t),"%");return"".concat(e[r]," ").concat(n)})}var Ne=i.forwardRef(function(e,t){var r=e.prefixCls,o=e.color,n=e.gradientId,c=e.radius,s=e.style,l=e.ptg,a=e.strokeLinecap,g=e.strokeWidth,u=e.size,f=e.gapDegree,m=o&&X(o)==="object",h=m?"#FFF":void 0,y=u/2,C=i.createElement("circle",{className:"".concat(r,"-circle-path"),r:c,cx:y,cy:y,stroke:h,strokeLinecap:a,strokeWidth:g,opacity:l===0?0:1,style:s,ref:t});if(!m)return C;var S="".concat(n,"-conic"),p=f?"".concat(180+f/2,"deg"):"0deg",v=ie(o,(360-f)/360),O=ie(o,1),P="conic-gradient(from ".concat(p,", ").concat(v.join(", "),")"),b="linear-gradient(to ".concat(f?"bottom":"top",", ").concat(O.join(", "),")");return i.createElement(i.Fragment,null,i.createElement("mask",{id:S},C),i.createElement("foreignObject",{x:0,y:0,width:u,height:u,mask:"url(#".concat(S,")")},i.createElement(ne,{bg:b},i.createElement(ne,{bg:P}))))}),G=100,J=function(t,r,o,n,c,s,l,a,g,u){var f=arguments.length>10&&arguments[10]!==void 0?arguments[10]:0,m=o/100*360*((360-s)/360),h=s===0?0:{bottom:0,top:180,left:90,right:-90}[l],y=(100-n)/100*r;g==="round"&&n!==100&&(y+=u/2,y>=r&&(y=r-.01));var C=G/2;return{stroke:typeof a=="string"?a:void 0,strokeDasharray:"".concat(r,"px ").concat(t),strokeDashoffset:y+f,transform:"rotate(".concat(c+m+h,"deg)"),transformOrigin:"".concat(C,"px ").concat(C,"px"),transition:"stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s, stroke-width .06s ease .3s, opacity .3s ease 0s",fillOpacity:0}},_e=["id","prefixCls","steps","strokeWidth","trailWidth","gapDegree","gapPosition","trailColor","strokeLinecap","style","className","strokeColor","percent"];function se(e){var t=e??[];return Array.isArray(t)?t:[t]}var Ae=function(t){var r=re(re({},Ee),t),o=r.id,n=r.prefixCls,c=r.steps,s=r.strokeWidth,l=r.trailWidth,a=r.gapDegree,g=a===void 0?0:a,u=r.gapPosition,f=r.trailColor,m=r.strokeLinecap,h=r.style,y=r.className,C=r.strokeColor,S=r.percent,p=ge(r,_e),v=G/2,O=De(o),P="".concat(O,"-gradient"),b=v-s/2,$=Math.PI*2*b,I=g>0?90+g/2:-90,x=$*((360-g)/360),w=X(c)==="object"?c:{count:c,gap:2},d=w.count,H=w.gap,V=se(S),_=se(C),A=_.find(function(E){return E&&X(E)==="object"}),W=A&&X(A)==="object",D=W?"butt":m,B=J($,x,0,100,I,g,u,f,D,s),K=Ie(),j=function(){var R=0;return V.map(function(L,T){var Z=_[T]||_[_.length-1],F=J($,x,R,L,I,g,u,Z,D,s);return R+=L,i.createElement(Ne,{key:T,color:Z,ptg:L,radius:b,prefixCls:n,gradientId:P,style:F,strokeLinecap:D,strokeWidth:s,gapDegree:g,ref:function(z){K[T]=z},size:G})}).reverse()},k=function(){var R=Math.round(d*(V[0]/100)),L=100/d,T=0;return new Array(d).fill(null).map(function(Z,F){var U=F<=R-1?_[0]:f,z=U&&X(U)==="object"?"url(#".concat(P,")"):void 0,te=J($,x,T,L,I,g,u,U,"butt",s,H);return T+=(x-te.strokeDashoffset+H)*100/x,i.createElement("circle",{key:F,className:"".concat(n,"-circle-path"),r:b,cx:v,cy:v,stroke:z,strokeWidth:s,opacity:1,style:te,ref:function(le){K[F]=le}})})};return i.createElement("svg",fe({className:M("".concat(n,"-circle"),y),viewBox:"0 0 ".concat(G," ").concat(G),style:h,id:o,role:"presentation"},p),!d&&i.createElement("circle",{className:"".concat(n,"-circle-trail"),r:b,cx:v,cy:v,stroke:f,strokeLinecap:D,strokeWidth:l||s,style:B}),d?k():j())};function N(e){return!e||e<0?0:e>100?100:e}function q({success:e,successPercent:t}){let r=t;return e&&"progress"in e&&(r=e.progress),e&&"percent"in e&&(r=e.percent),r}const We=({percent:e,success:t,successPercent:r})=>{const o=N(q({success:t,successPercent:r}));return[o,N(N(e)-o)]},Le=({success:e={},strokeColor:t})=>{const{strokeColor:r}=e;return[r||ee.green,t||null]},Y=(e,t,r)=>{var o,n,c,s;let l=-1,a=-1;if(t==="step"){const g=r.steps,u=r.strokeWidth;typeof e=="string"||typeof e>"u"?(l=e==="small"?2:14,a=u??8):typeof e=="number"?[l,a]=[e,e]:[l=14,a=8]=Array.isArray(e)?e:[e.width,e.height],l*=g}else if(t==="line"){const g=r==null?void 0:r.strokeWidth;typeof e=="string"||typeof e>"u"?a=g||(e==="small"?6:8):typeof e=="number"?[l,a]=[e,e]:[l=-1,a=8]=Array.isArray(e)?e:[e.width,e.height]}else(t==="circle"||t==="dashboard")&&(typeof e=="string"||typeof e>"u"?[l,a]=e==="small"?[60,60]:[120,120]:typeof e=="number"?[l,a]=[e,e]:Array.isArray(e)&&(l=(n=(o=e[0])!==null&&o!==void 0?o:e[1])!==null&&n!==void 0?n:120,a=(s=(c=e[0])!==null&&c!==void 0?c:e[1])!==null&&s!==void 0?s:120));return[l,a]},Re=3,Te=e=>Re/e*100,Me=e=>{const{prefixCls:t,trailColor:r=null,strokeLinecap:o="round",gapPosition:n,gapDegree:c,width:s=120,type:l,children:a,success:g,size:u=s,steps:f}=e,[m,h]=Y(u,"circle");let{strokeWidth:y}=e;y===void 0&&(y=Math.max(Te(m),6));const C={width:m,height:h,fontSize:m*.15+6},S=i.useMemo(()=>{if(c||c===0)return c;if(l==="dashboard")return 75},[c,l]),p=We(e),v=n||l==="dashboard"&&"bottom"||void 0,O=Object.prototype.toString.call(e.strokeColor)==="[object Object]",P=Le({success:g,strokeColor:e.strokeColor}),b=M(`${t}-inner`,{[`${t}-circle-gradient`]:O}),$=i.createElement(Ae,{steps:f,percent:f?p[1]:p,strokeWidth:y,trailWidth:y,strokeColor:f?P[1]:P,strokeLinecap:o,trailColor:r,prefixCls:t,gapDegree:S,gapPosition:v}),I=m<=20,x=i.createElement("div",{className:b,style:C},$,!I&&a);return I?i.createElement(pe,{title:a},x):x},Q="--progress-line-stroke-color",ce="--progress-percent",ae=e=>{const t=e?"100%":"-100%";return new Ce(`antProgress${e?"RTL":"LTR"}Active`,{"0%":{transform:`translateX(${t}) scaleX(0)`,opacity:.1},"20%":{transform:`translateX(${t}) scaleX(0)`,opacity:.5},to:{transform:"translateX(0) scaleX(1)",opacity:0}})},Be=e=>{const{componentCls:t,iconCls:r}=e;return{[t]:Object.assign(Object.assign({},ye(e)),{display:"inline-block","&-rtl":{direction:"rtl"},"&-line":{position:"relative",width:"100%",fontSize:e.fontSize},[`${t}-outer`]:{display:"inline-flex",alignItems:"center",width:"100%"},[`${t}-inner`]:{position:"relative",display:"inline-block",width:"100%",flex:1,overflow:"hidden",verticalAlign:"middle",backgroundColor:e.remainingColor,borderRadius:e.lineBorderRadius},[`${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.defaultColor}},[`${t}-success-bg, ${t}-bg`]:{position:"relative",background:e.defaultColor,borderRadius:e.lineBorderRadius,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOutCirc}`},[`${t}-layout-bottom`]:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",[`${t}-text`]:{width:"max-content",marginInlineStart:0,marginTop:e.marginXXS}},[`${t}-bg`]:{overflow:"hidden","&::after":{content:'""',background:{_multi_value_:!0,value:["inherit",`var(${Q})`]},height:"100%",width:`calc(1 / var(${ce}) * 100%)`,display:"block"},[`&${t}-bg-inner`]:{minWidth:"max-content","&::after":{content:"none"},[`${t}-text-inner`]:{color:e.colorWhite,[`&${t}-text-bright`]:{color:"rgba(0, 0, 0, 0.45)"}}}},[`${t}-success-bg`]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,backgroundColor:e.colorSuccess},[`${t}-text`]:{display:"inline-block",marginInlineStart:e.marginXS,color:e.colorText,lineHeight:1,width:"2em",whiteSpace:"nowrap",textAlign:"start",verticalAlign:"middle",wordBreak:"normal",[r]:{fontSize:e.fontSize},[`&${t}-text-outer`]:{width:"max-content"},[`&${t}-text-outer${t}-text-start`]:{width:"max-content",marginInlineStart:0,marginInlineEnd:e.marginXS}},[`${t}-text-inner`]:{display:"flex",justifyContent:"center",alignItems:"center",width:"100%",height:"100%",marginInlineStart:0,padding:`0 ${he(e.paddingXXS)}`,[`&${t}-text-start`]:{justifyContent:"start"},[`&${t}-text-end`]:{justifyContent:"end"}},[`&${t}-status-active`]:{[`${t}-bg::before`]:{position:"absolute",inset:0,backgroundColor:e.colorBgContainer,borderRadius:e.lineBorderRadius,opacity:0,animationName:ae(),animationDuration:e.progressActiveMotionDuration,animationTimingFunction:e.motionEaseOutQuint,animationIterationCount:"infinite",content:'""'}},[`&${t}-rtl${t}-status-active`]:{[`${t}-bg::before`]:{animationName:ae(!0)}},[`&${t}-status-exception`]:{[`${t}-bg`]:{backgroundColor:e.colorError},[`${t}-text`]:{color:e.colorError}},[`&${t}-status-exception ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorError}},[`&${t}-status-success`]:{[`${t}-bg`]:{backgroundColor:e.colorSuccess},[`${t}-text`]:{color:e.colorSuccess}},[`&${t}-status-success ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorSuccess}}})}},Fe=e=>{const{componentCls:t,iconCls:r}=e;return{[t]:{[`${t}-circle-trail`]:{stroke:e.remainingColor},[`&${t}-circle ${t}-inner`]:{position:"relative",lineHeight:1,backgroundColor:"transparent"},[`&${t}-circle ${t}-text`]:{position:"absolute",insetBlockStart:"50%",insetInlineStart:0,width:"100%",margin:0,padding:0,color:e.circleTextColor,fontSize:e.circleTextFontSize,lineHeight:1,whiteSpace:"normal",textAlign:"center",transform:"translateY(-50%)",[r]:{fontSize:e.circleIconFontSize}},[`${t}-circle&-status-exception`]:{[`${t}-text`]:{color:e.colorError}},[`${t}-circle&-status-success`]:{[`${t}-text`]:{color:e.colorSuccess}}},[`${t}-inline-circle`]:{lineHeight:1,[`${t}-inner`]:{verticalAlign:"bottom"}}}},Xe=e=>{const{componentCls:t}=e;return{[t]:{[`${t}-steps`]:{display:"inline-block","&-outer":{display:"flex",flexDirection:"row",alignItems:"center"},"&-item":{flexShrink:0,minWidth:e.progressStepMinWidth,marginInlineEnd:e.progressStepMarginInlineEnd,backgroundColor:e.remainingColor,transition:`all ${e.motionDurationSlow}`,"&-active":{backgroundColor:e.defaultColor}}}}}},Ge=e=>{const{componentCls:t,iconCls:r}=e;return{[t]:{[`${t}-small&-line, ${t}-small&-line ${t}-text ${r}`]:{fontSize:e.fontSizeSM}}}},He=e=>({circleTextColor:e.colorText,defaultColor:e.colorInfo,remainingColor:e.colorFillSecondary,lineBorderRadius:100,circleTextFontSize:"1em",circleIconFontSize:`${e.fontSize/e.fontSizeSM}em`}),Ve=me("Progress",e=>{const t=e.calc(e.marginXXS).div(2).equal(),r=ve(e,{progressStepMarginInlineEnd:t,progressStepMinWidth:t,progressActiveMotionDuration:"2.4s"});return[Be(r),Fe(r),Xe(r),Ge(r)]},He);var Ke=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,o=Object.getOwnPropertySymbols(e);n<o.length;n++)t.indexOf(o[n])<0&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]]);return r};const Ue=e=>{let t=[];return Object.keys(e).forEach(r=>{const o=parseFloat(r.replace(/%/g,""));Number.isNaN(o)||t.push({key:o,value:e[r]})}),t=t.sort((r,o)=>r.key-o.key),t.map(({key:r,value:o})=>`${o} ${r}%`).join(", ")},qe=(e,t)=>{const{from:r=ee.blue,to:o=ee.blue,direction:n=t==="rtl"?"to left":"to right"}=e,c=Ke(e,["from","to","direction"]);if(Object.keys(c).length!==0){const l=Ue(c),a=`linear-gradient(${n}, ${l})`;return{background:a,[Q]:a}}const s=`linear-gradient(${n}, ${r}, ${o})`;return{background:s,[Q]:s}},Qe=e=>{const{prefixCls:t,direction:r,percent:o,size:n,strokeWidth:c,strokeColor:s,strokeLinecap:l="round",children:a,trailColor:g=null,percentPosition:u,success:f}=e,{align:m,type:h}=u,y=s&&typeof s!="string"?qe(s,r):{[Q]:s,background:s},C=l==="square"||l==="butt"?0:void 0,S=n??[-1,c||(n==="small"?6:8)],[p,v]=Y(S,"line",{strokeWidth:c}),O={backgroundColor:g||void 0,borderRadius:C},P=Object.assign(Object.assign({width:`${N(o)}%`,height:v,borderRadius:C},y),{[ce]:N(o)/100}),b=q(e),$={width:`${N(b)}%`,height:v,borderRadius:C,backgroundColor:f==null?void 0:f.strokeColor},I={width:p<0?"100%":p},x=i.createElement("div",{className:`${t}-inner`,style:O},i.createElement("div",{className:M(`${t}-bg`,`${t}-bg-${h}`),style:P},h==="inner"&&a),b!==void 0&&i.createElement("div",{className:`${t}-success-bg`,style:$})),w=h==="outer"&&m==="start",d=h==="outer"&&m==="end";return h==="outer"&&m==="center"?i.createElement("div",{className:`${t}-layout-bottom`},x,a):i.createElement("div",{className:`${t}-outer`,style:I},w&&a,x,d&&a)},Ye=e=>{const{size:t,steps:r,rounding:o=Math.round,percent:n=0,strokeWidth:c=8,strokeColor:s,trailColor:l=null,prefixCls:a,children:g}=e,u=o(r*(n/100)),m=t??[t==="small"?2:14,c],[h,y]=Y(m,"step",{steps:r,strokeWidth:c}),C=h/r,S=Array.from({length:r});for(let p=0;p<r;p++){const v=Array.isArray(s)?s[p]:s;S[p]=i.createElement("div",{key:p,className:M(`${a}-steps-item`,{[`${a}-steps-item-active`]:p<=u-1}),style:{backgroundColor:p<=u-1?v:l,width:C,height:y}})}return i.createElement("div",{className:`${a}-steps-outer`},S,g)};var Ze=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,o=Object.getOwnPropertySymbols(e);n<o.length;n++)t.indexOf(o[n])<0&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]]);return r};const ze=["normal","exception","active","success"],tt=i.forwardRef((e,t)=>{const{prefixCls:r,className:o,rootClassName:n,steps:c,strokeColor:s,percent:l=0,size:a="default",showInfo:g=!0,type:u="line",status:f,format:m,style:h,percentPosition:y={}}=e,C=Ze(e,["prefixCls","className","rootClassName","steps","strokeColor","percent","size","showInfo","type","status","format","style","percentPosition"]),{align:S="end",type:p="outer"}=y,v=Array.isArray(s)?s[0]:s,O=typeof s=="string"||Array.isArray(s)?s:void 0,P=i.useMemo(()=>{if(v){const j=typeof v=="string"?v:Object.values(v)[0];return new be(j).isLight()}return!1},[s]),b=i.useMemo(()=>{var j,k;const E=q(e);return parseInt(E!==void 0?(j=E??0)===null||j===void 0?void 0:j.toString():(k=l??0)===null||k===void 0?void 0:k.toString(),10)},[l,e.success,e.successPercent]),$=i.useMemo(()=>!ze.includes(f)&&b>=100?"success":f||"normal",[f,b]),{getPrefixCls:I,direction:x,progress:w}=i.useContext($e),d=I("progress",r),[H,V,_]=Ve(d),A=u==="line",W=A&&!c,D=i.useMemo(()=>{if(!g)return null;const j=q(e);let k;const E=m||(L=>`${L}%`),R=A&&P&&p==="inner";return p==="inner"||m||$!=="exception"&&$!=="success"?k=E(N(l),N(j)):$==="exception"?k=A?i.createElement(xe,null):i.createElement(ke,null):$==="success"&&(k=A?i.createElement(Pe,null):i.createElement(we,null)),i.createElement("span",{className:M(`${d}-text`,{[`${d}-text-bright`]:R,[`${d}-text-${S}`]:W,[`${d}-text-${p}`]:W}),title:typeof k=="string"?k:void 0},k)},[g,l,b,$,u,d,m]);let B;u==="line"?B=c?i.createElement(Ye,Object.assign({},e,{strokeColor:O,prefixCls:d,steps:typeof c=="object"?c.count:c}),D):i.createElement(Qe,Object.assign({},e,{strokeColor:v,prefixCls:d,direction:x,percentPosition:{align:S,type:p}}),D):(u==="circle"||u==="dashboard")&&(B=i.createElement(Me,Object.assign({},e,{strokeColor:v,prefixCls:d,progressStatus:$}),D));const K=M(d,`${d}-status-${$}`,{[`${d}-${u==="dashboard"&&"circle"||u}`]:u!=="line",[`${d}-inline-circle`]:u==="circle"&&Y(a,"circle")[0]<=20,[`${d}-line`]:W,[`${d}-line-align-${S}`]:W,[`${d}-line-position-${p}`]:W,[`${d}-steps`]:c,[`${d}-show-info`]:g,[`${d}-${a}`]:typeof a=="string",[`${d}-rtl`]:x==="rtl"},w==null?void 0:w.className,o,n,V,_);return H(i.createElement("div",Object.assign({ref:t,style:Object.assign(Object.assign({},w==null?void 0:w.style),h),className:K,role:"progressbar","aria-valuenow":b,"aria-valuemin":0,"aria-valuemax":100},Se(C,["trailColor","strokeWidth","width","gapDegree","gapPosition","strokeLinecap","success","successPercent"])),B))});export{tt as P};
