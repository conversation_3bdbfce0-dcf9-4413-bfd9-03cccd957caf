import { useMemo, useRef } from 'react';
import { useSettings } from './useSettings';
import ChartConfigurationManager from '../config/ChartConfigurationManager';

/**
 * Unified Chart Configuration Hook
 * Provides centralized chart configuration to all chart components
 * Automatically updates when settings change and includes performance optimizations
 */
export const useUnifiedChartConfig = (options = {}) => {
  const { settings, charts, theme } = useSettings();
  const managerRef = useRef(null);
  
  // Create or update configuration manager
  const configManager = useMemo(() => {
    if (!managerRef.current) {
      managerRef.current = new ChartConfigurationManager(settings);
    } else {
      managerRef.current.updateSettings(settings);
    }
    return managerRef.current;
  }, [settings]);

  // Memoize configuration object to prevent unnecessary re-renders
  const config = useMemo(() => {
    const {
      chartType = 'bar',
      allowedTypes = ['bar', 'line', 'pie'],
      customOptions = {}
    } = options;

    return {
      // Core configuration
      manager: configManager,
      
      // Quick access to common configurations
      chartType: configManager.getChartType(chartType, allowedTypes),
      height: configManager.getChartHeight(),
      margins: configManager.getChartMargins(),
      colors: configManager.getColorScheme(),
      
      // Recharts-specific configurations
      axisConfig: configManager.getAxisConfig(),
      gridConfig: configManager.getGridConfig(),
      tooltipConfig: configManager.getTooltipConfig(),
      legendConfig: configManager.getLegendConfig(),
      animationConfig: configManager.getAnimationConfig(),
      hoverEffectsConfig: configManager.getHoverEffectsConfig(),
      clickToExpandConfig: configManager.getClickToExpandConfig(),
      responsiveContainerProps: configManager.getResponsiveContainerProps(),
      
      // Chart element configurations
      getBarElementConfig: (color, index) => configManager.getBarElementConfig(color, index),
      getLineElementConfig: (color, index) => configManager.getLineElementConfig(color, index),
      
      // Display configurations
      displayConfig: configManager.getDisplayConfig(),
      interactionConfig: configManager.getInteractionConfig(),
      performanceConfig: configManager.getPerformanceConfig(),
      
      // Utility methods
      applySettingsToData: (data, type) => configManager.applySettingsToData(data, type),
      areAnimationsActive: () => configManager.areAnimationsActive(),
      getTextColor: () => configManager.getTextColor(),
      getPrimaryColor: () => configManager.getPrimaryColor(),
      
      // Complete chart configuration
      getChartConfig: (type, customOpts) => configManager.getChartConfig(type, { ...customOptions, ...customOpts }),
      
      // Raw settings access (for backward compatibility)
      settings,
      charts,
      theme
    };
  }, [configManager, options, settings, charts, theme]);

  return config;
};

/**
 * Specialized hook for Recharts components
 * Provides Recharts-specific configuration with optimized defaults
 */
export const useRechartsConfig = (options = {}) => {
  const config = useUnifiedChartConfig(options);
  
  // Return Recharts-optimized configuration
  return {
    ...config,
    // Recharts-specific convenience methods
    getResponsiveContainer: () => config.responsiveContainerProps,
    getAxis: () => config.axisConfig,
    getGrid: () => config.gridConfig,
    getTooltip: () => config.tooltipConfig,
    getLegend: () => config.legendConfig,
    getAnimation: () => config.animationConfig,
    getHover: () => config.hoverEffectsConfig,
    
    // Chart-specific configurations
    getBarConfig: (color, index) => ({
      ...config.getBarElementConfig(color, index),
      maxBarSize: config.displayConfig.compactMode ? 30 : 40
    }),
    
    getLineConfig: (color, index) => ({
      ...config.getLineElementConfig(color, index),
      type: 'monotone'
    }),
    
    getPieConfig: () => ({
      cx: '50%',
      cy: '50%',
      outerRadius: 80,
      dataKey: 'value',
      colors: config.colors,
      label: config.displayConfig.showDataLabels
    })
  };
};

/**
 * Hook for dynamic chart components
 * Provides chart type switching functionality with validation
 */
export const useDynamicChartConfig = (options = {}) => {
  const {
    fallbackType = 'bar',
    allowedTypes = ['bar', 'line'],
    propChartType = null
  } = options;
  
  const config = useUnifiedChartConfig({
    ...options,
    chartType: fallbackType,
    allowedTypes
  });
  
  // Determine final chart type with priority logic
  const finalChartType = useMemo(() => {
    // 1. Use prop override if provided and allowed
    if (propChartType && allowedTypes.includes(propChartType)) {
      return propChartType;
    }
    
    // 2. Use default chart type setting if allowed
    const defaultType = config.charts.defaultType || fallbackType;
    if (allowedTypes.includes(defaultType)) {
      return defaultType;
    }
    
    // 3. Fall back to fallbackType
    return fallbackType;
  }, [propChartType, config.charts.defaultType, fallbackType, allowedTypes]);
  
  return {
    ...config,
    chartType: finalChartType,
    isDynamic: true,
    allowedTypes,
    
    // Dynamic chart utilities
    canSwitchTo: (type) => allowedTypes.includes(type),
    getConfigForType: (type) => {
      if (!allowedTypes.includes(type)) {
        console.warn(`Chart type "${type}" not allowed. Allowed types:`, allowedTypes);
        return config.getChartConfig(fallbackType);
      }
      return config.getChartConfig(type);
    }
  };
};

/**
 * Performance-optimized hook for high-frequency updates
 * Uses shallow comparison and selective memoization
 */
export const useOptimizedChartConfig = (options = {}) => {
  const { settings } = useSettings();
  const managerRef = useRef(null);
  
  // Only update manager when specific settings change
  const relevantSettings = useMemo(() => ({
    charts: settings.charts,
    theme: settings.theme
  }), [settings.charts, settings.theme]);
  
  const configManager = useMemo(() => {
    if (!managerRef.current) {
      managerRef.current = new ChartConfigurationManager(settings);
    } else {
      managerRef.current.updateSettings(settings);
    }
    return managerRef.current;
  }, [relevantSettings]);
  
  // Return minimal configuration for performance-critical components
  return useMemo(() => ({
    manager: configManager,
    height: configManager.getChartHeight(),
    colors: configManager.getColorScheme(),
    animations: configManager.getAnimationConfig(),
    responsive: configManager.getResponsiveConfig(),
    performance: configManager.getPerformanceConfig()
  }), [configManager]);
};

export default useUnifiedChartConfig;
