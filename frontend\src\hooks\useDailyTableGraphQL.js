/**
 * Custom hook for Daily Table GraphQL queries
 * Provides access to all daily production data via GraphQL endpoint
 */
import React from "react" ;
import { useState, useCallback } from 'react';

const GRAPHQL_ENDPOINT = '/api/graphql';

const useDailyTableGraphQL = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Generic GraphQL query executor
  const executeQuery = useCallback(async (query, variables = {}) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(GRAPHQL_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          query,
          variables 
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.errors) {
        throw new Error(result.errors.map(e => e.message).join(', '));
      }

      setLoading(false);
      return result.data;
    } catch (err) {
      setError(err.message);
      setLoading(false);
      throw err;
    }
  }, []);

  // Get all daily production data - FIXED: Now accepts filters
  const getAllDailyProduction = useCallback(async (filters = {}) => {
    const query = `
      query GetAllDailyProduction($filters: FilterInput) {
        getAllDailyProduction(filters: $filters) {
          Machine_Name
          Date_Insert_Day
          Run_Hours_Day
          Down_Hours_Day
          Good_QTY_Day
          Rejects_QTY_Day
          Speed_Day
          Availability_Rate_Day
          Performance_Rate_Day
          Quality_Rate_Day
          OEE_Day
          Shift
        }
      }
    `;
    return executeQuery(query, { filters });
  }, [executeQuery]);

  // Get production chart data with filters
  const getProductionChart = useCallback(async (filters = {}) => {
    const query = `
      query GetProductionChart($filters: FilterInput) {
        getProductionChart(filters: $filters) {
          Date_Insert_Day
          Total_Good_Qty_Day
          Total_Rejects_Qty_Day
          OEE_Day
          Speed_Day
          Availability_Rate_Day
          Performance_Rate_Day
          Quality_Rate_Day
        }
      }
    `;
    return executeQuery(query, { filters });
  }, [executeQuery]);

  // Get production sidecards
  const getProductionSidecards = useCallback(async (filters = {}) => {
    const query = `
      query GetProductionSidecards($filters: FilterInput) {
        getProductionSidecards(filters: $filters) {
          goodqty
          rejetqty
        }
      }
    `;
    return executeQuery(query, { filters });
  }, [executeQuery]);

  // Get unique dates
  const getUniqueDates = useCallback(async () => {
    const query = `
      query {
        getUniqueDates
      }
    `;
    return executeQuery(query);
  }, [executeQuery]);

  // Get machine models
  const getMachineModels = useCallback(async () => {
    const query = `
      query {
        getMachineModels {
          model
        }
      }
    `;
    return executeQuery(query);
  }, [executeQuery]);

  // Get machine names
  const getMachineNames = useCallback(async () => {
    const query = `
      query {
        getMachineNames {
          Machine_Name
        }
      }
    `;
    return executeQuery(query);
  }, [executeQuery]);

  // Get machine performance  // Get machine performance
  const getMachinePerformance = useCallback(async (filters = {}) => {
    const query = `
      query GetMachinePerformance($filters: FilterInput) {
        getMachinePerformance(filters: $filters) {
          Machine_Name
          Shift
          production
          rejects
          downtime
          availability
          performance
          oee
          quality
          disponibilite
        }
      }
    `;
    return executeQuery(query, { filters });
  }, [executeQuery]);

  // Get availability trends
  const getAvailabilityTrend = useCallback(async (filters = {}) => {
    const query = `
      query GetAvailabilityTrend($filters: FilterInput) {
        getAvailabilityTrend(filters: $filters) {
          date
          machine
          disponibilite
        }
      }
    `;
    return executeQuery(query, { filters });
  }, [executeQuery]);

  // Get performance metrics
  const getPerformanceMetrics = useCallback(async (filters = {}) => {
    const query = `
      query GetPerformanceMetrics($filters: FilterInput) {
        getPerformanceMetrics(filters: $filters) {
          machine
          model
          disponibilite
          stops
          mttr
          mtbf
        }
      }
    `;
    return executeQuery(query, { filters });
  }, [executeQuery]);

  // Composite function to get dashboard data
  const getDashboardData = useCallback(async (filters = {}) => {
    try {
      const [
        productionChart,
        sidecards,
        machinePerformance,
        availabilityTrend
      ] = await Promise.all([
        getProductionChart(filters),
        getProductionSidecards(filters),
        getMachinePerformance(filters),
        getAvailabilityTrend(filters)
      ]);

      return {
        productionChart: productionChart?.getProductionChart || [],
        sidecards: sidecards?.getProductionSidecards || { goodqty: 0, rejetqty: 0 },
        machinePerformance: machinePerformance?.getMachinePerformance || [],
        availabilityTrend: availabilityTrend?.getAvailabilityTrend || []
      };
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      throw err;
    }
  }, [getProductionChart, getProductionSidecards, getMachinePerformance, getAvailabilityTrend]);

  return {
    // State
    loading,
    error,
    
    // Individual query functions
    getAllDailyProduction,
    getProductionChart,
    getProductionSidecards,
    getUniqueDates,
    getMachineModels,
    getMachineNames,
    getMachinePerformance,
    getAvailabilityTrend,
    getPerformanceMetrics,
    
    // Composite functions
    getDashboardData,
    
    // Utility
    executeQuery
  };
};

export default useDailyTableGraphQL;
