import React, { memo } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axi<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Toolt<PERSON> as Recharts<PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  AreaChart,
  Area,
  ComposedChart,
} from "recharts";
import { Empty } from "antd";
import dayjs from "dayjs";
import { normalizePercentage } from "../../../utils/dataUtils";
import { formatDateLabel } from "../../../utils/chartDataUtils";
import SOMIPEM_COLORS from "../../../styles/brand-colors";
import { useEnhancedRechartsConfig } from "../../../utils/enhancedRechartsConfig";
import { useSettings } from "../../../hooks/useSettings";

// SOMIPEM Brand Color Palette (Blue Theme Only)
const COLORS = [
  SOMIPEM_COLORS.PRIMARY_BLUE,     // #1E3A8A - Primary blue
  SOMIPEM_COLORS.SECONDARY_BLUE,   // #3B82F6 - Secondary blue
  SOMIPEM_COLORS.CHART_TERTIARY,   // #93C5FD - Tertiary blue
  SOMIPEM_COLORS.CHART_QUATERNARY, // #DBEAFE - Quaternary blue
  "#60A5FA", // Light blue
  "#1D4ED8", // Dark blue
  "#3730A3", // Darker blue
  "#1E40AF", // Medium dark blue
  "#2563EB", // Medium blue
  "#6366F1", // Indigo blue
];

/**
 * Enhanced Quantity Bar Chart with expansion support (DATE-BASED)
 */
export const EnhancedQuantityBarChart = memo(({
  data,
  title,
  dataKey,
  color,
  label = "Quantité",
  tooltipLabel = "Quantité",
  isKg = false,
  height = 300,
  enhanced = false,
  expanded = false,
  zoom = 1,
  selectedDataPoints = [],
  chartConfig = {},
  dimensions = {},
  isModal = false,
}) => {
  // Get settings for enhanced chart configuration
  const { settings, charts, theme } = useSettings();

  // Enhanced chart configuration
  const enhancedChartConfig = useEnhancedRechartsConfig({
    charts,
    theme
  });
  if (!data || data.length === 0) {
    return (
      <div style={{ height, display: "flex", alignItems: "center", justifyContent: "center" }}>
        <Empty description="Aucune donnée disponible" />
      </div>
    );
  }



  // Get enhanced configuration
  const responsiveProps = enhancedChartConfig.getResponsiveContainerProps();
  const gridConfig = enhancedChartConfig.getGridConfig();
  const axisConfig = enhancedChartConfig.getAxisConfig();
  const tooltipConfig = enhancedChartConfig.getTooltipConfig();
  const legendConfig = enhancedChartConfig.getLegendConfig();
  const chartHeight = enhancedChartConfig.getChartHeight() || height;
  const chartMargins = enhancedChartConfig.getChartMargins();

  // Use provided dimensions or fallback to enhanced defaults
  const margin = dimensions.margin || chartMargins;
  const fontSize = dimensions.fontSize || (enhanced ? 14 : 12);
  const labelAngle = dimensions.labelAngle || (enhanced ? -45 : 0);
  const labelHeight = dimensions.labelHeight || (enhanced ? 100 : 60);

  return (
    <ResponsiveContainer {...responsiveProps} height={chartHeight}>
      <BarChart data={data} margin={margin}>
        <CartesianGrid {...gridConfig} />
        <XAxis
          dataKey="date"
          {...axisConfig}
          tick={{ fill: "#666", fontSize }}
          tickFormatter={(date) => formatDateLabel(date, expanded || enhanced, data.length)}
          interval={chartConfig.labelInterval !== undefined ? chartConfig.labelInterval : (enhanced ? 0 : "preserveStartEnd")}
          angle={labelAngle}
          textAnchor={labelAngle !== 0 ? "end" : "middle"}
          height={labelHeight}
          minTickGap={expanded ? 5 : 10}
        />
        <YAxis
          {...axisConfig}
          tick={{ fontSize }}
          tickFormatter={(value) => value.toLocaleString()}
          label={{
            value: isKg ? `${label} (kg)` : label,
            angle: -90,
            position: "insideLeft",
            style: { fill: "#666", fontSize },
          }}
        />
        <RechartsTooltip
          {...tooltipConfig}
          formatter={(value) => {
            const numValue = parseFloat(value);
            return [
              !isNaN(numValue) ? numValue.toLocaleString() : "N/A",
              isKg ? `${tooltipLabel} (kg)` : tooltipLabel
            ];
          }}
          labelFormatter={(label) => {
            try {
              if (label && dayjs(label).isValid()) {
                return `Date: ${dayjs(label).format("DD/MM/YYYY")}`;
              }
              return "Date: N/A";
            } catch (e) {
              return "Date: N/A";
            }
          }}
        />
        {(enhanced || charts.showLegend) && <Legend {...legendConfig} />}
        <Bar
          dataKey={dataKey}
          name={tooltipLabel}
          fill={color}
          maxBarSize={chartConfig.maxBarSize || (enhanced ? 60 : 40)}
          radius={enhanced || expanded ? [4, 4, 0, 0] : [0, 0, 0, 0]}
          {...enhancedChartConfig.getBarElementConfig(color)}
        />
      </BarChart>
    </ResponsiveContainer>
  );
});

/**
 * Enhanced Shift Bar Chart with expansion support (SHIFT-BASED)
 */
export const EnhancedShiftBarChart = memo(({
  data,
  title,
  dataKey,
  color,
  label = "Quantité",
  tooltipLabel = "Quantité",
  isKg = false,
  height = 300,
  enhanced = false,
  expanded = false,
  zoom = 1,
  selectedDataPoints = [],
  chartConfig = {},
  dimensions = {},
  isModal = false,
}) => {
  // Debug logging for downtime chart (simplified)
  if (title && title.includes('Temps d\'arrêt') && data?.length > 0) {
    console.log('EnhancedShiftBarChart - Downtime data received:', data.map(item => ({
      Shift: item.Shift,
      [dataKey]: item[dataKey]
    })));
  }

  if (!data || data.length === 0) {
    return (
      <div style={{ height, display: "flex", alignItems: "center", justifyContent: "center" }}>
        <Empty description="Aucune donnée disponible" />
      </div>
    );
  }



  // Use provided dimensions or fallback to defaults
  const margin = dimensions.margin || (enhanced ? { top: 30, right: 50, left: 50, bottom: 100 } : { top: 16, right: 24, left: 24, bottom: 60 });
  const fontSize = dimensions.fontSize || (enhanced ? 14 : 12);
  const labelAngle = dimensions.labelAngle || (enhanced ? -45 : 0);
  const labelHeight = dimensions.labelHeight || (enhanced ? 100 : 60);

  return (
    <ResponsiveContainer width="100%" height={height}>
      <BarChart data={data} margin={margin}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f5f5f5" />
        <XAxis
          dataKey="Shift"
          tick={{ fill: "#666", fontSize }}
          tickFormatter={(shift) => shift || "N/A"}
          interval={chartConfig.labelInterval !== undefined ? chartConfig.labelInterval : (enhanced ? 0 : "preserveStartEnd")}
          angle={labelAngle}
          textAnchor={labelAngle !== 0 ? "end" : "middle"}
          height={labelHeight}
          minTickGap={expanded ? 5 : 10}
        />
        <YAxis
          tick={{ fontSize }}
          tickFormatter={(value) => value.toLocaleString()}
          domain={title && title.includes('Temps d\'arrêt') ? [0, 'dataMax'] : ['auto', 'auto']}
          label={{
            value: isKg ? `${label} (kg)` : label,
            angle: -90,
            position: "insideLeft",
            style: { fill: "#666", fontSize },
          }}
        />
        <RechartsTooltip
          contentStyle={{
            backgroundColor: "#fff",
            border: "1px solid #f0f0f0",
            borderRadius: 8,
            boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
            fontSize: enhanced ? 14 : 12,
          }}
          formatter={(value) => {
            const numValue = parseFloat(value);
            return [
              !isNaN(numValue) ? numValue.toLocaleString() : "N/A",
              isKg ? `${tooltipLabel} (kg)` : tooltipLabel
            ];
          }}
          labelFormatter={(label) => `Équipe: ${label}`}
        />
        {enhanced && <Legend />}
        <Bar
          dataKey={dataKey}
          name={tooltipLabel}
          fill={color}
          maxBarSize={chartConfig.maxBarSize || (enhanced ? 60 : 40)}
          radius={enhanced || expanded ? [4, 4, 0, 0] : [0, 0, 0, 0]}
        />
      </BarChart>
    </ResponsiveContainer>
  );
});

/**
 * Enhanced TRS Line Chart with expansion support
 */
export const EnhancedTRSLineChart = memo(({
  data,
  color = COLORS[0],
  height = 300,
  enhanced = false,
  expanded = false,
  zoom = 1,
  selectedDataPoints = [],
  chartConfig = {},
  dimensions = {},
  isModal = false,
}) => {
  if (!data || data.length === 0) {
    return (
      <div style={{ height, display: "flex", alignItems: "center", justifyContent: "center" }}>
        <Empty description="Aucune donnée TRS disponible" />
      </div>
    );
  }

  // Use provided dimensions or fallback to defaults
  const margin = dimensions.margin || (enhanced ? { top: 30, right: 50, left: 50, bottom: 100 } : { top: 16, right: 24, left: 24, bottom: 60 });
  const fontSize = dimensions.fontSize || (enhanced ? 14 : 12);
  const labelAngle = dimensions.labelAngle || (enhanced ? -45 : 0);
  const labelHeight = dimensions.labelHeight || (enhanced ? 100 : 60);

  return (
    <ResponsiveContainer width="100%" height={height}>
      <LineChart data={data} margin={margin}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f5f5f5" />
        <XAxis
          dataKey="date"
          tick={{ fill: "#666", fontSize }}
          tickFormatter={(date) => formatDateLabel(date, expanded || enhanced, data.length)}
          interval={chartConfig.labelInterval !== undefined ? chartConfig.labelInterval : (enhanced ? 0 : "preserveStartEnd")}
          angle={labelAngle}
          textAnchor={labelAngle !== 0 ? "end" : "middle"}
          height={labelHeight}
          minTickGap={expanded ? 5 : 10}
        />
        <YAxis
          tick={{ fontSize }}
          tickFormatter={(value) => `${value}%`}
          domain={[0, 100]}
          label={{
            value: "TRS (%)",
            angle: -90,
            position: "insideLeft",
            style: { fill: "#666", fontSize },
          }}
        />
        <RechartsTooltip
          contentStyle={{
            backgroundColor: "#fff",
            border: "1px solid #f0f0f0",
            borderRadius: 8,
            boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
            fontSize: enhanced ? 14 : 12,
          }}
          formatter={(value) => {
            let parsed = parseFloat(value);
            const isValidNumber = !isNaN(parsed);
            if (isValidNumber && parsed <= 1 && parsed > 0) {
              parsed = parsed * 100;
            }
            return [isValidNumber ? `${parsed.toFixed(2)}%` : `${value}%`, "TRS"];
          }}
          labelFormatter={(label) => {
            try {
              if (label && dayjs(label).isValid()) {
                return `Date: ${dayjs(label).format("DD/MM/YYYY")}`;
              }
              return "Date: N/A";
            } catch (e) {
              return "Date: N/A";
            }
          }}
        />
        {enhanced && <Legend />}
        <Line
          type="monotone"
          dataKey="oee"
          name="TRS"
          stroke={color}
          strokeWidth={chartConfig.strokeWidth || (enhanced || expanded ? 3 : 2)}
          dot={{ r: chartConfig.dotSize || (enhanced || expanded ? 6 : 4), fill: color }}
          activeDot={{ r: (chartConfig.dotSize || (enhanced || expanded ? 6 : 4)) + 2, fill: "#fff", stroke: color, strokeWidth: 2 }}
        />
      </LineChart>
    </ResponsiveContainer>
  );
});

/**
 * Enhanced TRS Line Chart with expansion support (SHIFT-BASED)
 */
export const EnhancedShiftTRSLineChart = memo(({
  data,
  color = COLORS[0],
  height = 300,
  enhanced = false,
  expanded = false,
  zoom = 1,
  selectedDataPoints = [],
  chartConfig = {},
  dimensions = {},
  isModal = false,
}) => {
  if (!data || data.length === 0) {
    return (
      <div style={{ height, display: "flex", alignItems: "center", justifyContent: "center" }}>
        <Empty description="Aucune donnée TRS disponible" />
      </div>
    );
  }

  // Use provided dimensions or fallback to defaults
  const margin = dimensions.margin || (enhanced ? { top: 30, right: 50, left: 50, bottom: 100 } : { top: 16, right: 24, left: 24, bottom: 60 });
  const fontSize = dimensions.fontSize || (enhanced ? 14 : 12);
  const labelAngle = dimensions.labelAngle || (enhanced ? -45 : 0);
  const labelHeight = dimensions.labelHeight || (enhanced ? 100 : 60);

  return (
    <ResponsiveContainer width="100%" height={height}>
      <LineChart data={data} margin={margin}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f5f5f5" />
        <XAxis
          dataKey="Shift"
          tick={{ fill: "#666", fontSize }}
          tickFormatter={(shift) => shift || "N/A"}
          interval={chartConfig.labelInterval !== undefined ? chartConfig.labelInterval : (enhanced ? 0 : "preserveStartEnd")}
          angle={labelAngle}
          textAnchor={labelAngle !== 0 ? "end" : "middle"}
          height={labelHeight}
          minTickGap={expanded ? 5 : 10}
        />
        <YAxis
          tick={{ fontSize }}
          tickFormatter={(value) => `${value}%`}
          domain={[0, 100]}
          label={{
            value: "TRS (%)",
            angle: -90,
            position: "insideLeft",
            style: { fill: "#666", fontSize },
          }}
        />
        <RechartsTooltip
          contentStyle={{
            backgroundColor: "#fff",
            border: "1px solid #f0f0f0",
            borderRadius: 8,
            boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
            fontSize: enhanced ? 14 : 12,
          }}
          formatter={(value) => {
            let parsed = parseFloat(value);
            const isValidNumber = !isNaN(parsed);
            if (isValidNumber && parsed <= 1 && parsed > 0) {
              parsed = parsed * 100;
            }
            return [isValidNumber ? `${parsed.toFixed(2)}%` : `${value}%`, "TRS"];
          }}
          labelFormatter={(label) => `Équipe: ${label}`}
        />
        {enhanced && <Legend />}
        <Line
          type="monotone"
          dataKey="oee"
          name="TRS"
          stroke={color}
          strokeWidth={chartConfig.strokeWidth || (enhanced || expanded ? 3 : 2)}
          dot={{ r: chartConfig.dotSize || (enhanced || expanded ? 6 : 4), fill: color }}
          activeDot={{ r: (chartConfig.dotSize || (enhanced || expanded ? 6 : 4)) + 2, fill: "#fff", stroke: color, strokeWidth: 2 }}
        />
      </LineChart>
    </ResponsiveContainer>
  );
});

/**
 * Enhanced Performance Line Chart with expansion support
 */
export const EnhancedPerformanceLineChart = memo(({
  data,
  color = COLORS[5],
  height = 300,
  enhanced = false,
  expanded = false,
  zoom = 1,
  selectedDataPoints = [],
  chartConfig = {},
  dimensions = {},
  isModal = false,
}) => {
  if (!data || data.length === 0) {
    return (
      <div style={{ height, display: "flex", alignItems: "center", justifyContent: "center" }}>
        <Empty description="Aucune donnée de performance disponible" />
      </div>
    );
  }



  // Use provided dimensions or fallback to defaults
  const margin = dimensions.margin || (enhanced ? { top: 30, right: 50, left: 50, bottom: 100 } : { top: 16, right: 24, left: 24, bottom: 60 });
  const fontSize = dimensions.fontSize || (enhanced ? 14 : 12);
  const labelAngle = dimensions.labelAngle || (enhanced ? -45 : 0);
  const labelHeight = dimensions.labelHeight || (enhanced ? 100 : 60);

  return (
    <ResponsiveContainer width="100%" height={height}>
      <LineChart data={data} margin={margin}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f5f5f5" />
        <XAxis
          dataKey="Shift"
          tick={{ fill: "#666", fontSize }}
          tickFormatter={(shift) => shift || "N/A"}
          interval={chartConfig.labelInterval !== undefined ? chartConfig.labelInterval : (enhanced ? 0 : "preserveStartEnd")}
          angle={labelAngle}
          textAnchor={labelAngle !== 0 ? "end" : "middle"}
          height={labelHeight}
          minTickGap={expanded ? 5 : 10}
        />
        <YAxis
          tick={{ fontSize }}
          tickFormatter={(value) => `${value}%`}
          domain={[0, 100]}
          label={{
            value: "Performance (%)",
            angle: -90,
            position: "insideLeft",
            style: { fill: "#666", fontSize },
          }}
        />
        <RechartsTooltip
          contentStyle={{
            backgroundColor: "#fff",
            border: "1px solid #f0f0f0",
            borderRadius: 8,
            boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
            fontSize: enhanced ? 14 : 12,
          }}
          formatter={(value) => {
            let parsed = parseFloat(value);
            const isValidNumber = !isNaN(parsed);
            if (isValidNumber && parsed <= 1 && parsed > 0) {
              parsed = parsed * 100;
            }
            return [isValidNumber ? `${parsed.toFixed(2)}%` : `${value}%`, "Performance"];
          }}
          labelFormatter={(label) => `Équipe: ${label}`}
        />
        {enhanced && <Legend />}
        <Line
          type="monotone"
          dataKey="performance"
          name="Performance"
          stroke={color}
          strokeWidth={chartConfig.strokeWidth || (enhanced || expanded ? 3 : 2)}
          dot={{ r: chartConfig.dotSize || (enhanced || expanded ? 6 : 4), fill: color }}
          activeDot={{ r: (chartConfig.dotSize || (enhanced || expanded ? 6 : 4)) + 2, fill: "#fff", stroke: color, strokeWidth: 2 }}
        />
      </LineChart>
    </ResponsiveContainer>
  );
});

/**
 * Enhanced Cycle Time Line Chart with expansion support
 */
export const EnhancedCycleTimeLineChart = memo(({
  data,
  color = COLORS[1],
  height = 300,
  enhanced = false,
  expanded = false,
  zoom = 1,
  selectedDataPoints = [],
  chartConfig = {},
  dimensions = {},
  isModal = false,
}) => {
  if (!data || data.length === 0) {
    return (
      <div style={{ height, display: "flex", alignItems: "center", justifyContent: "center" }}>
        <Empty description="Aucune donnée de cycle disponible" />
      </div>
    );
  }

  // Use provided dimensions or fallback to defaults
  const margin = dimensions.margin || (enhanced ? { top: 30, right: 50, left: 50, bottom: 100 } : { top: 16, right: 24, left: 24, bottom: 60 });
  const fontSize = dimensions.fontSize || (enhanced ? 14 : 12);
  const labelAngle = dimensions.labelAngle || (enhanced ? -45 : 0);
  const labelHeight = dimensions.labelHeight || (enhanced ? 100 : 60);

  return (
    <ResponsiveContainer width="100%" height={height}>
      <LineChart data={data} margin={margin}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f5f5f5" />
        <XAxis
          dataKey="date"
          tick={{ fill: "#666", fontSize }}
          tickFormatter={(date) => formatDateLabel(date, expanded || enhanced, data.length)}
          interval={chartConfig.labelInterval !== undefined ? chartConfig.labelInterval : (enhanced ? 0 : "preserveStartEnd")}
          angle={labelAngle}
          textAnchor={labelAngle !== 0 ? "end" : "middle"}
          height={labelHeight}
          minTickGap={expanded ? 5 : 10}
        />
        <YAxis
          tick={{ fontSize }}
          tickFormatter={(value) => `${value}s`}
          label={{
            value: "Cycle De Temps (s)",
            angle: -90,
            position: "insideLeft",
            style: { fill: "#666", fontSize },
          }}
        />
        <RechartsTooltip
          contentStyle={{
            backgroundColor: "#fff",
            border: "1px solid #f0f0f0",
            borderRadius: 8,
            boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
            fontSize: enhanced ? 14 : 12,
          }}
          formatter={(value) => {
            const isValidNumber = typeof value === "number" && !isNaN(value);
            return [
              isValidNumber ? `${value.toFixed(2)}s` : `${value}s`,
              "Cycle De Temps"
            ];
          }}
          labelFormatter={(label) => {
            try {
              if (label && dayjs(label).isValid()) {
                return `Date: ${dayjs(label).format("DD/MM/YYYY")}`;
              }
              return "Date: N/A";
            } catch (e) {
              return "Date: N/A";
            }
          }}
        />
        {enhanced && <Legend />}
        <Line
          type="monotone"
          dataKey="speed"
          name="Cycle De Temps"
          stroke={color}
          strokeWidth={chartConfig.strokeWidth || (enhanced || expanded ? 3 : 2)}
          dot={{ r: chartConfig.dotSize || (enhanced || expanded ? 6 : 4), fill: color }}
          activeDot={{ r: (chartConfig.dotSize || (enhanced || expanded ? 6 : 4)) + 2, fill: "#fff", stroke: color, strokeWidth: 2 }}
        />
      </LineChart>
    </ResponsiveContainer>
  );
});

/**
 * Enhanced Pie Chart with expansion support
 */
export const EnhancedPieChart = memo(({
  data,
  dataKey = "value",
  nameKey = "name",
  colors = COLORS,
  height = 300,
  enhanced = false,
  zoom = 1,
  selectedDataPoints = [],
}) => {
  if (!data || data.length === 0) {
    return (
      <div style={{ height, display: "flex", alignItems: "center", justifyContent: "center" }}>
        <Empty description="Aucune donnée disponible" />
      </div>
    );
  }

  const margin = enhanced ? { top: 20, right: 30, left: 30, bottom: 20 } : { top: 16, right: 24, left: 24, bottom: 16 };

  return (
    <ResponsiveContainer width="100%" height={height}>
      <PieChart margin={margin}>
        <Pie
          data={data}
          dataKey={dataKey}
          nameKey={nameKey}
          cx="50%"
          cy="50%"
          innerRadius={enhanced ? 80 : 60}
          outerRadius={enhanced ? 120 : 80}
          paddingAngle={enhanced ? 8 : 5}
          label={enhanced ? ({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%` : false}
          labelLine={enhanced}
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
          ))}
        </Pie>
        <RechartsTooltip
          contentStyle={{
            backgroundColor: "#fff",
            border: "1px solid #f0f0f0",
            borderRadius: 8,
            boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
            fontSize: enhanced ? 14 : 12,
          }}
        />
        <Legend
          layout={enhanced ? "horizontal" : "vertical"}
          verticalAlign={enhanced ? "bottom" : "middle"}
          align={enhanced ? "center" : "right"}
          wrapperStyle={{
            paddingLeft: enhanced ? 0 : 24,
            paddingTop: enhanced ? 20 : 0,
            fontSize: enhanced ? 14 : 12,
            color: "#666",
          }}
        />
      </PieChart>
    </ResponsiveContainer>
  );
});

/**
 * Enhanced Machine Production Bar Chart (Production Only)
 */
export const EnhancedMachineProductionChart = memo(({
  data,
  height = 300,
  enhanced = false,
  zoom = 1,
  selectedDataPoints = [],
}) => {


  if (!data || data.length === 0) {
    return (
      <div style={{ height, display: "flex", alignItems: "center", justifyContent: "center" }}>
        <Empty description="Aucune donnée de production disponible" />
      </div>
    );
  }

  // Aggregate production data by machine name
  const aggregatedData = data.reduce((acc, item) => {
    const machineName = item.Machine_Name;
    if (!acc[machineName]) {
      acc[machineName] = {
        Machine_Name: machineName,
        production: 0,
      };
    }

    acc[machineName].production += Number(item.production) || 0;

    return acc;
  }, {});

  const chartData = Object.values(aggregatedData);
  const margin = enhanced ? { top: 20, right: 30, left: 30, bottom: 80 } : { top: 16, right: 24, left: 24, bottom: 60 };
  const fontSize = enhanced ? 14 : 12;

  return (
    <ResponsiveContainer width="100%" height={height}>
      <BarChart data={chartData} margin={margin}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f5f5f5" />
        <XAxis
          dataKey="Machine_Name"
          tick={{ fill: "#666", fontSize }}
          interval={0}
          angle={-45}
          textAnchor="end"
          height={enhanced ? 100 : 80}
        />
        <YAxis
          tick={{ fontSize }}
          tickFormatter={(value) => value.toLocaleString()}
          label={{
            value: "Production (pcs)",
            angle: -90,
            position: "insideLeft",
            style: { fill: "#666", fontSize },
          }}
        />
        <RechartsTooltip
          contentStyle={{
            backgroundColor: "#fff",
            border: "1px solid #f0f0f0",
            borderRadius: 8,
            boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
            fontSize: enhanced ? 14 : 12,
          }}
          formatter={(value) => {
            const isNumber = typeof value === "number" && !isNaN(value);
            const formattedValue = isNumber
              ? Number.isInteger(value)
                ? value.toLocaleString()
                : value.toFixed(2)
              : value;

            return [formattedValue, "Production"];
          }}
        />
        {enhanced && <Legend />}
        <Bar
          dataKey="production"
          name="Production"
          fill={COLORS[2]}
          radius={enhanced ? [4, 4, 0, 0] : [0, 0, 0, 0]}
        />
      </BarChart>
    </ResponsiveContainer>
  );
});

/**
 * Enhanced Machine Rejects Bar Chart (Rejects Only)
 */
export const EnhancedMachineRejectsChart = memo(({
  data,
  height = 300,
  enhanced = false,
  zoom = 1,
  selectedDataPoints = [],
}) => {
  if (!data || data.length === 0) {
    return (
      <div style={{ height, display: "flex", alignItems: "center", justifyContent: "center" }}>
        <Empty description="Aucune donnée de rejets disponible" />
      </div>
    );
  }

  // Aggregate rejects data by machine name
  const aggregatedData = data.reduce((acc, item) => {
    const machineName = item.Machine_Name;
    if (!acc[machineName]) {
      acc[machineName] = {
        Machine_Name: machineName,
        rejects: 0,
      };
    }

    acc[machineName].rejects += Number(item.rejects) || 0;

    return acc;
  }, {});

  const chartData = Object.values(aggregatedData);
  const margin = enhanced ? { top: 20, right: 30, left: 30, bottom: 80 } : { top: 16, right: 24, left: 24, bottom: 60 };
  const fontSize = enhanced ? 14 : 12;

  return (
    <ResponsiveContainer width="100%" height={height}>
      <BarChart data={chartData} margin={margin}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f5f5f5" />
        <XAxis
          dataKey="Machine_Name"
          tick={{ fill: "#666", fontSize }}
          interval={0}
          angle={-45}
          textAnchor="end"
          height={enhanced ? 100 : 80}
        />
        <YAxis
          tick={{ fontSize }}
          tickFormatter={(value) => value.toLocaleString()}
          label={{
            value: "Rejets (kg)",
            angle: -90,
            position: "insideLeft",
            style: { fill: "#666", fontSize },
          }}
        />
        <RechartsTooltip
          contentStyle={{
            backgroundColor: "#fff",
            border: "1px solid #f0f0f0",
            borderRadius: 8,
            boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
            fontSize: enhanced ? 14 : 12,
          }}
          formatter={(value) => {
            const isNumber = typeof value === "number" && !isNaN(value);
            const formattedValue = isNumber
              ? Number.isInteger(value)
                ? value.toLocaleString()
                : value.toFixed(2)
              : value;

            return [formattedValue, "Rejets"];
          }}
        />
        {enhanced && <Legend />}
        <Bar
          dataKey="rejects"
          name="Rejets"
          fill={COLORS[4]}
          radius={enhanced ? [4, 4, 0, 0] : [0, 0, 0, 0]}
        />
      </BarChart>
    </ResponsiveContainer>
  );
});

/**
 * Enhanced Machine TRS Bar Chart (TRS Only)
 */
export const EnhancedMachineTRSChart = memo(({
  data,
  height = 300,
  enhanced = false,
  zoom = 1,
  selectedDataPoints = [],
}) => {


  if (!data || data.length === 0) {
    return (
      <div style={{ height, display: "flex", alignItems: "center", justifyContent: "center" }}>
        <Empty description="Aucune donnée TRS disponible" />
      </div>
    );
  }

  // Aggregate TRS data by machine name
  const aggregatedData = data.reduce((acc, item) => {
    const machineName = item.Machine_Name;
    if (!acc[machineName]) {
      acc[machineName] = {
        Machine_Name: machineName,
        trs: 0,
        count: 0,
      };
    }

    let trsValue = Number(item.oee) || 0;
    // Convert to percentage if the value is between 0 and 1
    if (trsValue > 0 && trsValue <= 1) {
      trsValue = trsValue * 100;
    }

    acc[machineName].trs += trsValue;
    acc[machineName].count += 1;

    return acc;
  }, {});

  // Calculate average TRS for each machine
  const chartData = Object.values(aggregatedData).map(item => ({
    Machine_Name: item.Machine_Name,
    trs: item.count > 0 ? (item.trs / item.count) : 0,
  }));



  const margin = enhanced ? { top: 20, right: 30, left: 30, bottom: 80 } : { top: 16, right: 24, left: 24, bottom: 60 };
  const fontSize = enhanced ? 14 : 12;

  return (
    <ResponsiveContainer width="100%" height={height}>
      <BarChart data={chartData} margin={margin}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f5f5f5" />
        <XAxis
          dataKey="Machine_Name"
          tick={{ fill: "#666", fontSize }}
          interval={0}
          angle={-45}
          textAnchor="end"
          height={enhanced ? 100 : 80}
        />
        <YAxis
          tick={{ fontSize }}
          tickFormatter={(value) => `${value.toFixed(1)}%`}
          label={{
            value: "TRS (%)",
            angle: -90,
            position: "insideLeft",
            style: { fill: "#666", fontSize },
          }}
          domain={[0, 100]}
        />
        <RechartsTooltip
          contentStyle={{
            backgroundColor: "#fff",
            border: "1px solid #f0f0f0",
            borderRadius: 8,
            boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
            fontSize: enhanced ? 14 : 12,
          }}
          formatter={(value) => {
            let numValue = parseFloat(value);
            if (isNaN(numValue)) return ["N/A", "TRS"];

            // Value should already be in percentage format from aggregation
            return [`${numValue.toFixed(1)}%`, "TRS"];
          }}
        />
        {enhanced && <Legend />}
        <Bar
          dataKey="trs"
          name="TRS"
          fill={COLORS[5]}
          radius={enhanced ? [4, 4, 0, 0] : [0, 0, 0, 0]}
        />
      </BarChart>
    </ResponsiveContainer>
  );
});

// Set display names
EnhancedQuantityBarChart.displayName = 'EnhancedQuantityBarChart';
EnhancedShiftBarChart.displayName = 'EnhancedShiftBarChart';
EnhancedTRSLineChart.displayName = 'EnhancedTRSLineChart';
EnhancedShiftTRSLineChart.displayName = 'EnhancedShiftTRSLineChart';
EnhancedPerformanceLineChart.displayName = 'EnhancedPerformanceLineChart';
EnhancedCycleTimeLineChart.displayName = 'EnhancedCycleTimeLineChart';
EnhancedPieChart.displayName = 'EnhancedPieChart';
EnhancedMachineProductionChart.displayName = 'EnhancedMachineProductionChart';
EnhancedMachineRejectsChart.displayName = 'EnhancedMachineRejectsChart';
EnhancedMachineTRSChart.displayName = 'EnhancedMachineTRSChart';
