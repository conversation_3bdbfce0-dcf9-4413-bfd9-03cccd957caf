import React, { useState, useCallback, useMemo } from 'react';
import { Card, Button, Tooltip, Space, Grid } from 'antd';
import { FullscreenOutlined, CompressOutlined, ExpandOutlined, DownloadOutlined, ZoomInOutlined, ZoomOutOutlined } from '@ant-design/icons';
import { useUnifiedChartConfig } from '../../../hooks/useUnifiedChartConfig';
import UnifiedChartModal from './UnifiedChartModal';

const { useBreakpoint } = Grid;

/**
 * Enhanced Unified Chart Expansion System
 * Provides responsive chart expansion with consistent behavior across all dashboards
 * Features mobile-optimized interactions and smooth animations
 */
const UnifiedChartExpansion = ({
  children,
  title,
  data,
  chartType = 'bar',
  expandMode = 'modal', // 'modal' or 'inline'
  onExpand,
  onCollapse,
  exportEnabled = false,
  className = '',
  style = {},
  cardProps = {},
  ...otherProps
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);

  // Responsive breakpoint detection
  const screens = useBreakpoint();

  const isMobile = !screens.md;
  const isTablet = screens.md && !screens.lg;
  const isDesktop = screens.lg;

  // Get unified chart configuration with responsive settings
  const chartConfig = useUnifiedChartConfig({
    chartType,
    customOptions: {
      responsive: true,
      mobile: isMobile,
      tablet: isTablet,
      desktop: isDesktop
    }
  });

  // Check if click to expand is enabled with responsive considerations
  const clickToExpandEnabled = chartConfig?.interactionConfig?.clickToExpand !== false;

  // Enhanced expansion handling with responsive optimizations
  const handleExpansion = useCallback(async (e) => {
    if (!clickToExpandEnabled) {
      return;
    }

    // Prevent event propagation if event is provided
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    setIsLoading(true);

    try {
      // Responsive delay based on device and data size
      const delay = isMobile ? 50 : (data && data.length > 100) ? 100 : 0;
      if (delay > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }

      if (expandMode === 'modal') {
        setIsModalVisible(true);
        if (onExpand) onExpand();
      } else {
        const newExpandedState = !isExpanded;
        setIsExpanded(newExpandedState);

        if (newExpandedState && onExpand) {
          onExpand();
        } else if (!newExpandedState && onCollapse) {
          onCollapse();
        }
      }
    } catch (error) {
      console.error('❌ UnifiedChartExpansion - Error during expansion:', error);
    } finally {
      setIsLoading(false);
    }
  }, [expandMode, isExpanded, onExpand, onCollapse, data, clickToExpandEnabled, isMobile]);

  // Handle zoom controls for inline expansion
  const handleZoomIn = useCallback(() => {
    setZoomLevel(prev => Math.min(prev + 0.2, 2));
  }, []);

  const handleZoomOut = useCallback(() => {
    setZoomLevel(prev => Math.max(prev - 0.2, 0.5));
  }, []);

  const handleZoomReset = useCallback(() => {
    setZoomLevel(1);
  }, []);

  // Handle export functionality
  const handleExport = useCallback(() => {
    setIsLoading(true);
    try {
      console.log('Exporting chart:', title);
      // Export functionality would be implemented here
    } finally {
      setTimeout(() => setIsLoading(false), 1000);
    }
  }, [title]);

  // Handle modal close
  const handleModalClose = useCallback(() => {
    setIsModalVisible(false);
    if (onCollapse) {
      onCollapse();
    }
  }, [onCollapse]);

  // Handle card click with responsive behavior
  const handleCardClick = useCallback((e) => {
    // Only trigger expansion if clicking on the card itself, not controls
    if (e.target.closest('.ant-card-extra') || e.target.closest('.chart-container') || e.target.closest('.expand-button')) {
      return;
    }

    // Respect the "Click to Expand" setting and device type
    if (clickToExpandEnabled && !isMobile) {
      handleExpansion(e);
    }
  }, [clickToExpandEnabled, handleExpansion, isMobile]);

  // Enhanced chart controls with responsive design
  const renderChartControls = () => {
    if (!isExpanded || expandMode === 'modal') return null;

    return (
      <Space size={isMobile ? "small" : "middle"}>
        {!isMobile && (
          <>
            <Tooltip title="Zoom avant">
              <Button
                size="small"
                icon={<ZoomInOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleZoomIn();
                }}
                disabled={zoomLevel >= 2}
              />
            </Tooltip>
            <Tooltip title="Zoom arrière">
              <Button
                size="small"
                icon={<ZoomOutOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleZoomOut();
                }}
                disabled={zoomLevel <= 0.5}
              />
            </Tooltip>
            <Tooltip title="Réinitialiser le zoom">
              <Button
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  handleZoomReset();
                }}
                disabled={zoomLevel === 1}
              >
                {Math.round(zoomLevel * 100)}%
              </Button>
            </Tooltip>
          </>
        )}
        {exportEnabled && (
          <Tooltip title={isMobile ? "Exporter" : "Exporter le graphique"}>
            <Button
              size="small"
              icon={<DownloadOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                handleExport();
              }}
              loading={isLoading}
            >
              {!isMobile && "Exporter"}
            </Button>
          </Tooltip>
        )}
      </Space>
    );
  };

  // Calculate responsive chart dimensions
  const chartDimensions = useMemo(() => {
    if (expandMode === 'modal') {
      return {
        height: chartConfig.height,
        width: '100%'
      };
    }

    const baseHeight = isMobile ? 250 : isTablet ? 300 : chartConfig.height;
    const expandedHeight = isMobile ? 400 : isTablet ? 500 : Math.max(600, chartConfig.height * 1.5);

    return {
      height: isExpanded ? expandedHeight * zoomLevel : baseHeight,
      width: '100%'
    };
  }, [isExpanded, expandMode, chartConfig.height, isMobile, isTablet, zoomLevel]);

  // Enhanced chart rendering with responsive optimizations
  const renderChart = (height) => {
    if (!children) return null;

    return (
      <div
        className={`chart-container ${isExpanded ? 'expanded' : ''} ${isMobile ? 'mobile' : ''} ${isTablet ? 'tablet' : ''}`}
        style={{
          height: height,
          width: '100%',
          position: 'relative',
          overflow: isExpanded ? 'auto' : 'hidden',
          transform: isExpanded ? `scale(${zoomLevel})` : 'scale(1)',
          transformOrigin: 'top left',
          transition: 'all 0.3s ease',
        }}
      >
        {React.cloneElement(children, {
          ...children.props,
          height: height,
          data: data,
          enhanced: isExpanded,
          expanded: isExpanded,
          isModal: false,
          isMobile: isMobile,
          isTablet: isTablet,
          isDesktop: isDesktop,
          zoomLevel: zoomLevel,
          chartConfig: {
            ...chartConfig,
            // Responsive chart configuration
            showAllLabels: !isMobile || isExpanded,
            labelInterval: isMobile && !isExpanded ? 1 : 0,
            maxBarSize: isMobile ? 30 : 40,
            strokeWidth: isMobile ? 2 : 3,
            dotSize: isMobile ? 4 : 6,
            fontSize: isMobile ? 10 : 12,
            // Touch-friendly settings for mobile
            touchEnabled: isMobile,
            gestureHandling: isMobile ? 'cooperative' : 'auto'
          },
          ...otherProps
        })}
      </div>
    );
  };

  // Enhanced responsive card styling
  const cardStyle = useMemo(() => ({
    cursor: clickToExpandEnabled ? 'pointer' : 'default',
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    borderRadius: isMobile ? '8px' : '12px',
    ...(isExpanded && {
      position: 'relative',
      zIndex: 10,
      boxShadow: isMobile
        ? '0 4px 16px rgba(0,0,0,0.12)'
        : '0 8px 24px rgba(0,0,0,0.15)',
      transform: isMobile ? 'none' : 'translateY(-2px)',
    }),
    ...(isMobile && {
      margin: '8px 0',
    }),
    ...style
  }), [clickToExpandEnabled, isExpanded, isMobile, style]);

  return (
    <>
      {/* Enhanced Responsive Chart Card */}
      <Card
        {...cardProps}
        title={title}
        className={`unified-chart-expansion ${isExpanded ? 'expanded' : ''} ${isMobile ? 'mobile' : ''} ${isTablet ? 'tablet' : ''} ${className}`}
        extra={
          <Space size={isMobile ? "small" : "middle"}>
            {renderChartControls()}
            <Tooltip
              title={
                !clickToExpandEnabled
                  ? 'Expansion désactivée dans les paramètres'
                  : expandMode === 'modal'
                    ? (isMobile ? 'Plein écran' : 'Ouvrir en plein écran')
                    : (isExpanded ? 'Réduire' : 'Agrandir')
              }
            >
              <Button
                icon={
                  expandMode === 'modal'
                    ? <FullscreenOutlined />
                    : (isExpanded ? <CompressOutlined /> : <ExpandOutlined />)
                }
                onClick={(e) => {
                  handleExpansion(e);
                }}
                type={isExpanded ? 'primary' : 'default'}
                loading={isLoading}
                disabled={!clickToExpandEnabled}
                className="expand-button"
                size={isMobile ? "small" : "middle"}
                style={{
                  minWidth: isMobile ? '32px' : '40px',
                  minHeight: isMobile ? '32px' : '40px'
                }}
              />
            </Tooltip>
          </Space>
        }
        hoverable={clickToExpandEnabled && !isMobile}
        style={cardStyle}
        onClick={isMobile ? undefined : handleCardClick}
        styles={{
          body: {
            padding: isMobile ? '12px' : '16px',
            ...cardProps.styles?.body
          }
        }}
      >
        {renderChart(chartDimensions.height)}
      </Card>

      {/* Enhanced Responsive Modal */}
      <UnifiedChartModal
        visible={isModalVisible}
        onClose={handleModalClose}
        title={title}
        data={data}
        chartType={chartType}
        chartConfig={chartConfig}
        exportEnabled={exportEnabled}
      >
        {children}
      </UnifiedChartModal>
    </>
  );
};

export default UnifiedChartExpansion;
