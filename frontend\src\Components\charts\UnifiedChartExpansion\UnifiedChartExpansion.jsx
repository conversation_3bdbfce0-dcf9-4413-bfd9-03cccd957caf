import React, { useState, useCallback, useMemo } from 'react';
import { Card, Button, Tooltip, Space } from 'antd';
import { FullscreenOutlined, CompressOutlined, ExpandOutlined } from '@ant-design/icons';
import { useUnifiedChartConfig } from '../../../hooks/useUnifiedChartConfig';
import UnifiedChartModal from './UnifiedChartModal';

/**
 * Unified Chart Expansion System
 * Consolidates all chart expansion functionality across the application
 * Integrates with unified chart configuration for consistent behavior
 */
const UnifiedChartExpansion = ({
  children,
  title,
  data,
  chartType = 'bar',
  expandMode = 'modal', // 'modal' or 'inline'
  onExpand,
  onCollapse,
  exportEnabled = false,
  className = '',
  style = {},
  cardProps = {},
  ...otherProps
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Get unified chart configuration with fallback
  const chartConfig = useUnifiedChartConfig({
    chartType,
    customOptions: {}
  });

  // Check if click to expand is enabled with fallback
  const clickToExpandEnabled = chartConfig?.interactionConfig?.clickToExpand !== false;

  // Debug effect to track modal state changes
  React.useEffect(() => {
    console.log('🔧 UnifiedChartExpansion - Modal state changed:', {
      isModalVisible,
      title,
      clickToExpandEnabled,
      expandMode,
      dataLength: data?.length || 0
    });
  }, [isModalVisible, title, clickToExpandEnabled, expandMode, data]);

  // Handle expansion toggle
  const handleExpansion = useCallback(async (e) => {
    console.log('🔧 UnifiedChartExpansion - handleExpansion called:', {
      clickToExpandEnabled,
      expandMode,
      isModalVisible,
      title,
      dataLength: data?.length || 0
    });

    if (!clickToExpandEnabled) {
      console.log('⚠️ UnifiedChartExpansion - Click to expand is disabled');
      return;
    }

    // Prevent event propagation if event is provided
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    setIsLoading(true);

    try {
      // Small delay for large datasets
      if (data && data.length > 100) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      if (expandMode === 'modal') {
        console.log('🔧 UnifiedChartExpansion - Opening modal');
        setIsModalVisible(true);
        if (onExpand) onExpand();
      } else {
        const newExpandedState = !isExpanded;
        console.log('🔧 UnifiedChartExpansion - Toggling inline expansion:', newExpandedState);
        setIsExpanded(newExpandedState);

        if (newExpandedState && onExpand) {
          onExpand();
        } else if (!newExpandedState && onCollapse) {
          onCollapse();
        }
      }
    } catch (error) {
      console.error('❌ UnifiedChartExpansion - Error during expansion:', error);
    } finally {
      setIsLoading(false);
    }
  }, [expandMode, isExpanded, onExpand, onCollapse, data, clickToExpandEnabled, title]);

  // Handle modal close
  const handleModalClose = useCallback(() => {
    console.log('🔧 UnifiedChartExpansion - Modal closing');
    setIsModalVisible(false);
    if (onCollapse) {
      onCollapse();
    }
  }, [onCollapse]);

  // Handle card click
  const handleCardClick = useCallback((e) => {
    console.log('🔧 UnifiedChartExpansion - Card clicked:', {
      target: e.target.className,
      closest: {
        cardExtra: !!e.target.closest('.ant-card-extra'),
        chartContainer: !!e.target.closest('.chart-container'),
        expandButton: !!e.target.closest('.expand-button')
      }
    });

    // Only trigger expansion if clicking on the card itself, not controls
    if (e.target.closest('.ant-card-extra') || e.target.closest('.chart-container') || e.target.closest('.expand-button')) {
      console.log('🔧 UnifiedChartExpansion - Card click ignored (clicked on control)');
      return;
    }

    // Respect the "Click to Expand" setting
    if (clickToExpandEnabled) {
      console.log('🔧 UnifiedChartExpansion - Card click triggering expansion');
      handleExpansion(e);
    } else {
      console.log('🔧 UnifiedChartExpansion - Card click ignored (expansion disabled)');
    }
  }, [clickToExpandEnabled, handleExpansion]);

  // Render chart controls for expanded inline mode
  const renderChartControls = () => {
    if (!isExpanded || expandMode === 'modal') return null;

    return (
      <Space size="small">
        {exportEnabled && (
          <Tooltip title="Exporter">
            <Button
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                // Export functionality would go here
                console.log('Export chart:', title);
              }}
            >
              Export
            </Button>
          </Tooltip>
        )}
      </Space>
    );
  };

  // Calculate chart dimensions based on expansion state
  const chartDimensions = useMemo(() => {
    if (expandMode === 'modal') {
      return {
        height: chartConfig.height,
        width: '100%'
      };
    }

    return {
      height: isExpanded ? Math.max(600, chartConfig.height * 1.5) : chartConfig.height,
      width: '100%'
    };
  }, [isExpanded, expandMode, chartConfig.height]);

  // Render the chart with appropriate props
  const renderChart = (height) => {
    if (!children) return null;

    return (
      <div
        className={`chart-container ${isExpanded ? 'expanded' : ''}`}
        style={{
          height: height,
          width: '100%',
          position: 'relative',
          overflow: 'visible',
        }}
      >
        {React.cloneElement(children, {
          ...children.props,
          height: height,
          data: data,
          enhanced: isExpanded,
          expanded: isExpanded,
          isModal: false,
          chartConfig: chartConfig,
          ...otherProps
        })}
      </div>
    );
  };

  // Card style with unified configuration
  const cardStyle = useMemo(() => ({
    cursor: clickToExpandEnabled ? 'pointer' : 'default',
    transition: 'all 0.3s ease',
    ...(isExpanded && {
      position: 'relative',
      zIndex: 10,
      boxShadow: '0 8px 24px rgba(0,0,0,0.15)',
    }),
    ...style
  }), [clickToExpandEnabled, isExpanded, style]);

  return (
    <>
      {/* Main Chart Card */}
      <Card
        {...cardProps}
        title={title}
        className={`unified-chart-expansion ${isExpanded ? 'expanded' : ''} ${className}`}
        extra={
          <Space>
            {renderChartControls()}
            <Tooltip 
              title={
                !clickToExpandEnabled 
                  ? 'Expansion désactivée dans les paramètres'
                  : expandMode === 'modal' 
                    ? 'Ouvrir en plein écran' 
                    : (isExpanded ? 'Réduire' : 'Agrandir')
              }
            >
              <Button
                icon={
                  expandMode === 'modal'
                    ? <FullscreenOutlined />
                    : (isExpanded ? <CompressOutlined /> : <ExpandOutlined />)
                }
                onClick={(e) => {
                  console.log('🔧 UnifiedChartExpansion - Button clicked');
                  handleExpansion(e);
                }}
                type={isExpanded ? 'primary' : 'default'}
                loading={isLoading}
                disabled={!clickToExpandEnabled}
                className="expand-button"
              />
            </Tooltip>
          </Space>
        }
        hoverable={clickToExpandEnabled}
        style={cardStyle}
        onClick={handleCardClick}
      >
        {renderChart(chartDimensions.height)}
      </Card>

      {/* Unified Modal */}
      <UnifiedChartModal
        visible={isModalVisible}
        onClose={handleModalClose}
        title={title}
        data={data}
        chartType={chartType}
        chartConfig={chartConfig}
      >
        {children}
      </UnifiedChartModal>
    </>
  );
};

export default UnifiedChartExpansion;
