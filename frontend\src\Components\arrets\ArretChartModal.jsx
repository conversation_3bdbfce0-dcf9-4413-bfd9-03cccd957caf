import React from 'react';
import { <PERSON><PERSON>, But<PERSON> } from 'antd';
import { FullscreenExitOutlined } from '@ant-design/icons';
import { useArretQueuedContext } from '../../context/arret/ArretQueuedContext.jsx';
import ArretBarChart from './charts/ArretBarChart';
import ArretPieChart from './charts/ArretPieChart';
import ArretTrendChart from './charts/ArretTrendChart';
import ArretHeatmapChart from './charts/ArretHeatmapChart';
import ArretHorizontalBarChart from './charts/ArretHorizontalBarChart';
import ArretAreaChart from './charts/ArretAreaChart';

const ArretChartModal = () => {
  const context = useArretQueuedContext();
  
  if (!context) {
    return null;
  }  const { 
    chartData = [],
    topStopsData = [],
    durationTrend = [],
    machineComparison = [],
    stopReasons = [],
    loading = false,
    isChartModalVisible,
    setIsChartModalVisible,
    chartModalContent
  } = context;
    const chartComponents = {
    bar: <ArretBarChart data={machineComparison} loading={loading} />,
    pie: <ArretPieChart data={topStopsData} loading={loading} />,
    trend: <ArretTrendChart data={chartData} loading={loading} />,
    heatmap: <ArretHeatmapChart data={durationTrend} loading={loading} />,
    horizontalBar: <ArretHorizontalBarChart data={stopReasons} loading={loading} />,
    area: <ArretAreaChart data={durationTrend} loading={loading} />
  };  const chartTitles = {
    bar: 'Comparaison Machines',
    pie: 'Top 5 Causes', 
    trend: 'Evolution Arrêts',
    heatmap: 'Durée par Heure',
    horizontalBar: 'Causes d\'Arrêt',
    area: 'Tendance Durée'
  };
  const handleClose = () => {
    setIsChartModalVisible(false);
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <span>{chartTitles[chartModalContent] || 'Graphique'}</span>
          <Button 
            icon={<FullscreenExitOutlined />}
            onClick={handleClose}
            size="small"
            type="text"
          >
            Fermer
          </Button>
        </div>
      }
      open={isChartModalVisible}
      onCancel={handleClose}
      footer={null}
      width="90vw"
      style={{ 
        top: 20,
        paddingBottom: 0
      }}
      bodyStyle={{ 
        height: '80vh',
        padding: '24px',
        overflow: 'hidden'
      }}
      destroyOnClose
    >
      <div style={{ height: '100%', width: '100%' }}>
        {chartComponents[chartModalContent]}
      </div>
    </Modal>
  );
};

export default ArretChartModal;
