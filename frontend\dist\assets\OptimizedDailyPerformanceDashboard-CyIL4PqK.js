import{A as Be,r as p,$ as Fe,a0 as Ee,R as e,a1 as Z,U as ue,W as Y,a2 as Q,N as _,a3 as h,a4 as tt,a5 as ae,a6 as fe,a7 as Me,a8 as K,a9 as v,aa as Ce,ab as O,V as We,T as me,v as nt,ac as be,ad as Te,ae as he,G as Ne,af as at,ag as rt,ah as z}from"./index-gs31pxOi.js";import{w as C,l as ot,E as st,r as it}from"./chart-config-BlCOOSyR.js";import{R as ge}from"./CloseCircleOutlined-CwMt_cM7.js";import{R as pe}from"./CheckCircleOutlined-DSx8vC-g.js";import{R as ye}from"./SyncOutlined-BrIqP8w4.js";import{R as lt}from"./WarningOutlined-X6B97o_R.js";import{L as ct,B as Se}from"./index-B6tjFLDG.js";import{R as Ie}from"./LineChartOutlined-Bf4j28ts.js";import{R as Re}from"./ClockCircleOutlined-CiulfqLg.js";import{R as ie}from"./DashboardOutlined-mvH9ifye.js";import{S as te}from"./index-C-rwPe8o.js";import{R as Ae}from"./HistoryOutlined-C_ww08XJ.js";import{R as dt}from"./FilterOutlined-CWlXg5FA.js";import{R as ut}from"./FileTextOutlined-C7OWCm18.js";import{P as mt}from"./progress-B0lrj7SI.js";const ve=n=>{const g=parseFloat(n);return isNaN(g)?0:g},gt=()=>{const{isAuthenticated:n}=Be(),g=p.useCallback((E,k)=>{const B=(()=>{if(typeof window<"u"){const A=window.location.origin;return A.includes("ngrok-free.app")||A.includes("ngrok.io")?A:"http://localhost:5000"}return"http://localhost:5000"})();return Fe[E](`${B}${k}`).retry(2).withCredentials().timeout(3e4)},[]),[a,T]=p.useState([]),[x,D]=p.useState([]),[f,i]=p.useState({}),[c,s]=p.useState([]),[N,U]=p.useState(null),[S,X]=p.useState([]),[j,q]=p.useState(!0),[o,u]=p.useState(null),[w,R]=p.useState(new Date),[r,l]=p.useState(!1),[L,M]=p.useState(null),[t,d]=p.useState(!1),[y,I]=p.useState({connected:!1,connecting:!1,updating:!1,reconnecting:!1}),[$e,re]=p.useState({}),[ze,Oe]=p.useState([]),[Qe,Ue]=p.useState(null),[je,qe]=p.useState(new Date),[Ve,Ge]=p.useState("all"),[Ye,Ke]=p.useState("machines"),oe=p.useCallback(E=>E.map(k=>{const B=ve(k.TRS||"0"),A=B>80?"success":B>60?"warning":"error",J=ve(k.Quantite_Bon||"0"),H=ve(k.Quantite_Planifier||"0"),m=J/(H||1)*100;return{...k,status:A,progress:m}}),[]),le=p.useCallback(async(E,k)=>{try{if(!n){console.log("🔐 Skipping machine sessions update - user not authenticated");return}const A=(await g("get","/api/activeSessions")).body,J={};A.forEach(m=>{J[m.machine_id]=m});const H={};k.forEach(m=>{m.id&&(H[m.id]=m)});for(const m of E){if(!m.id)continue;const ce={...m,Regleur_Prenom:m.Regleur_Prenom||"0",Quantite_Planifier:m.Quantite_Planifier||"0",Quantite_Bon:m.Quantite_Bon||"0",Quantite_Rejet:m.Quantite_Rejet||"0",TRS:m.TRS||"0",Poid_unitaire:m.Poid_unitaire||"0",cycle_theorique:m.cycle_theorique||"0",empreint:m.empreint||"0",Etat:m.Etat||"off",Code_arret:m.Code_arret||""},se=!!J[m.id];m.Etat==="on"&&!se?(await g("post","/api/createSession").send({machineId:m.id,machineData:ce}),re(ee=>({...ee,[m.id]:{active:!0,startTime:new Date,lastUpdate:new Date}})),console.log(`New session started for ${m.Machine_Name}`)):m.Etat==="on"&&se?(await g("post","/api/updateSession").send({machineId:m.id,machineData:ce}),re(ee=>({...ee,[m.id]:{...ee[m.id],lastUpdate:new Date}}))):m.Etat==="off"&&se&&(await g("post","/api/stopSession").send({machineId:m.id}),re(ee=>{const de={...ee};return delete de[m.id],de}),console.log(`Session ended for ${m.Machine_Name}`))}}catch(B){console.error("Error handling machine sessions:",B)}},[n,g]),Ze=p.useCallback(async()=>{try{if(!n){console.log("🔐 Skipping data fetch - user not authenticated"),q(!1),u("Authentication required");return}q(!0);const E=await Promise.all([g("get","/api/RealTimeTable"),g("get","/api/MachineCard"),g("get","/api/sidecards"),g("get","/api/dailyStats")]),[k,B,A,J]=E;D([...a]);const H=oe(B.body);T(H),i(A.body[0]||{}),s(J.body),u(null),q(!1),R(new Date),await le(H,a)}catch(E){console.error("Error fetching data:",E),u(E.message||"Failed to fetch data"),q(!1),R(new Date)}},[n,g,a,oe,le]),Xe=p.useCallback(async E=>{if(E)try{if(!n){console.log("🔐 Skipping machine history fetch - user not authenticated"),M("Authentication required");return}l(!0),M(null);const k=await g("get",`/api/machineSessions/${E}`);if(Array.isArray(k.body)&&k.body.length>0){const B=k.body.map(A=>({...A,timestamp:new Date(A.session_start),isActive:!A.session_end,highlight:!A.session_end}));X(B)}else console.log("No sessions found for this machine"),X([]),M("No sessions found for this machine");l(!1)}catch(k){console.error("Error fetching machine history:",k),M("No history data available for this machine"),l(!1),X([])}},[n,g]),Je=p.useCallback(()=>{y.connected?(C.requestUpdate(),console.log("Requesting data update from server")):y.connecting||y.reconnecting?console.log("Connection already in progress"):(I(E=>({...E,connecting:!0})),Ee.info({message:"Reconnecting",description:"Attempting to establish WebSocket connection",icon:e.createElement(Z,{spin:!0,style:{color:"#1890ff"}}),placement:"bottomRight",duration:2,key:"websocket-reconnecting"}),C.connect())},[y.connected,y.connecting,y.reconnecting]),_e=p.useCallback(async()=>{try{if(!n){console.log("🔐 Skipping operator stats fetch - user not authenticated");return}const E=await g("get","/api/operator-stats");Oe(E.body)}catch(E){console.error("Error fetching operator stats:",E)}},[n,g]),we=p.useCallback(async()=>{try{if(!n){console.log("🔐 Skipping production stats fetch - user not authenticated");return}const E=await g("get","/api/production-stats");Ue(E.body)}catch(E){console.error("Error fetching production stats:",E)}},[n,g]);return p.useEffect(()=>{C.isConnected?(I(b=>({...b,connected:!0,connecting:!1})),C.requestUpdate()):(I(b=>({...b,connecting:!0})),C.connect());const E=b=>{console.log("Received initial data from WebSocket"),I(P=>({...P,connecting:!1,updating:!1}));const F=oe(b.machineData),V={};b.activeSessions.forEach(P=>{V[P.machine_id]={active:!0,startTime:new Date(P.session_start),lastUpdate:new Date(P.last_updated),sessionId:P.id}}),re(V),T(F),D([...F]),i(b.sideCardData||{}),s(b.dailyStats||[]),u(null),q(!1),R(new Date),console.log("Initial data loaded successfully")},k=b=>{console.log("Received update from WebSocket",b),I(ne=>({...ne,updating:!0})),setTimeout(()=>{I(ne=>({...ne,updating:!1}))},500),D([...a]);const F=b.data.changedMachines||[],V=b.data.fullData||[],P=oe(V);T(P),R(new Date),le(P,a),console.log(`${F.length} machine(s) updated`)},B=b=>{console.log("Received session update from WebSocket",b);const{sessionData:F,updateType:V}=b,P=F.machine_id;V==="created"||V==="updated"?(re(ne=>({...ne,[P]:{active:!0,startTime:new Date(F.session_start),lastUpdate:new Date(F.last_updated),sessionId:F.id}})),console.log(`Session ${V} for ${F.Machine_Name||"machine "+P}`)):V==="stopped"&&(re(ne=>{const ke={...ne};return delete ke[P],ke}),console.log(`Session stopped for ${F.Machine_Name||"machine "+P}`))},A=()=>{console.log("WebSocket connected"),I(b=>({...b,connected:!0,connecting:!1})),C.requestUpdate(),console.log("WebSocket connection established successfully")},J=()=>{console.log("WebSocket disconnected"),I(b=>({...b,connected:!1,connecting:!1})),document.visibilityState==="visible"&&Ee.warning({message:"Connexion perdue",description:"La connexion WebSocket a été interrompue",icon:e.createElement(Z,{style:{color:"#faad14"}}),placement:"bottomRight",duration:4,key:"websocket-disconnected"}),console.log("WebSocket disconnected - No automatic reconnection")},H=b=>{console.error("WebSocket error:",b),I(F=>({...F,connected:!1,connecting:!1})),Ee.error({message:"Erreur de connexion",description:"Impossible de se connecter au service de données en temps réel. Utilisation du mode de secours.",icon:e.createElement(ge,{style:{color:"#ff4d4f"}}),placement:"bottomRight",duration:4,key:"websocket-error"}),u("Erreur de connexion WebSocket"),console.log("WebSocket error - NOT falling back to HTTP polling (disabled for testing)")},m=C.addEventListener("initialData",E),ce=C.addEventListener("update",k),se=C.addEventListener("sessionUpdate",B),ee=C.addEventListener("connect",A),de=C.addEventListener("disconnect",J),He=C.addEventListener("error",H);console.log("Establishing WebSocket connection...");const et=setTimeout(()=>{C.isConnected||(console.log("WebSocket connection timeout - NOT falling back to HTTP polling (disabled for testing)"),I(b=>({...b,connecting:!1})),console.log("WebSocket connection timeout - still attempting to connect"))},1e4);return()=>{clearTimeout(et),m(),ce(),se(),ee(),de(),He(),console.log("useDashboardData unmounting - keeping WebSocket connection alive"),window.addEventListener("beforeunload",()=>{console.log("Page unloading, disconnecting WebSocket"),C.disconnect()},{once:!0})}},[oe,le,Ze,a]),p.useEffect(()=>{_e(),we()},[_e,we]),{machineData:a,previousMachineData:x,sideCardData:f,dailyStats:c,selectedMachine:N,machineHistory:S,operatorStats:ze,productionStats:Qe,sessionStatus:$e,loading:j,error:o,lastUpdate:w,historyLoading:r,historyError:L,wsStatus:y,isHistoricalView:t,selectedDate:je,selectedShift:Ve,selectedView:Ye,setSelectedMachine:U,fetchMachineHistory:Xe,handleRefresh:Je,setSelectedDate:qe,setSelectedShift:Ge,setSelectedView:Ke,formatMachineData:oe}},pt=({wsStatus:n,onRefresh:g})=>{const a=()=>n.connected?e.createElement(Y,{title:"Data is being updated in real-time"},e.createElement(Q,{className:"ws-status-tag",color:"success"},e.createElement(pe,null)," Real-time connected")):n.connecting?e.createElement(Y,{title:"Attempting to establish real-time connection"},e.createElement(Q,{className:"ws-status-tag",color:"processing"},e.createElement(ye,{spin:!0})," Connecting...")):n.reconnecting?e.createElement(Y,{title:"Connection lost, attempting to reconnect"},e.createElement(Q,{className:"ws-status-tag",color:"warning"},e.createElement(ye,{spin:!0})," Reconnecting...")):e.createElement(Y,{title:"WebSocket connection lost - click Refresh to reconnect"},e.createElement(Q,{className:"ws-status-tag",color:"error"},e.createElement(lt,null)," Disconnected")),T=()=>n.updating?e.createElement(Y,{title:"Receiving new data from server"},e.createElement(Q,{className:"ws-status-tag ws-status-updating",color:"processing"},e.createElement(ye,{spin:!0})," Updating")):null,x=()=>{const D=!n.connected&&n.connecting,f=n.updating;let i="Refresh data from server",c="Refresh Data";return D?i="Connection in progress...":f?i="Data is currently being updated":!n.connected&&!n.reconnecting?(i="Click to attempt WebSocket reconnection",c="Reconnect"):n.reconnecting&&(i="Reconnection in progress...",c="Reconnecting..."),e.createElement(Y,{title:i},e.createElement(_,{type:!n.connected&&!n.connecting&&!n.reconnecting?"danger":"primary",icon:e.createElement(Z,{spin:n.updating||n.reconnecting}),onClick:g,size:"small",loading:D,disabled:f||n.reconnecting},c))};return e.createElement(ue,null,a(),T(),x())};ot.throttle((n,g)=>{n&&n.data&&(n.data=g,n.update("none"))},500);const G=window.innerWidth<768,ft=n=>{const g=n?h.DARK.BORDER:h.ACCENT_BORDER,a=n?h.DARK.TEXT_SECONDARY:h.LIGHT_GRAY,T=n?h.DARK.TEXT:h.DARK_GRAY,x=n?h.DARK.BACKGROUND:h.WHITE,D=n?h.DARK.BORDER:h.ACCENT_BORDER,f=[h.PRIMARY_BLUE,h.SECONDARY_BLUE,h.CHART_TERTIARY,h.CHART_QUATERNARY,"#60A5FA","#1D4ED8","#3730A3"],i=n?"0 4px 12px rgba(0, 0, 0, 0.5)":"0 4px 12px rgba(0, 0, 0, 0.1)";return{responsive:!0,maintainAspectRatio:!1,animation:!1,devicePixelRatio:1,plugins:{legend:{position:"top",align:"center",labels:{color:a,padding:15,usePointStyle:!0,pointStyle:"circle",boxWidth:10,font:{weight:500}},display:!G},tooltip:{enabled:!G||window.innerWidth>480,backgroundColor:x,titleColor:n?"rgba(255, 255, 255, 0.95)":"rgba(0, 0, 0, 0.95)",bodyColor:a,borderColor:D,borderWidth:1,padding:10,cornerRadius:6,boxPadding:5,displayColors:!0,boxShadow:i,callbacks:{label:function(c){let s=c.dataset.label||"";return s&&(s+=": "),s+=Math.round(c.parsed.y*100)/100,s},title:function(c){return c[0].label}}},title:{display:!1,color:T,font:{weight:600,size:16}}},scales:{x:{grid:{display:!1,color:g,z:-1},border:{color:n?"rgba(255, 255, 255, 0.2)":"rgba(0, 0, 0, 0.2)"},ticks:{color:a,maxRotation:0,autoSkipPadding:10,maxTicksLimit:G?5:10,padding:8,font:{size:G?10:12}},title:{display:!1,color:T,font:{weight:500}}},y:{beginAtZero:!0,grid:{color:g,z:-1,lineWidth:1,drawBorder:!0},border:{color:n?"rgba(255, 255, 255, 0.2)":"rgba(0, 0, 0, 0.2)"},ticks:{color:a,precision:0,maxTicksLimit:G?5:8,padding:8,font:{size:G?10:12}},title:{display:!1,color:T,font:{weight:500}}}},elements:{point:{radius:G?0:3,hoverRadius:G?3:6,backgroundColor:function(c){const s=c.datasetIndex%f.length;return f[s]},borderColor:n?"#141414":"white",borderWidth:2,hoverBorderWidth:2,hoverBorderColor:n?"rgba(255, 255, 255, 0.5)":"rgba(0, 0, 0, 0.5)"},line:{borderWidth:G?2:3,tension:.2,fill:!1,borderColor:function(c){const s=c.datasetIndex%f.length;return f[s]},borderCapStyle:"round"},bar:{backgroundColor:function(c){const s=c.datasetIndex%f.length;return f[s]},borderWidth:0,borderRadius:4,hoverBackgroundColor:function(c){return c.datasetIndex%f.length,n?h.DARK.SECONDARY_BLUE:h.SECONDARY_BLUE}}}}},{Title:Et,Text:W}=We,{TabPane:xe}=fe,bt=({visible:n,machine:g,machineHistory:a,loading:T,error:x,onClose:D,onRefresh:f,darkMode:i})=>{if(!g)return null;const c=[{title:"Start Time",dataIndex:"session_start",key:"session_start",render:o=>new Date(o).toLocaleString(),sorter:(o,u)=>new Date(u.session_start)-new Date(o.session_start)},{title:"End Time",dataIndex:"session_end",key:"session_end",render:o=>o?new Date(o).toLocaleString():e.createElement(Q,{color:"processing"},"Active")},{title:"Duration",key:"duration",render:(o,u)=>{const w=new Date(u.session_start),r=(u.session_end?new Date(u.session_end):new Date)-w,l=Math.floor(r/36e5),L=Math.floor(r%36e5/6e4);return`${l}h ${L}m`}},{title:"TRS",dataIndex:"TRS",key:"TRS",render:o=>{const u=parseFloat(o||0);let w="red";return u>80?w="green":u>60&&(w="orange"),e.createElement("span",{style:{color:w}},u.toFixed(1),"%")},sorter:(o,u)=>parseFloat(o.TRS||0)-parseFloat(u.TRS||0)},{title:"Production",dataIndex:"Quantite_Bon",key:"Quantite_Bon",sorter:(o,u)=>parseFloat(o.Quantite_Bon||0)-parseFloat(u.Quantite_Bon||0)},{title:"Rejects",dataIndex:"Quantite_Rejet",key:"Quantite_Rejet",sorter:(o,u)=>parseFloat(o.Quantite_Rejet||0)-parseFloat(u.Quantite_Rejet||0)},{title:"Status",key:"status",render:(o,u)=>e.createElement(Q,{color:u.session_end?"default":"processing"},u.session_end?"Completed":"Active")}],s=ft(i),N=()=>{const o=new Date().getHours();return o>=6&&o<14?"morning":o>=14&&o<22?"afternoon":"night"},U=(o,u)=>{if(u==="all")return!0;const R=new Date(o.session_start).getHours();return u==="morning"&&R>=6&&R<14||u==="afternoon"&&R>=14&&R<22||u==="night"&&(R>=22||R<6)},S=o=>{if(o==null||o==="")return 0;const u=String(o).replace(/,/g,"."),w=Number.parseFloat(u);return isNaN(w)?0:w},X=(o,u=1)=>o==null||o===""?"0":S(o).toFixed(u).replace(/\./g,","),j=o=>o==null||o===""?"0":S(o).toLocaleString("en-US").replace(/,/g,"."),q=()=>{if(T)return e.createElement("div",{style:{textAlign:"center",padding:"40px 0"}},e.createElement("div",{className:"ant-spin ant-spin-lg ant-spin-spinning"},e.createElement("span",{className:"ant-spin-dot ant-spin-dot-spin"},e.createElement("i",{className:"ant-spin-dot-item"}),e.createElement("i",{className:"ant-spin-dot-item"}),e.createElement("i",{className:"ant-spin-dot-item"}),e.createElement("i",{className:"ant-spin-dot-item"}))),e.createElement("div",{style:{marginTop:16}},"Chargement de l'historique..."));if(x)return e.createElement(ae,{description:e.createElement(e.Fragment,null,e.createElement("p",null,"Erreur de chargement: ",x),e.createElement(_,{type:"primary",icon:e.createElement(Z,null),onClick:f},"Réessayer"))});if(!a||a.length===0)return e.createElement(ae,{description:e.createElement(e.Fragment,null,e.createElement("p",null,"Aucune session trouvée pour cette machine"),e.createElement("p",null,"La table machine_sessions est vide ou aucune donnée n'est disponible"),e.createElement(_,{type:"primary",icon:e.createElement(Z,null),onClick:f},"Rafraîchir")),image:ae.PRESENTED_IMAGE_SIMPLE});const o={labels:a.map(r=>{const l=new Date(r.session_start);return l.toLocaleDateString()+" "+l.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),datasets:[{label:"TRS (%)",data:a.map(r=>parseFloat(r.TRS)||0),backgroundColor:"rgba(153, 102, 255, 0.2)",borderColor:"rgba(153, 102, 255, 1)",borderWidth:2,fill:!0}]},u={labels:a.map(r=>{const l=new Date(r.session_start);return l.toLocaleDateString()+" "+l.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),datasets:[{label:"Good Production",data:a.map(r=>parseFloat(r.Quantite_Bon)||0),backgroundColor:"rgba(75, 192, 192, 0.6)",borderColor:"rgba(75, 192, 192, 1)",borderWidth:1}]},w={labels:a.map(r=>{const l=new Date(r.session_start);return l.toLocaleDateString()+" "+l.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),datasets:[{label:"Rejected Production",data:a.map(r=>parseFloat(r.Quantite_Rejet)||0),backgroundColor:"rgba(255, 99, 132, 0.6)",borderColor:"rgba(255, 99, 132, 1)",borderWidth:1}]},R={labels:a.map(r=>{const l=new Date(r.session_start);return l.toLocaleDateString()+" "+l.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),datasets:[{label:"Session Duration (min)",data:a.map(r=>{const l=new Date(r.session_start),L=r.session_end?new Date(r.session_end):new Date;return Math.round((L-l)/6e4)}),backgroundColor:"rgba(255, 159, 64, 0.6)",borderColor:"rgba(255, 159, 64, 1)",borderWidth:1}]};return e.createElement(fe,{defaultActiveKey:"1",className:i?"dark-mode":""},e.createElement(xe,{tab:"Sessions",key:"1"},e.createElement(Me,{columns:c,dataSource:a.map((r,l)=>({...r,key:l})),pagination:{pageSize:5},scroll:{x:!0}})),e.createElement(xe,{tab:"Charts",key:"2"},e.createElement(K,{gutter:[16,16]},e.createElement(v,{xs:24,md:12},e.createElement("div",{className:"chart-container"},e.createElement("h3",{className:"chart-title"},e.createElement(Ie,null)," TRS (%)"),e.createElement("div",{style:{height:200}},e.createElement(ct,{data:o,options:{...s,scales:{...s.scales,y:{...s.scales.y,beginAtZero:!0,max:100,grid:{color:i?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:i?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...s.scales.x,grid:{display:!1,color:i?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:i?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...s.plugins,legend:{...s.plugins.legend,labels:{...s.plugins.legend.labels,color:i?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}}})))),e.createElement(v,{xs:24,md:12},e.createElement("div",{className:"chart-container"},e.createElement("h3",{className:"chart-title"},e.createElement(pe,null)," Production (pcs)"),e.createElement("div",{style:{height:200}},e.createElement(Se,{data:u,options:{...s,scales:{...s.scales,y:{...s.scales.y,beginAtZero:!0,grid:{color:i?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:i?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...s.scales.x,grid:{display:!1,color:i?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:i?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...s.plugins,legend:{...s.plugins.legend,labels:{...s.plugins.legend.labels,color:i?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}}})))),e.createElement(v,{xs:24,md:12},e.createElement("div",{className:"chart-container"},e.createElement("h3",{className:"chart-title"},e.createElement(ge,null)," Rejects (pcs)"),e.createElement("div",{style:{height:200}},e.createElement(Se,{data:w,options:{...s,scales:{...s.scales,y:{...s.scales.y,beginAtZero:!0,grid:{color:i?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:i?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...s.scales.x,grid:{display:!1,color:i?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:i?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...s.plugins,legend:{...s.plugins.legend,labels:{...s.plugins.legend.labels,color:i?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}}})))),e.createElement(v,{xs:24,md:12},e.createElement("div",{className:"chart-container"},e.createElement("h3",{className:"chart-title"},e.createElement(Re,null)," Session Duration (min)"),e.createElement("div",{style:{height:200}},e.createElement(Se,{data:R,options:{...s,scales:{...s.scales,y:{...s.scales.y,beginAtZero:!0,grid:{color:i?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:i?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...s.scales.x,grid:{display:!1,color:i?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:i?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...s.plugins,legend:{...s.plugins.legend,labels:{...s.plugins.legend.labels,color:i?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}}})))))),e.createElement(xe,{tab:e.createElement("span",null,e.createElement(Ce,{style:{marginRight:8}}),"Informations"),key:"3"},e.createElement("div",{style:{padding:"16px 0"}},e.createElement(K,{gutter:[24,24]},e.createElement(v,{xs:24,md:12},e.createElement(O,{title:e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(ie,{style:{color:"#1890ff",marginRight:8}}),e.createElement("span",null,"Détails de la machine")),bordered:!0,style:{height:"100%"}},e.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:16}},e.createElement("div",{style:{width:64,height:64,borderRadius:8,background:"rgba(24, 144, 255, 0.1)",display:"flex",alignItems:"center",justifyContent:"center",marginRight:16}},e.createElement(ie,{style:{fontSize:32,color:"#1890ff"}})),e.createElement("div",null,e.createElement(Et,{level:4,style:{margin:0}},g.Machine_Name),e.createElement(W,{type:"secondary"},a.length>0&&a[0].Ordre_Fabrication?`OF : ${a[0].Ordre_Fabrication}`:"Aucun ordre de fabrication"))),e.createElement(me,{style:{margin:"16px 0"}}),e.createElement(K,{gutter:[16,16]},e.createElement(v,{span:12},e.createElement(te,{title:e.createElement(W,{style:{fontSize:14}},"Sessions ",N()==="morning"?"matin":N()==="afternoon"?"après-midi":"nuit"),value:j(a.filter(r=>U(r,N())).length),prefix:e.createElement(Ae,null),valueStyle:{color:"#1890ff",fontSize:20}})),e.createElement(v,{span:12},e.createElement(te,{title:e.createElement(W,{style:{fontSize:14}},"Sessions actives ",N()==="morning"?"matin":N()==="afternoon"?"après-midi":"nuit"),value:j(a.filter(r=>!r.session_end&&U(r,N())).length),prefix:e.createElement(Re,null),valueStyle:{color:a.filter(r=>!r.session_end&&U(r,N())).length>0?"#52c41a":"#8c8c8c",fontSize:20}}))))),e.createElement(v,{xs:24,md:12},e.createElement(O,{title:e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(Ae,{style:{color:"#1890ff",marginRight:8}}),e.createElement("span",null,"Historique des sessions")),bordered:!0,style:{height:"100%"}},a.length>0?e.createElement(e.Fragment,null,e.createElement("div",{style:{marginBottom:16}},e.createElement(W,{strong:!0},"Dernière session :"),e.createElement("div",{style:{background:"rgba(0,0,0,0.02)",padding:"12px",borderRadius:"8px",marginTop:"8px"}},e.createElement("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:8}},e.createElement(W,null,"Début :"),e.createElement(W,{strong:!0},a[0].session_start?new Date(a[0].session_start).toLocaleString("fr-FR"):"")),e.createElement("div",{style:{display:"flex",justifyContent:"space-between"}},e.createElement(W,null,"Fin :"),e.createElement(W,{strong:!0},a[0].session_end?new Date(a[0].session_end).toLocaleString("fr-FR"):e.createElement(Q,{color:"processing"},"En cours"))))),e.createElement(me,{style:{margin:"16px 0"}}),e.createElement(K,{gutter:[16,16]},e.createElement(v,{span:8},e.createElement(te,{title:e.createElement(W,{style:{fontSize:14}},"TRS moyen"),value:(()=>{const r=a.map(l=>S(l.TRS||0)).filter(l=>!isNaN(l));return r.length?X(r.reduce((l,L)=>l+L,0)/r.length):"N/A"})(),suffix:"%",valueStyle:{fontSize:18}})),e.createElement(v,{span:8},e.createElement(te,{title:e.createElement(W,{style:{fontSize:14}},"Bons produits"),value:j(a.reduce((r,l)=>r+S(l.Quantite_Bon||0),0)),valueStyle:{color:"#52c41a",fontSize:18}})),e.createElement(v,{span:8},e.createElement(te,{title:e.createElement(W,{style:{fontSize:14}},"Produits rejetés"),value:j(a.reduce((r,l)=>r+S(l.Quantite_Rejet||0),0)),valueStyle:{color:"#ff4d4f",fontSize:18}})))):e.createElement(ae,{description:"Aucune donnée de session disponible",image:ae.PRESENTED_IMAGE_SIMPLE}))),e.createElement(v,{xs:24},e.createElement(O,{title:e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(Ie,{style:{color:"#1890ff",marginRight:8}}),e.createElement("span",null,"Métriques de performance")),bordered:!0},a.length>0?e.createElement(K,{gutter:[24,24]},e.createElement(v,{xs:24,md:8},e.createElement(O,{style:{background:"rgba(0,0,0,0.02)"}},e.createElement(te,{title:"Durée moyenne des sessions",value:(()=>{const r=a.map(t=>{const d=new Date(t.session_start);return(t.session_end?new Date(t.session_end):new Date)-d}),l=r.reduce((t,d)=>t+d,0)/r.length,L=Math.floor(l/36e5),M=Math.floor(l%36e5/6e4);return`${L}h ${M}m`})(),prefix:e.createElement(Re,null)}))),e.createElement(v,{xs:24,md:8},e.createElement(O,{style:{background:"rgba(0,0,0,0.02)"}},e.createElement(te,{title:"Taux de rejet moyen",value:(()=>{const r=a.reduce((L,M)=>L+S(M.Quantite_Bon||0),0),l=a.reduce((L,M)=>L+S(M.Quantite_Rejet||0),0);return r+l>0?X(l/(r+l)*100):"0,0"})(),suffix:"%",prefix:e.createElement(ge,null),valueStyle:{color:"#ff4d4f"}}))),e.createElement(v,{xs:24,md:8},e.createElement(O,{style:{background:"rgba(0,0,0,0.02)"}},e.createElement(te,{title:"Production totale",value:j(a.reduce((r,l)=>r+S(l.Quantite_Bon||0),0)),prefix:e.createElement(pe,null),valueStyle:{color:"#52c41a"}})))):e.createElement(ae,{description:"Aucune donnée de performance disponible",image:ae.PRESENTED_IMAGE_SIMPLE})))))))};return e.createElement(tt,{title:`Sessions de ${g.Machine_Name}`,open:n,width:800,onCancel:D,footer:[e.createElement(_,{key:"close",onClick:D},"Fermer"),e.createElement(_,{key:"allSessions",type:"primary",onClick:()=>window.open("/sessions-report","_blank")},"Voir toutes les sessions")],destroyOnClose:!0},q())},{Title:Le,Text:De}=We,{TabPane:Pe}=fe,$={primary:h.PRIMARY_BLUE,success:h.SUCCESS,warning:h.WARNING,error:h.ERROR,gray:h.LIGHT_GRAY},ht=()=>{const{darkMode:n}=nt(),[g,a]=p.useState(!1),[T,x]=p.useState("machines"),D=t=>{let d="Connecté",y=$.success,I=e.createElement(pe,null);return t.fallbackMode?(d="Mode de secours",y=$.warning,I=e.createElement(Ce,null)):t.reconnecting?(d="Reconnexion...",y=$.warning,I=e.createElement(Z,{spin:!0})):t.connecting?(d="Connexion...",y=$.primary,I=e.createElement(Z,{spin:!0})):t.connected||(d="Déconnecté",y=$.error,I=e.createElement(ge,null)),e.createElement("div",{style:{display:"flex",alignItems:"center",marginRight:"16px"}},e.createElement(he,{status:t.connected?"success":t.fallbackMode?"warning":t.reconnecting?"processing":"error",text:e.createElement(Y,{title:t.fallbackMode?"Utilisation des mises à jour périodiques au lieu de la connexion en temps réel":""},e.createElement("span",{style:{color:y,display:"flex",alignItems:"center"}},e.createElement("span",{style:{marginRight:"4px"}},I),d))}))},{machineData:f,selectedMachine:i,machineHistory:c,loading:s,error:N,lastUpdate:U,wsStatus:S,historyLoading:X,historyError:j,setSelectedMachine:q,handleRefresh:o,fetchMachineHistory:u}=gt(),w=()=>U.toLocaleTimeString(),R=t=>{if(!t.id){z.info("Cette machine n'est pas encore configurée");return}q(t),u(t.id),a(!0)},r=t=>{x(t.target.value)},l=(t,d)=>d&&d.id!==1?$.gray:d&&d.Etat==="off"?$.error:d&&d.Etat==="on"||t==="success"?$.success:t==="warning"?$.warning:$.error,L=()=>s&&!S.connected&&!f.length?e.createElement("div",{style:{textAlign:"center",padding:"40px 0"}},e.createElement(rt,{size:"large"}),e.createElement("div",{style:{marginTop:16}},"Chargement des données...")):N&&!f.length?e.createElement(be,{message:"Erreur de connexion",description:e.createElement(e.Fragment,null,N,S.fallbackMode&&e.createElement("div",{style:{marginTop:"10px"}},e.createElement("strong",null,"Mode de secours activé:")," Les données seront actualisées périodiquement au lieu des mises à jour en temps réel."),!S.fallbackMode&&e.createElement("div",{style:{marginTop:"10px"}},e.createElement("strong",null,"Dépannage:"),e.createElement("ul",null,e.createElement("li",null,"Vérifiez votre connexion réseau"),e.createElement("li",null,"Le serveur peut être temporairement indisponible"),e.createElement("li",null,"Essayez de rafraîchir la page")))),type:"error",showIcon:!0,action:e.createElement(_,{type:"primary",onClick:o},"Réessayer")}):!f||f.length===0?e.createElement(be,{message:"Aucune donnée",description:"Aucune donnée de machine disponible",type:"info",showIcon:!0}):e.createElement(e.Fragment,null,e.createElement(K,{gutter:[16,16]},f.map(t=>e.createElement(v,{xs:24,sm:24,md:12,key:t.id||t.Machine_Name},e.createElement("div",{className:"machine-card-container",style:{position:"relative"}},e.createElement(st,{machine:t,handleMachineClick:R,getStatusColor:l}),t.id!==1&&e.createElement("div",{className:"soon-overlay"},e.createElement("div",{className:"soon-text"},"En développement..."),e.createElement(_,{type:"primary",ghost:!0,size:"small",icon:e.createElement(Ne,null)},"Configuration requise")),!t.id&&e.createElement("div",{className:"soon-overlay"},e.createElement("div",{className:"soon-text"},"En développement..."),e.createElement(_,{type:"default",size:"small"},"Configuration requise"))))))),M=[{title:"Machine",dataIndex:"Machine_Name",key:"Machine_Name",sorter:(t,d)=>t.Machine_Name.localeCompare(d.Machine_Name)},{title:"Statut",dataIndex:"Etat",key:"Etat",render:t=>e.createElement(Q,{color:t==="on"?"success":"error"},t==="on"?"En ligne":"Hors ligne"),filters:[{text:"En ligne",value:"on"},{text:"Hors ligne",value:"off"}],onFilter:(t,d)=>d.Etat===t},{title:"TRS",dataIndex:"TRS",key:"TRS",sorter:(t,d)=>parseFloat(t.TRS||0)-parseFloat(d.TRS||0),render:t=>{const d=parseFloat(t||0);let y="red";return d>80?y="green":d>60&&(y="orange"),e.createElement("span",{style:{color:y}},d.toFixed(1),"%")}},{title:"Production",dataIndex:"Quantite_Bon",key:"Quantite_Bon",sorter:(t,d)=>parseFloat(t.Quantite_Bon||0)-parseFloat(d.Quantite_Bon||0)},{title:"Rejets",dataIndex:"Quantite_Rejet",key:"Quantite_Rejet",sorter:(t,d)=>parseFloat(t.Quantite_Rejet||0)-parseFloat(d.Quantite_Rejet||0)},{title:"Opérateur",dataIndex:"Regleur_Prenom",key:"Regleur_Prenom",render:t=>t||"Non assigné"},{title:"Actions",key:"actions",render:(t,d)=>e.createElement(_,{type:"primary",size:"small",disabled:d.Etat==="off",onClick:y=>{y.stopPropagation(),d.Etat!=="off"&&R(d)},title:d.Etat==="off"?"Machine hors ligne. Détails non disponibles.":"Voir les détails de la machine"},"Détails")}];return e.createElement("div",{style:{padding:"24px"}},e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:24}},e.createElement("div",null,e.createElement(Le,{level:2},e.createElement(ie,null)," Tableau de Bord"),e.createElement(De,{type:"secondary"},new Date().toLocaleDateString())),e.createElement(ue,null,D(S),e.createElement(_,{type:"primary",icon:e.createElement(Z,null),onClick:o},"Actualiser"))),N&&e.createElement(be,{type:"error",message:"Erreur de connexion",description:`Dernière erreur: ${N} | Dernière mise à jour: ${w()}`,showIcon:!0,closable:!0,style:{marginBottom:16}}),e.createElement(me,null),e.createElement("div",{style:{marginBottom:16,display:"flex",justifyContent:"space-between",alignItems:"center"}},e.createElement(Te.Group,{value:T,onChange:r,buttonStyle:"solid"},e.createElement(Te.Button,{value:"machines"},e.createElement(ie,null)," Machines")),e.createElement(ue,null,e.createElement(Y,{title:"Filtrer les données"},e.createElement(_,{icon:e.createElement(dt,null)},"Filtres")),e.createElement(Y,{title:"Exporter les données"},e.createElement(_,{icon:e.createElement(ut,null)},"Exporter")))),e.createElement(K,{gutter:[16,16],style:{marginBottom:"16px"}},e.createElement(v,{span:24},e.createElement(O,null,e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"}},e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(De,{strong:!0,style:{marginRight:"10px"}},"Dernière mise à jour: ",w()),e.createElement(pt,{wsStatus:S,onRefresh:o})))))),T==="machines"&&e.createElement(K,{gutter:[18,18]},e.createElement(v,{xs:24,lg:24},e.createElement(O,{title:e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(ie,{style:{fontSize:20,marginRight:8}}),e.createElement("span",null,"Statistiques des machines")),extra:e.createElement(he,{count:f.length,style:{backgroundColor:"#1890ff"}})},L()))),e.createElement(me,null),e.createElement(O,{title:e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(Ne,{style:{fontSize:20,marginRight:8}}),e.createElement("span",null,"Détails des machines")),extra:e.createElement(ue,null,e.createElement(_,{type:"primary",icon:e.createElement(Z,null),onClick:o,size:"small"},"Actualiser"),e.createElement(Q,{color:"processing"},f.filter(t=>t.Etat==="on").length," sessions actives"))},e.createElement(fe,{defaultActiveKey:"1",className:n?"dark-mode":""},e.createElement(Pe,{tab:"Tableau",key:"1"},e.createElement(Me,{columns:M,dataSource:f.map((t,d)=>({...t,key:d})),pagination:{pageSize:10},scroll:{x:!0},onRow:t=>({onClick:()=>t.Etat!=="off"&&R(t),style:{cursor:t.Etat==="off"?"not-allowed":"pointer",opacity:t.Etat==="off"?.7:1}})})),e.createElement(Pe,{tab:"Cartes",key:"2"},e.createElement(K,{gutter:[16,16]},f.map((t,d)=>e.createElement(v,{key:d,xs:24,sm:12,md:8,lg:6},e.createElement("div",{style:{position:"relative"}},e.createElement(O,{hoverable:!!t.id&&t.Etat!=="off",onClick:()=>t.id&&t.Etat!=="off"&&R(t),style:{borderTop:`2px solid ${l(t.status,t)}`,opacity:t.Etat==="off"?.7:1,cursor:t.Etat==="off"?"not-allowed":"pointer"},title:t.Etat==="off"?"Machine hors ligne. Détails non disponibles.":""},e.createElement("div",{style:{textAlign:"center"}},e.createElement(Le,{level:4},t.Machine_Name||"Machine"),e.createElement(mt,{type:"dashboard",percent:parseFloat(t.TRS||"0"),status:parseFloat(t.TRS)>80?"success":parseFloat(t.TRS)>60?"normal":"exception"}),t.Etat==="on"&&e.createElement(he,{status:"processing",text:"Session active",style:{marginTop:8}}),e.createElement("div",{style:{marginTop:8}},e.createElement(De,null,"Production: ",parseFloat(t.Quantite_Bon||0))))),t.id!==1&&e.createElement("div",{className:"soon-overlay"},e.createElement("div",{className:"soon-text"},"En développement..."),e.createElement(_,{type:"default",size:"small"},"Configuration requise")),!t.id&&e.createElement("div",{className:"soon-overlay"},e.createElement("div",{className:"soon-text"},"En développement..."),e.createElement(_,{type:"default",size:"small"},"Configuration requise"))))))))),e.createElement("div",{style:{position:"fixed",bottom:20,right:20,zIndex:1e3}},e.createElement(at,{content:e.createElement("div",{style:{width:250}},e.createElement("p",null,e.createElement("strong",null,"Available tools:")),e.createElement("ul",null,e.createElement("li",null,"Machine view"),e.createElement("li",null,"Detailed performance analysis"),e.createElement("li",null,"Data export")),e.createElement(_,{type:"primary",block:!0},"User Guide")),title:"Help and tools",trigger:"click",placement:"topRight"},e.createElement(_,{type:"primary",shape:"circle",icon:e.createElement(Ce,null),size:"large",style:{boxShadow:"0 4px 12px rgba(0, 0, 0, 0.15)"}}))),e.createElement(bt,{visible:g,machine:i,machineHistory:c,loading:X,error:j,onClose:()=>a(!1),onRefresh:()=>u(i==null?void 0:i.id),darkMode:n}))};typeof window<"u"&&it();const Pt=()=>{const{isAuthenticated:n,loading:g}=Be(),a=(x,D)=>{const f=(()=>{if(typeof window<"u"){const i=window.location.origin;return i.includes("ngrok-free.app")||i.includes("ngrok.io")?i:"http://localhost:5000"}return"http://localhost:5000"})();return Fe[x](`${f}${D}`).retry(2).withCredentials().timeout(3e4)};p.useEffect(()=>{console.log("Initializing WebSocket connection from OptimizedDailyPerformanceDashboard"),C.connect();const x=()=>{document.visibilityState==="visible"&&(C.isConnected||(console.log("Tab is visible again, checking WebSocket connection..."),C.ensureConnection()))};return document.addEventListener("visibilitychange",x),()=>{document.removeEventListener("visibilitychange",x),C.disconnect()}},[]);const T=async()=>{var x,D,f,i;try{if(!n){z.error("Please log in to create test notifications",3);return}z.loading("Creating test notification...",1);const c=await a("post","/api/notifications/test-sse").send({});if(console.log("✅ Test SSE notification response:",c.body),c.body.success){const{notification:s,broadcast_result:N,database_saved:U,database_error:S}=c.body;U?(z.success(`✅ Test notification created and saved! ${s.title}`,4),console.log("📊 Database saved successfully. Notification will persist after reload.")):(z.warning(`⚠️ Notification broadcast but not saved to database: ${S}. Will disappear on reload.`,6),console.log("⚠️ Database error:",S),console.log("📡 SSE broadcast successful but notification is temporary")),console.log("📊 SSE Broadcast metrics:",N)}else z.warning("Test notification created but may not have been broadcast properly",3)}catch(c){console.error("❌ Failed to create test notification via SSE:",c),console.error("❌ Error details:",{message:c.message,status:c.status,response:((x=c.response)==null?void 0:x.body)||c.response,code:c.code}),c.code==="ECONNABORTED"?z.error("Test notification timed out - server may be busy",3):c.status===401||((D=c.response)==null?void 0:D.status)===401?(z.error("Authentication required - please login again",3),console.error("🔐 Authentication failed - user may need to re-login")):c.code==="ECONNREFUSED"?(z.error("Cannot connect to server - please check if the application is running",4),console.error("🔌 Connection refused - backend server may not be running")):c.status>=500?(z.error("Server error - please try again later",3),console.error("🚨 Server error (5xx) - check backend logs")):z.error(`Failed to create test notification: ${((i=(f=c.response)==null?void 0:f.body)==null?void 0:i.message)||c.message}`,3)}};return e.createElement("div",{className:"optimized-dashboard-wrapper"},e.createElement("div",{style:{position:"absolute",top:"10px",right:"10px",zIndex:2e3}},e.createElement(_,{type:"primary",onClick:T,disabled:g||!n,loading:g,style:{backgroundColor:n?"#52c41a":"#d9d9d9",borderColor:n?"#52c41a":"#d9d9d9",fontWeight:"bold"},title:n?"Create a test SSE notification":"Please log in to use this feature"},"🧪 Test SSE Notification")),e.createElement(ht,null))};export{Pt as default};
