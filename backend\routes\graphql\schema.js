

import { GraphQLObjectType, GraphQLSchema, GraphQLString, GraphQLInt, GraphQLBoolean, GraphQLList, GraphQLInputObjectType, GraphQLNonNull } from 'graphql';
import { dailyTableQueries, dailyTableTypes } from './dailyTableResolvers.js';
import { stopTableQueries, stopTableTypes } from './stopTableResolvers.js';
import { externalApiResolvers } from './externalApiResolvers.js';

// External API Types
const ExternalServiceType = new GraphQLObjectType({
  name: 'ExternalService',
  fields: {
    name: { type: GraphQLString },
    status: { type: GraphQLString },
    responseTime: { type: GraphQLInt },
    details: { type: GraphQLString }
  }
});

const HealthStatusType = new GraphQLObjectType({
  name: 'HealthStatus',
  fields: {
    overall: { type: GraphQLString },
    timestamp: { type: GraphQLString },
    services: { type: new GraphQLList(ExternalServiceType) },
    error: { type: GraphQLString }
  }
});

const WebhookType = new GraphQLObjectType({
  name: 'Webhook',
  fields: {
    name: { type: GraphQLString },
    url: { type: GraphQLString },
    active: { type: GraphQLBoolean },
    format: { type: GraphQLString },
    timeout: { type: GraphQLInt }
  }
});

const ConnectivityTestType = new GraphQLObjectType({
  name: 'ConnectivityTest',
  fields: {
    connected: { type: GraphQLBoolean },
    url: { type: GraphQLString },
    responseTime: { type: GraphQLInt },
    testDuration: { type: GraphQLInt },
    timestamp: { type: GraphQLString },
    error: { type: GraphQLString }
  }
});

const EndpointMonitorResultType = new GraphQLObjectType({
  name: 'EndpointMonitorResult',
  fields: {
    name: { type: GraphQLString },
    healthy: { type: GraphQLBoolean },
    url: { type: GraphQLString },
    responseTime: { type: GraphQLInt },
    status: { type: GraphQLInt },
    timestamp: { type: GraphQLString },
    error: { type: GraphQLString },
    metrics: { type: GraphQLString }
  }
});

const EndpointMonitorType = new GraphQLObjectType({
  name: 'EndpointMonitor',
  fields: {
    timestamp: { type: GraphQLString },
    totalEndpoints: { type: GraphQLInt },
    results: { type: new GraphQLList(EndpointMonitorResultType) }
  }
});

const NotificationResultType = new GraphQLObjectType({
  name: 'NotificationResult',
  fields: {
    success: { type: GraphQLBoolean },
    platform: { type: GraphQLString },
    timestamp: { type: GraphQLString },
    response: { type: GraphQLString },
    error: { type: GraphQLString }
  }
});

const MultiPlatformResultType = new GraphQLObjectType({
  name: 'MultiPlatformResult',
  fields: {
    platform: { type: GraphQLString },
    success: { type: GraphQLBoolean },
    timestamp: { type: GraphQLString },
    response: { type: GraphQLString },
    error: { type: GraphQLString }
  }
});

const MultiPlatformNotificationResultType = new GraphQLObjectType({
  name: 'MultiPlatformNotificationResult',
  fields: {
    success: { type: GraphQLBoolean },
    totalPlatforms: { type: GraphQLInt },
    successfulSends: { type: GraphQLInt },
    failedSends: { type: GraphQLInt },
    totalTime: { type: GraphQLInt },
    timestamp: { type: GraphQLString },
    results: { type: new GraphQLList(MultiPlatformResultType) }
  }
});

const WebhookRegistrationResultType = new GraphQLObjectType({
  name: 'WebhookRegistrationResult',
  fields: {
    success: { type: GraphQLBoolean },
    name: { type: GraphQLString },
    url: { type: GraphQLString },
    format: { type: GraphQLString },
    timeout: { type: GraphQLInt },
    message: { type: GraphQLString }
  }
});

const WebhookTestResultType = new GraphQLObjectType({
  name: 'WebhookTestResult',
  fields: {
    success: { type: GraphQLBoolean },
    webhook: { type: GraphQLString },
    timestamp: { type: GraphQLString },
    response: { type: GraphQLString },
    error: { type: GraphQLString },
    config: { type: GraphQLString }
  }
});

const WebhookStatusResultType = new GraphQLObjectType({
  name: 'WebhookStatusResult',
  fields: {
    success: { type: GraphQLBoolean },
    webhook: { type: GraphQLString },
    active: { type: GraphQLBoolean },
    message: { type: GraphQLString },
    timestamp: { type: GraphQLString }
  }
});

// Input Types
const EndpointInputType = new GraphQLInputObjectType({
  name: 'EndpointInput',
  fields: {
    name: { type: new GraphQLNonNull(GraphQLString) },
    url: { type: new GraphQLNonNull(GraphQLString) },
    headers: { type: GraphQLString }
  }
});

const NotificationInputType = new GraphQLInputObjectType({
  name: 'NotificationInput',
  fields: {
    platform: { type: new GraphQLNonNull(GraphQLString) },
    webhook_url: { type: new GraphQLNonNull(GraphQLString) },
    message: { type: new GraphQLNonNull(GraphQLString) },
    type: { type: GraphQLString },
    title: { type: GraphQLString }
  }
});

const PlatformInputType = new GraphQLInputObjectType({
  name: 'PlatformInput',
  fields: {
    platform: { type: new GraphQLNonNull(GraphQLString) },
    webhook_url: { type: new GraphQLNonNull(GraphQLString) },
    name: { type: GraphQLString }
  }
});

const MultiPlatformNotificationInputType = new GraphQLInputObjectType({
  name: 'MultiPlatformNotificationInput',
  fields: {
    message: { type: new GraphQLNonNull(GraphQLString) },
    platforms: { type: new GraphQLNonNull(new GraphQLList(PlatformInputType)) },
    type: { type: GraphQLString },
    title: { type: GraphQLString }
  }
});

const WebhookRegistrationInputType = new GraphQLInputObjectType({
  name: 'WebhookRegistrationInput',
  fields: {
    name: { type: new GraphQLNonNull(GraphQLString) },
    url: { type: new GraphQLNonNull(GraphQLString) },
    headers: { type: GraphQLString },
    timeout: { type: GraphQLInt },
    format: { type: GraphQLString }
  }
});

// Root Query combining all resolver types
const RootQuery = new GraphQLObjectType({
  name: 'RootQueryType',
  fields: {
    ...dailyTableQueries,
    ...stopTableQueries,
    // External API queries
    externalHealthStatus: {
      type: HealthStatusType,
      resolve: externalApiResolvers.Query.externalHealthStatus
    },
    registeredWebhooks: {
      type: new GraphQLList(WebhookType),
      resolve: externalApiResolvers.Query.registeredWebhooks
    },
    testExternalService: {
      type: ConnectivityTestType,
      args: {
        url: { type: new GraphQLNonNull(GraphQLString) },
        timeout: { type: GraphQLInt }
      },
      resolve: externalApiResolvers.Query.testExternalService
    },
    monitorEndpoints: {
      type: EndpointMonitorType,
      args: {
        endpoints: { type: new GraphQLNonNull(new GraphQLList(EndpointInputType)) },
        timeout: { type: GraphQLInt }
      },
      resolve: externalApiResolvers.Query.monitorEndpoints
    }
  }
});

// Root Mutation for external API operations
const RootMutation = new GraphQLObjectType({
  name: 'RootMutationType',
  fields: {
    sendExternalNotification: {
      type: NotificationResultType,
      args: {
        input: { type: new GraphQLNonNull(NotificationInputType) }
      },
      resolve: externalApiResolvers.Mutation.sendExternalNotification
    },
    sendMultiPlatformNotification: {
      type: MultiPlatformNotificationResultType,
      args: {
        input: { type: new GraphQLNonNull(MultiPlatformNotificationInputType) }
      },
      resolve: externalApiResolvers.Mutation.sendMultiPlatformNotification
    },
    registerWebhook: {
      type: WebhookRegistrationResultType,
      args: {
        input: { type: new GraphQLNonNull(WebhookRegistrationInputType) }
      },
      resolve: externalApiResolvers.Mutation.registerWebhook
    },
    testWebhook: {
      type: WebhookTestResultType,
      args: {
        webhookName: { type: new GraphQLNonNull(GraphQLString) }
      },
      resolve: externalApiResolvers.Mutation.testWebhook
    },
    updateWebhookStatus: {
      type: WebhookStatusResultType,
      args: {
        webhookName: { type: new GraphQLNonNull(GraphQLString) },
        active: { type: new GraphQLNonNull(GraphQLBoolean) }
      },
      resolve: externalApiResolvers.Mutation.updateWebhookStatus
    },
    performHealthCheck: {
      type: HealthStatusType,
      resolve: externalApiResolvers.Mutation.performHealthCheck
    }
  }
});

// Export the complete GraphQL schema
export const schema = new GraphQLSchema({
  query: RootQuery,
  mutation: RootMutation
});

// Export all types for potential reuse
export const types = {
  ...dailyTableTypes,
  ...stopTableTypes,
  ExternalServiceType,
  HealthStatusType,
  WebhookType,
  ConnectivityTestType,
  EndpointMonitorType,
  NotificationResultType,
  MultiPlatformNotificationResultType,
  WebhookRegistrationResultType,
  WebhookTestResultType,
  WebhookStatusResultType
};
