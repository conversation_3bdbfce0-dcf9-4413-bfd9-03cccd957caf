import{r as a}from"./index-gs31pxOi.js";const p="/api/graphql",C=()=>{const[_,i]=a.useState(!1),[f,d]=a.useState(null),t=a.useCallback(async(e,r={})=>{i(!0),d(null);try{const n=await fetch(p,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:e,variables:r})});if(!n.ok)throw new Error(`HTTP error! status: ${n.status}`);const s=await n.json();if(s.errors)throw new Error(s.errors.map(o=>o.message).join(", "));return i(!1),s.data}catch(n){throw d(n.message),i(!1),n}},[]),q=a.useCallback(async(e={})=>t(`
      query GetAllDailyProduction($filters: FilterInput) {
        getAllDailyProduction(filters: $filters) {
          Machine_Name
          Date_Insert_Day
          Run_Hours_Day
          Down_Hours_Day
          Good_QTY_Day
          Rejects_QTY_Day
          Speed_Day
          Availability_Rate_Day
          Performance_Rate_Day
          Quality_Rate_Day
          OEE_Day
          Shift
        }
      }
    `,{filters:e}),[t]),c=a.useCallback(async(e={})=>t(`
      query GetProductionChart($filters: FilterInput) {
        getProductionChart(filters: $filters) {
          Date_Insert_Day
          Total_Good_Qty_Day
          Total_Rejects_Qty_Day
          OEE_Day
          Speed_Day
          Availability_Rate_Day
          Performance_Rate_Day
          Quality_Rate_Day
        }
      }
    `,{filters:e}),[t]),l=a.useCallback(async(e={})=>t(`
      query GetProductionSidecards($filters: FilterInput) {
        getProductionSidecards(filters: $filters) {
          goodqty
          rejetqty
        }
      }
    `,{filters:e}),[t]),h=a.useCallback(async()=>t(`
      query {
        getUniqueDates
      }
    `),[t]),g=a.useCallback(async()=>t(`
      query {
        getMachineModels {
          model
        }
      }
    `),[t]),D=a.useCallback(async()=>t(`
      query {
        getMachineNames {
          Machine_Name
        }
      }
    `),[t]),u=a.useCallback(async(e={})=>t(`
      query GetMachinePerformance($filters: FilterInput) {
        getMachinePerformance(filters: $filters) {
          Machine_Name
          Shift
          production
          rejects
          downtime
          availability
          performance
          oee
          quality
          disponibilite
        }
      }
    `,{filters:e}),[t]),y=a.useCallback(async(e={})=>t(`
      query GetAvailabilityTrend($filters: FilterInput) {
        getAvailabilityTrend(filters: $filters) {
          date
          machine
          disponibilite
        }
      }
    `,{filters:e}),[t]),m=a.useCallback(async(e={})=>t(`
      query GetPerformanceMetrics($filters: FilterInput) {
        getPerformanceMetrics(filters: $filters) {
          machine
          model
          disponibilite
          stops
          mttr
          mtbf
        }
      }
    `,{filters:e}),[t]),b=a.useCallback(async(e={})=>{try{const[r,n,s,o]=await Promise.all([c(e),l(e),u(e),y(e)]);return{productionChart:(r==null?void 0:r.getProductionChart)||[],sidecards:(n==null?void 0:n.getProductionSidecards)||{goodqty:0,rejetqty:0},machinePerformance:(s==null?void 0:s.getMachinePerformance)||[],availabilityTrend:(o==null?void 0:o.getAvailabilityTrend)||[]}}catch(r){throw console.error("Error fetching dashboard data:",r),r}},[c,l,u,y]);return{loading:_,error:f,getAllDailyProduction:q,getProductionChart:c,getProductionSidecards:l,getUniqueDates:h,getMachineModels:g,getMachineNames:D,getMachinePerformance:u,getAvailabilityTrend:y,getPerformanceMetrics:m,getDashboardData:b,executeQuery:t}};export{C as u};
