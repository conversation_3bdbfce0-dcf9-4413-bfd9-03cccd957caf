import React from "react";
import { Row, Col, Space, Typography, Tag, Empty } from "antd";
import { LineChartOutlined } from "@ant-design/icons";
import {
  ResponsiveContainer,
  ComposedChart,
  Bar,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Legend,
} from "recharts";
import dayjs from "dayjs";
import ExpandableChart from "../../charts/ChartExpansion/ExpandableChart";
import {
  EnhancedQuantityBarChart,
  EnhancedTRSLineChart,
  EnhancedCycleTimeLineChart,
} from "../../charts/ChartExpansion/EnhancedChartComponents";

const { Text } = Typography;

/**
 * Enhanced container component for the trends chart section with expansion capabilities
 * @param {Object} props - Component props
 * @param {Array} props.data - The data to display in the charts
 * @param {Array} props.colors - Array of colors for styling
 * @param {string} props.dateRangeType - The type of date range (day, week, month)
 * @param {string} props.dateFilter - The date filter value
 * @param {function} props.formatDateRange - Function to format the date range for display
 * @returns {JSX.Element} The rendered enhanced chart section
 */
const EnhancedTrendsChartSection = ({
  data,
  colors,
  dateRangeType,
  dateFilter,
  formatDateRange
}) => {
  // Chart expansion handlers
  const handleChartExpand = (chartName) => {
    console.log(`Chart expanded: ${chartName}`);
    // You can add analytics tracking here
  };

  const handleChartCollapse = (chartName) => {
    console.log(`Chart collapsed: ${chartName}`);
    // You can add analytics tracking here
  };

  return (
    <>
      {/* Enhanced Quantity Charts */}
      <Col span={24}>
        <Row gutter={[24, 24]}>
          <Col xs={24} md={12}>
            <ExpandableChart
              title={`Quantité Bonne - ${dateRangeType === "day" ? "Journalière" : dateRangeType === "week" ? "Hebdomadaire" : "Mensuelle"}`}
              data={data}
              chartType="bar"
              expandMode="modal"
              onExpand={() => handleChartExpand("quantity-good")}
              onCollapse={() => handleChartCollapse("quantity-good")}
              exportEnabled={true}
              zoomEnabled={true}
              extra={
                <Tag color={dateFilter ? "blue" : "green"}>
                  {formatDateRange(dateFilter, dateRangeType)}
                </Tag>
              }
            >
              <EnhancedQuantityBarChart
                data={data}
                title="Quantité Bonne"
                dataKey="good"
                color={colors[2]}
                tooltipLabel="Quantité bonne"
              />
            </ExpandableChart>
          </Col>

          <Col xs={24} md={12}>
            <ExpandableChart
              title={`Quantité Rejetée - ${dateRangeType === "day" ? "Journalière" : dateRangeType === "week" ? "Hebdomadaire" : "Mensuelle"}`}
              data={data}
              chartType="bar"
              expandMode="modal"
              onExpand={() => handleChartExpand("quantity-reject")}
              onCollapse={() => handleChartCollapse("quantity-reject")}
              exportEnabled={true}
              zoomEnabled={true}
              extra={
                <Tag color={dateFilter ? "blue" : "green"}>
                  {formatDateRange(dateFilter, dateRangeType)}
                </Tag>
              }
            >
              <EnhancedQuantityBarChart
                data={data}
                title="Quantité Rejetée"
                dataKey="reject"
                color={colors[4]}
                label="Quantité"
                tooltipLabel="Quantité rejetée"
                isKg={true}
              />
            </ExpandableChart>
          </Col>
        </Row>
      </Col>

      {/* Enhanced TRS and Cycle Time Charts */}
      <Col xs={24} md={24}>
        <Row gutter={[24, 24]}>
          <Col xs={24} md={12}>
            <ExpandableChart
              title="Tendances TRS (Taux de Rendement Synthétique)"
              data={data}
              chartType="line"
              expandMode="modal"
              onExpand={() => handleChartExpand("trs-trends")}
              onCollapse={() => handleChartCollapse("trs-trends")}
              exportEnabled={true}
              zoomEnabled={true}
              extra={<Tag color="cyan">Évolution TRS</Tag>}
            >
              <EnhancedTRSLineChart
                data={data}
                color={colors[0]}
              />
            </ExpandableChart>
          </Col>

          <Col xs={24} md={12}>
            <ExpandableChart
              title="Tendances Cycle De Temps"
              data={data}
              chartType="line"
              expandMode="modal"
              onExpand={() => handleChartExpand("cycle-time-trends")}
              onCollapse={() => handleChartCollapse("cycle-time-trends")}
              exportEnabled={true}
              zoomEnabled={true}
              extra={<Tag color="orange">Évolution Cycle</Tag>}
            >
              <EnhancedCycleTimeLineChart
                data={data}
                color={colors[1]}
              />
            </ExpandableChart>
          </Col>
        </Row>
      </Col>


    </>
  );
};

/**
 * Enhanced Combined Chart Component
 */
const EnhancedCombinedChart = ({
  data,
  colors,
  height = 400,
  enhanced = false,
  zoom = 1,
}) => {
  if (!data || data.length === 0) {
    return (
      <div style={{ height, display: "flex", alignItems: "center", justifyContent: "center" }}>
        <Empty description="Aucune donnée disponible" />
      </div>
    );
  }

  const margin = enhanced ? { top: 20, right: 30, left: 30, bottom: 20 } : { top: 16, right: 24, left: 24, bottom: 16 };
  const fontSize = enhanced ? 14 : 12;

  return (
    <ResponsiveContainer width="100%" height={height}>
      <ComposedChart data={data} margin={margin}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f5f5f5" />
        <XAxis
          dataKey="date"
          tick={{ fill: "#666", fontSize }}
          tickFormatter={(date) => {
            try {
              if (date && dayjs(date).isValid()) {
                return enhanced ? dayjs(date).format("DD/MM/YYYY") : dayjs(date).format("DD/MM");
              }
              return "N/A";
            } catch (e) {
              return "N/A";
            }
          }}
          interval={enhanced ? 0 : "preserveStartEnd"}
          angle={enhanced ? -45 : 0}
          textAnchor={enhanced ? "end" : "middle"}
          height={enhanced ? 80 : 60}
        />
        <YAxis
          yAxisId="left"
          tick={{ fontSize }}
          tickFormatter={(value) => value.toLocaleString()}
          label={{
            value: "Quantité",
            angle: -90,
            position: "insideLeft",
            style: { fill: "#666", fontSize },
          }}
        />
        <YAxis
          yAxisId="right"
          orientation="right"
          tick={{ fontSize }}
          tickFormatter={(value) => `${value}%`}
          domain={[0, 100]}
          label={{
            value: "TRS (%)",
            angle: 90,
            position: "insideRight",
            style: { fill: "#666", fontSize },
          }}
        />
        <RechartsTooltip
          contentStyle={{
            backgroundColor: "#fff",
            border: "1px solid #f0f0f0",
            borderRadius: 8,
            boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
            fontSize: enhanced ? 14 : 12,
          }}
          formatter={(value, name) => {
            const isValidNumber = typeof value === "number" && !isNaN(value);
            const formattedValue = isValidNumber
              ? Number.isInteger(value)
                ? value.toLocaleString()
                : value.toFixed(2)
              : value;

            const labels = {
              good: "Quantité bonne",
              reject: "Quantité rejetée (kg)",
              oee: "TRS (%)",
            };

            return [formattedValue, labels[name] || name];
          }}
          labelFormatter={(label) => {
            try {
              if (label && dayjs(label).isValid()) {
                return `Date: ${dayjs(label).format("DD/MM/YYYY")}`;
              }
              return "Date: N/A";
            } catch (e) {
              return "Date: N/A";
            }
          }}
        />
        <Legend
          wrapperStyle={{ paddingTop: 20 }}
          formatter={(value) => {
            const labels = {
              good: "Quantité bonne",
              reject: "Quantité rejetée (kg)",
              oee: "TRS (%)",
            };
            return <span style={{ color: "#666" }}>{labels[value] || value}</span>;
          }}
        />
        <Bar
          yAxisId="left"
          dataKey="good"
          name="good"
          fill={colors[2]}
          maxBarSize={enhanced ? 60 : 40}
          stackId="production"
        />
        <Bar
          yAxisId="left"
          dataKey="reject"
          name="reject"
          fill={colors[4]}
          maxBarSize={enhanced ? 60 : 40}
          stackId="production"
        />
        <Line
          yAxisId="right"
          type="monotone"
          dataKey="oee"
          name="oee"
          stroke={colors[0]}
          strokeWidth={enhanced ? 3 : 2}
          dot={{ r: enhanced ? 6 : 4, fill: colors[0] }}
          activeDot={{ r: enhanced ? 8 : 6, fill: "#fff", stroke: colors[0], strokeWidth: 2 }}
        />
      </ComposedChart>
    </ResponsiveContainer>
  );
};

export default EnhancedTrendsChartSection;
