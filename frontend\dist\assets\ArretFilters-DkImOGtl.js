import{r as Y,$ as ee,aj as H,R as a,U as k,a8 as z,a9 as N,a3 as x,ak as U,a2 as I,W as Q,N as B,a1 as te,al as ae}from"./index-gs31pxOi.js";import{i as ne}from"./isoWeek-DO7WpCT2.js";import{c as oe,a as re,b as le,I as ie,u as se,d as ce,e as de,i as ue}from"./eventHandlers-Dv3C7g4x.js";import{S as me}from"./index-J5dbWBeo.js";import{R as W}from"./CalendarOutlined-Dy17No_z.js";import{R as q}from"./ClockCircleOutlined-CiulfqLg.js";import{e as ge,D as G}from"./ClearOutlined-BrdHce1s.js";import{R as pe}from"./FilterOutlined-CWlXg5FA.js";const J=p=>{if(!p)return null;try{const e=String(p).trim();if(e.includes("/")){const f=e.split(" "),d=f[0],s=f[1]||"00:00:00",[h,T,M]=d.split("/");if(h&&T&&M&&h.length<=2&&T.length<=2&&M.length===4){const D=h.padStart(2,"0"),S=T.padStart(2,"0"),E=s.split(":"),R=parseInt(E[0])||0,y=parseInt(E[1])||0,g=parseInt(E[2])||0,c=new Date(parseInt(M),parseInt(T)-1,parseInt(h),R,y,g);if(!isNaN(c.getTime()))return c}}if(e.includes("-")&&e.includes(" ")){const f=e.indexOf(" "),d=e.substring(0,f),s=e.substring(f+1);if(s.includes("-")){const h=s.lastIndexOf("-"),T=s.substring(0,h),M=s.substring(h+1);if(M.includes("-")){const[D,S]=M.split("-");if(d&&D&&S&&T){const E=T.split(":"),R=parseInt(E[0])||0,y=parseInt(E[1])||0,g=parseInt(E[2])||0,c=new Date(parseInt(d),parseInt(D)-1,parseInt(S),R,y,g);if(!isNaN(c.getTime()))return c}}}}const o=new Date(e);return isNaN(o.getTime())?null:o}catch(e){return console.warn("Date parsing error:",e,"for date:",p),null}},fe=(p,e,o,f)=>{const d=Y.useRef(!1),s=Y.useRef(!0);Y.useRef(Date.now());const h=Y.useCallback(async(D=!1)=>{var E,R,y,g,c;if(d.current&&!D){console.log("⏸️ Fetch already in progress, skipping...");return}if(!s.current){console.log("⏸️ Component unmounted, skipping fetch");return}d.current=!0,console.log("🚀 Starting queued data fetch with state:",{selectedMachineModel:e.selectedMachineModel,selectedMachine:e.selectedMachine,selectedDate:e.selectedDate,dateRangeType:e.dateRangeType,forceRefresh:D}),e.selectedMachineModel&&e.selectedMachine&&e.selectedDate&&o(n=>({...n,complexFilterLoading:!0}));try{const n={model:e.selectedMachineModel||null,machine:e.selectedMachine||null,date:e.selectedDate?typeof e.selectedDate=="string"?e.selectedDate:e.selectedDate.format("YYYY-MM-DD"):null,startDate:e.selectedDate?typeof e.selectedDate=="string"?e.selectedDate:e.selectedDate.clone().startOf(e.dateRangeType).format("YYYY-MM-DD"):null,endDate:e.selectedDate?typeof e.selectedDate=="string"?e.selectedDate:e.selectedDate.clone().endOf(e.dateRangeType).format("YYYY-MM-DD"):null,dateRangeType:e.dateRangeType||"month"};console.log("🔍 Built filters for GraphQL queries:",{filters:n,selectedDateType:typeof e.selectedDate,selectedDateValue:e.selectedDate,selectedMachineModel:e.selectedMachineModel,selectedMachine:e.selectedMachine,dateRangeType:e.dateRangeType}),o(t=>({...t,loading:!0,essentialLoading:!0}));const w=await p.getEssentialData(n);if(!s.current)return;if(w.sidecards){const t=[{title:"Total Arrêts",value:w.sidecards.Arret_Totale||0,icon:"🚨"},{title:"Arrêts Non Déclarés",value:w.sidecards.Arret_Totale_nondeclare||0,icon:"⚠️"},{title:"Durée Totale",value:0,suffix:"min",icon:"⏱️"},{title:"Durée Moyenne",value:0,suffix:"min",icon:"⏱️"},{title:"Interventions",value:0,icon:"🔧"}];o(l=>({...l,arretStats:t,totalStops:w.sidecards.Arret_Totale||0,undeclaredStops:w.sidecards.Arret_Totale_nondeclare||0,essentialLoading:!1}))}if(await new Promise(t=>setTimeout(t,100)),n.machine){const t=await p.getPerformanceData(n);if(!s.current)return;o(l=>({...l,mttr:t.performance.mttr,mtbf:t.performance.mtbf,doper:t.performance.doper,showPerformanceMetrics:!0}))}else o(t=>({...t,mttr:0,mtbf:0,doper:0,showPerformanceMetrics:!1}));await new Promise(t=>setTimeout(t,200)),o(t=>({...t,detailedLoading:!0}));const b=await p.getChartData(n);if(console.log("🔍 Raw backend chart data:",b),console.log("🔍 Raw machine comparison data:",b.machineComparison),!s.current)return;if(b.topStops){console.log("🔍 Processing topStops data:",b.topStops);const t=b.topStops.reduce((i,F)=>i+F.count,0),l=b.topStops.map(i=>({...i,percentage:t>0?(i.count/t*100).toFixed(1):0}));console.log("📊 topStopsWithPercentage:",l);const m=(b.machineComparison||[]).map(i=>{const F=i.totalStops||i.stops||i.incidents||0,O=i.totalDuration||i.duration||0;return{Machine_Name:i.Machine_Name,machine:i.Machine_Name,name:i.Machine_Name,stops:F,totalStops:F,totalDuration:O,avgDuration:F>0?(O/F).toFixed(1):0}});console.log("🎯 Formatted machine comparison data for charts:",m);const u=l.map(i=>({reason:i.stopName||i.name||"Non défini",count:i.count||0,name:i.stopName||i.name||"Non défini",value:i.count||0,percentage:i.percentage,stopName:i.stopName||i.name||"Non défini"}));console.log("🎯 Formatted stopReasons for chart:",u),o(i=>({...i,topStopsData:l,machineComparison:m,stopReasons:u,chartData:[],durationTrend:[],disponibiliteTrendData:[],downtimeParetoData:l,mttrCalendarData:[],disponibiliteByMachineData:[]}))}await new Promise(t=>setTimeout(t,300));const v=await p.getTableData(n);if(!s.current)return;o(t=>({...t,stopsData:v.stopsData}));const r=((E=v.stopsData)==null?void 0:E.filter(t=>{if(e.selectedMachine&&t.Machine_Name!==e.selectedMachine)return!1;if(e.selectedDate&&t.Date_Insert){const l=J(t.Date_Insert),m=new Date(e.selectedDate);if(console.log("🔍 Date filtering:",{stopDateString:t.Date_Insert,parsedStopDate:l,filterDate:m,dateRangeType:e.dateRangeType}),!l)return console.warn("❌ Failed to parse stop date:",t.Date_Insert),!1;if(e.dateRangeType==="day"){const u=l.toDateString()===m.toDateString();return console.log("📅 Day filter result:",u),u}else if(e.dateRangeType==="week"){const u=new Date(m);u.setDate(m.getDate()-m.getDay());const i=new Date(u);i.setDate(u.getDate()+6);const F=l>=u&&l<=i;return console.log("📅 Week filter result:",F,{weekStart:u.toDateString(),weekEnd:i.toDateString(),stopDate:l.toDateString()}),F}else if(e.dateRangeType==="month"){const u=l.getMonth()===m.getMonth()&&l.getFullYear()===m.getFullYear();return console.log("📅 Month filter result:",u,{stopMonth:l.getMonth(),stopYear:l.getFullYear(),filterMonth:m.getMonth(),filterYear:m.getFullYear()}),u}}return!0}))||[];o(t=>({...t,filteredStopsData:r}));const C={};console.log("🔧 Starting evolution data generation with FILTERED data:",{filteredStopsDataLength:(r==null?void 0:r.length)||0,totalStopsDataLength:((R=v.stopsData)==null?void 0:R.length)||0,sampleFilteredStops:(r==null?void 0:r.slice(0,3))||[],sampleFilteredDates:(r==null?void 0:r.slice(0,5).map(t=>t.Date_Insert))||[],currentFilters:{selectedMachine:e.selectedMachine,selectedDate:e.selectedDate,dateRangeType:e.dateRangeType}}),r.forEach(t=>{if(t.Date_Insert&&typeof t.Date_Insert=="string"){let l,m=t.Date_Insert.toString();if(console.log("🔍 Processing date:",m),l=J(t.Date_Insert),l){const u=l.toISOString().split("T")[0];C[u]||(C[u]={date:u,stops:0,duration:0}),C[u].stops++,t.duration_minutes&&t.duration_minutes>0&&(C[u].duration+=parseFloat(t.duration_minutes))}else console.warn("❌ Invalid date format, skipping:",t.Date_Insert)}}),console.log("🔧 Daily stats generated:",C);let _=Object.values(C).sort((t,l)=>new Date(t.date)-new Date(l.date));if(e.selectedDate&&e.dateRangeType)if(console.log("🎯 Using date filter for evolution chart:",{selectedDate:e.selectedDate,dateRangeType:e.dateRangeType}),e.dateRangeType==="month"){const t=new Date(e.selectedDate),l=t.getMonth(),m=t.getFullYear();_=_.filter(u=>{const i=new Date(u.date);return i.getMonth()===l&&i.getFullYear()===m})}else if(e.dateRangeType==="week"){const t=new Date(e.selectedDate),l=new Date(t);l.setDate(t.getDate()-t.getDay());const m=new Date(l);m.setDate(l.getDate()+6),_=_.filter(u=>{const i=new Date(u.date);return i>=l&&i<=m})}else{const t=new Date(e.selectedDate),l=new Date(t);l.setDate(t.getDate()-3);const m=new Date(t);m.setDate(t.getDate()+3),_=_.filter(u=>{const i=new Date(u.date);return i>=l&&i<=m})}else console.log("📊 No date filter active - showing all available data for selected model");_=_.map(t=>{console.log("🔄 Formatting displayDate for:",t.date);let l,m=!1,u;if(t.date&&typeof t.date=="string")if(t.date.match(/^\d{4}-\d{2}-\d{2}$/))l=new Date(t.date),m=!isNaN(l.getTime());else{const i=t.date.match(/^(\d{4}) \d{2}:\d{2}:\d{2}-(\d{1,2})-\s*(\d{1,2})$/);if(i){const[F,O,j,V]=i,K=`${O}-${j.padStart(2,"0")}-${V.padStart(2,"0")}`;l=new Date(K),m=!isNaN(l.getTime()),console.log("🔧 Re-parsed date:",t.date,"->",K,"Valid:",m)}}if(m&&l)u=l.toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit"}),console.log("✅ Generated displayDate:",u);else{const i=t.date.match(/^(\d{4}) \d{2}:\d{2}:\d{2}-(\d{1,2})-\s*(\d{1,2})$/);if(i){const[F,O,j,V]=i;u=`${V.padStart(2,"0")}/${j.padStart(2,"0")}`,console.log("🔧 Fallback displayDate:",u)}else u=t.date.slice(0,10),console.log("⚠️ Using raw fallback displayDate:",u)}return{...t,displayDate:u}}),console.log("📊 Generated evolution data:",{dailyStatsKeys:Object.keys(C),evolutionDataLength:_.length,evolutionData:_.slice(0,3),fullEvolutionData:_});let P=_;if(_.length===0?(console.log("⚠️ No evolution data found!"),console.log("🔍 Debug info for empty evolution data:",{filteredStopsDataLength:(r==null?void 0:r.length)||0,dailyStatsCount:Object.keys(C).length,hasSelectedDate:!!e.selectedDate,hasSelectedMachine:!!e.selectedMachine,dateRangeType:e.dateRangeType,sampleFilteredStops:(r==null?void 0:r.slice(0,2))||[]}),P=[]):console.log("✅ Using real evolution data:",P.length,"items"),o(t=>({...t,chartData:P})),((y=v.stopsData)==null?void 0:y.length)>0){const t={};v.stopsData.forEach(m=>{const u=m.Regleur_Prenom||"Non assigné";t[u]=(t[u]||0)+1});const l=Object.entries(t).map(([m,u])=>({operator:m,interventions:u}));o(m=>({...m,operatorStats:l}))}let A=[],L=[],$=[];e.selectedMachine&&((g=v.stopsData)==null?void 0:g.length)>0&&(console.log("🔧 Calculating advanced analytics for machine:",e.selectedMachine),console.log("🔍 Using FILTERED data for Pareto analysis:",{totalStopsData:((c=v.stopsData)==null?void 0:c.length)||0,filteredStopsData:(r==null?void 0:r.length)||0,selectedMachine:e.selectedMachine,selectedDate:e.selectedDate,dateRangeType:e.dateRangeType,sampleFilteredStops:(r==null?void 0:r.slice(0,2))||[]}),A=oe(r,e.dateRangeType),L=re(r),$=le(r),console.log("🔧 Advanced analytics calculated with FILTERED data:",{disponibiliteTrendData:A.length,mttrCalendarData:L.length,downtimeParetoData:$.length,downtimeParetoSample:$.slice(0,3),downtimeParetoDataFull:$})),o(t=>({...t,loading:!1,detailedLoading:!1,complexFilterLoading:!1,disponibiliteTrendData:A,mttrCalendarData:L,downtimeParetoData:$,arretsByRange:v.stopsData||[],sidebarStats:t.arretStats}))}catch(n){if(console.error("❌ Error in queued data fetch:",n),!s.current)return;o(w=>({...w,loading:!1,essentialLoading:!1,detailedLoading:!1,complexFilterLoading:!1,error:n.message||"Failed to fetch data",arretStats:[{title:"Total Arrêts",value:0,icon:"🚨"},{title:"Arrêts Non Déclarés",value:0,icon:"⚠️"},{title:"Durée Totale",value:0,suffix:"min",icon:"⏱️"},{title:"Durée Moyenne",value:0,suffix:"min",icon:"⏱️"},{title:"Interventions",value:0,icon:"🔧"}]}))}finally{d.current=!1}},[e.selectedMachineModel,e.selectedMachine,e.selectedDate,e.dateRangeType,p,o]),T=Y.useCallback(async()=>{try{const D=await p.getMachineModels(),S=await p.getMachineNames();if(s.current){const E=S.map(R=>{var c;const y=R.Machine_Name;let g="";return y.startsWith("IPSO")?g="IPSO":y.startsWith("IPS")?g="IPS":y.startsWith("CCM")?g="CCM":g=((c=y.match(/^[A-Za-z]+/))==null?void 0:c[0])||"UNKNOWN",{name:y,model:g}});o(R=>({...R,machineModels:D,machineNames:E,selectedMachineModel:"IPS"}))}}catch(D){console.error("❌ Error initializing machine data:",D),s.current&&o(S=>({...S,error:D.message}))}},[p,o]),M=Y.useCallback(D=>{s.current=D},[]);return{fetchDataInQueue:h,initializeMachineData:T,setMounted:M,isMounted:s.current,pendingFetch:d.current}},he=p=>({async getEssentialData(o){const f=await p(`
        query($filters: StopFilterInput) {
          getStopSidecards(filters: $filters) {
            Arret_Totale
            Arret_Totale_nondeclare
          }
        }
      `,{filters:o});return{sidecards:f==null?void 0:f.getStopSidecards,priority:1,loadingState:"essentialLoading"}},async getPerformanceData(o){const f=await p(`
        query($filters: StopFilterInput) {
          getAllMachineStops(filters: $filters) {
            Date_Insert
            Machine_Name
            Code_Stop
            duration_minutes
            Debut_Stop
            Fin_Stop_Time
          }
        }
      `,{filters:o}),d=(f==null?void 0:f.getAllMachineStops)||[];let s=0,h=0,T=0;if(d.length>0){const M=d.reduce((y,g)=>{if(g.duration_minutes&&g.duration_minutes>0)return y+parseFloat(g.duration_minutes);if(g.Debut_Stop&&g.Fin_Stop_Time)try{const c=b=>{if(b.includes(" ")){const[v,r]=b.split(" "),[C,_,P]=v.split("/"),[A,L]=r.split(":");return new Date(P,_-1,C,A,L)}return new Date(b)},n=c(g.Debut_Stop),w=c(g.Fin_Stop_Time);if(!isNaN(n.getTime())&&!isNaN(w.getTime())){const b=w-n,v=Math.max(0,Math.floor(b/(1e3*60)));return y+v}}catch(c){console.warn("Error calculating duration for stop:",g,c)}return y},0);s=d.length>0?M/d.length:0;const D=d.length>0?30:1,S=d.length/D;h=S>0?1440/S:0;const E=M,R=D*24*60;T=R>0?(R-E)/R*100:0,s=Math.max(0,Math.min(s,1440)),h=Math.max(0,Math.min(h,10080)),T=Math.max(0,Math.min(T,100))}return{performance:{mttr:Number(s.toFixed(1)),mtbf:Number(h.toFixed(1)),doper:Number(T.toFixed(1))},priority:2,loadingState:"essentialLoading"}},async getChartData(o){const[f,d]=await Promise.all([p(`
          query($filters: StopFilterInput) {
            getTop5Stops(filters: $filters) {
              stopName
              count
            }
          }
        `,{filters:o}),p(`
          query($filters: StopFilterInput) {
            getMachineStopComparison(filters: $filters) {
              Machine_Name
              stops
              totalDuration
            }
          }
        `,{filters:o})]);return{topStops:(f==null?void 0:f.getTop5Stops)||[],machineComparison:(d==null?void 0:d.getMachineStopComparison)||[],priority:3,loadingState:"detailedLoading"}},async getTableData(o){const f=await p(`
        query($filters: StopFilterInput) {
          getAllMachineStops(filters: $filters) {
            Date_Insert
            Machine_Name
            Part_NO
            Code_Stop
            Debut_Stop
            Fin_Stop_Time
            Regleur_Prenom
            duration_minutes
          }
        }
      `,{filters:o});return{stopsData:(f==null?void 0:f.getAllMachineStops)||[],priority:4,loadingState:"detailedLoading"}},async getMachineModels(){const o=await p(`
        query {
          getStopMachineModels {
            model
          }
        }
      `);return(o==null?void 0:o.getStopMachineModels)||[]},async getMachineNames(){const o=await p(`
        query {
          getStopMachineNames {
            Machine_Name
          }
        }
      `);return(o==null?void 0:o.getStopMachineNames)||[]}});H.extend(ne);H.extend(ue);const X=Y.createContext(),De=()=>{const p=Y.useContext(X);return p||(console.error("⚠️  useArretQueuedContext: Context not found!"),null)},ve=({children:p})=>{const[e,o]=Y.useState({machineModels:[],machineNames:[],selectedMachineModel:"",selectedMachine:"",filteredMachineNames:[],dateRangeType:"month",selectedDate:null,dateFilterActive:!1,dateRangeDescription:"",arretStats:[],stopsData:[],topStopsData:[],durationTrend:[],machineComparison:[],operatorStats:[],stopReasons:[],chartData:[],filteredStopsData:[],disponibiliteTrendData:[],downtimeParetoData:[],mttrCalendarData:[],disponibiliteByMachineData:[],loading:!1,essentialLoading:!1,detailedLoading:!1,complexFilterLoading:!1,error:null,isChartModalVisible:!1,chartModalContent:null,chartOptions:{activeTab:"bar"},mttr:0,mtbf:0,doper:0,showPerformanceMetrics:!1,totalStops:0,undeclaredStops:0,avgDuration:0,totalDuration:0,sidebarStats:[],arretsByRange:[]}),[f,d]=Y.useState(ie),s=se(f,d),h=ce({stopsData:e.stopsData,rawChartData:e.durationTrend,selectedMachine:e.selectedMachine,selectedMachineModel:e.selectedMachineModel,selectedDate:e.selectedDate,doper:e.doper}),T=Y.useRef(!0),M=Y.useRef(!1),D=Y.useCallback(async(c,n={})=>{const w=(()=>{if(typeof window<"u"){const r=window.location.origin;return r.includes("ngrok-free.app")||r.includes("ngrok.io")?r:"http://localhost:5000"}return"http://localhost:5000"})(),v=(await ee.post(`${w}/api/graphql`).send({query:c,variables:n}).set("Content-Type","application/json").withCredentials().timeout(3e4).retry(2)).body;if(v.errors)throw new Error(v.errors[0].message);return v.data},[]),S=he(D),E=fe(S,e,o),R=de(e,o,E,s),y=Y.useCallback((c,n)=>{if(!c)return{short:"",full:""};const w=H(c);if(n==="day")return{short:w.format("DD/MM"),full:w.format("DD/MM/YYYY")};if(n==="week"){const b=w.startOf("isoWeek"),v=w.endOf("isoWeek");return{short:`${b.format("DD/MM")} - ${v.format("DD/MM")}`,full:`Semaine du ${b.format("DD/MM/YYYY")} au ${v.format("DD/MM/YYYY")}`}}else if(n==="month")return{short:w.format("MM/YYYY"),full:w.format("MMMM YYYY")};return{short:"",full:""}},[]);Y.useEffect(()=>{if(e.selectedMachineModel){const c=e.machineNames.filter(n=>n.model===e.selectedMachineModel||typeof n=="string"&&n.includes(e.selectedMachineModel));o(n=>({...n,filteredMachineNames:c})),e.selectedMachine&&!c.find(n=>(typeof n=="string"?n:n.name)===e.selectedMachine)&&o(n=>({...n,selectedMachine:""}))}else o(c=>({...c,filteredMachineNames:[]}))},[e.selectedMachineModel,e.machineNames,e.selectedMachine]),Y.useEffect(()=>{(h.totalDuration||h.averageDuration||h.totalInterventions)&&o(c=>({...c,arretStats:c.arretStats.map(n=>n.title==="Durée Totale"?{...n,value:h.totalDuration}:n.title==="Durée Moyenne"?{...n,value:h.averageDuration}:n.title==="Interventions"?{...n,value:h.totalInterventions}:n)}))},[h.totalDuration,h.averageDuration,h.totalInterventions]),Y.useEffect(()=>{if(M.current)return;(async()=>{try{E.setMounted(!0),await E.initializeMachineData(),M.current=!0}catch(n){console.error("❌ Error during initial load:",n),T.current&&o(w=>({...w,error:n.message}))}})()},[]),Y.useEffect(()=>{console.log("🎯 Data fetch effect triggered:",{initialLoadComplete:M.current,selectedMachineModel:e.selectedMachineModel,selectedMachine:e.selectedMachine,selectedDate:e.selectedDate,selectedDateType:typeof e.selectedDate,selectedDateFormatted:e.selectedDate?typeof e.selectedDate=="string"?e.selectedDate:e.selectedDate.format("YYYY-MM-DD"):null,dateRangeType:e.dateRangeType,dateFilterActive:e.dateFilterActive}),M.current&&e.selectedMachineModel?(console.log("✅ Triggering data fetch with model:",e.selectedMachineModel),E.fetchDataInQueue()):console.log("⏸️ Data fetch skipped - waiting for initialization or machine model")},[e.selectedMachineModel,e.selectedMachine,e.selectedDate,e.dateRangeType]),Y.useEffect(()=>()=>{T.current=!1,E.setMounted(!1)},[]);const g={...e,computedValues:h,formatDateRange:y,...R,refreshData:()=>E.fetchDataInQueue(!0),skeletonManager:s,showChartModal:c=>{o(n=>({...n,isChartModalVisible:!0,chartModalContent:c}))},hideChartModal:()=>{o(c=>({...c,isChartModalVisible:!1,chartModalContent:null}))},openChartModal:c=>{o(n=>({...n,isChartModalVisible:!0,chartModalContent:c}))},setChartOptions:c=>{o(n=>({...n,chartOptions:typeof c=="function"?c(n.chartOptions):c}))}};return a.createElement(X.Provider,{value:g},p)},{Option:Z}=U,Ye=({onFilterChange:p})=>{const e=De();if(!e)return a.createElement("div",null,"Context not available");const{machineModels:o=[],filteredMachineNames:f=[],selectedMachineModel:d="",selectedMachine:s="",handleMachineModelChange:h,handleMachineChange:T,dateRangeType:M="day",selectedDate:D=null,dateFilterActive:S=!1,handleDateRangeTypeChange:E,handleDateChange:R,loading:y=!1,stopsData:g=[],resetFilters:c,handleRefresh:n,complexFilterLoading:w=!1,dataManager:b}=e;Y.useEffect(()=>{const r={model:d,machine:s,date:D==null?void 0:D.format("YYYY-MM-DD"),dateType:M,dateFilterActive:S,hasAllFilters:d&&s&&S,dataCount:g==null?void 0:g.length};p&&typeof p=="function"&&p(r)},[d,s,D,M,S,g==null?void 0:g.length,p,o,f]);const v=()=>M==="day"?a.createElement(G,{value:D,onChange:R,format:"DD/MM/YYYY",placeholder:"Sélectionner une date",allowClear:!0,style:{width:"100%"}}):M==="week"?a.createElement(G,{value:D,onChange:R,picker:"week",format:"[Semaine] w YYYY",placeholder:"Sélectionner une semaine",allowClear:!0,style:{width:"100%"}}):M==="month"?a.createElement(G,{value:D,onChange:R,picker:"month",format:"MMMM YYYY",placeholder:"Sélectionner un mois",allowClear:!0,style:{width:"100%"}}):null;return a.createElement(k,{direction:"vertical",size:"middle",style:{width:"100%"}},a.createElement(z,{gutter:[16,16],align:"middle"},a.createElement(N,{xs:24,sm:12,lg:6},a.createElement("div",null,a.createElement("div",{style:{marginBottom:4,fontSize:"12px",color:x.LIGHT_GRAY}},"Modèle de Machine"),a.createElement(U,{placeholder:"Sélectionner un modèle",value:d,onChange:h,style:{width:"100%"},allowClear:!0,showSearch:!0,filterOption:(r,C)=>C.children.toLowerCase().includes(r.toLowerCase())},o.map(r=>{const C=typeof r=="object"?r.model:r;return a.createElement(Z,{key:C,value:C},C)})))),a.createElement(N,{xs:24,sm:12,lg:6},a.createElement("div",null,a.createElement("div",{style:{marginBottom:4,fontSize:"12px",color:x.LIGHT_GRAY}},"Machine Spécifique"),a.createElement(U,{placeholder:"Sélectionner une machine",value:s,onChange:r=>{typeof T=="function"?T(r):console.error("handleMachineChange is not a function!",typeof T)},style:{width:"100%"},allowClear:!0,showSearch:!0,disabled:!d&&f.length===0,filterOption:(r,C)=>C.children.toLowerCase().includes(r.toLowerCase())},f.map(r=>{const C=r.name;return a.createElement(Z,{key:C||`machine-${Math.random()}`,value:C},C||"Unknown Machine")})))),a.createElement(N,{xs:24,sm:12,lg:6},a.createElement("div",null,a.createElement("div",{style:{marginBottom:4,fontSize:"12px",color:x.LIGHT_GRAY}},"Type de Période"),a.createElement(me,{value:M,onChange:E,options:[{label:"Jour",value:"day",icon:a.createElement(W,null)},{label:"Semaine",value:"week",icon:a.createElement(W,null)},{label:"Mois",value:"month",icon:a.createElement(W,null)}],style:{width:"100%"}}))),a.createElement(N,{xs:24,sm:12,lg:6},a.createElement("div",null,a.createElement("div",{style:{marginBottom:4,fontSize:"12px",color:x.LIGHT_GRAY}},"Sélection de Date"),v()))),a.createElement(z,{gutter:[16,16],align:"middle",justify:"space-between"},a.createElement(N,null,a.createElement(k,null,(d||s||S)&&a.createElement(k,{wrap:!0},d&&a.createElement(I,{color:"blue",closable:!0,onClose:()=>h("")},"Modèle: ",typeof d=="object"?d.model:d),s&&a.createElement(I,{color:"green",closable:!0,onClose:()=>T("")},"Machine: ",typeof s=="object"?s.Machine_Name:s),S&&D&&a.createElement(I,{color:"orange",closable:!0,onClose:()=>R(null)},a.createElement(q,{style:{marginRight:4}}),D.format(M==="day"?"DD/MM/YYYY":M==="week"?"[Semaine] w YYYY":"MMMM YYYY"))))),a.createElement(N,null,a.createElement(k,null,a.createElement(Q,{title:"Effacer tous les filtres"},a.createElement(B,{icon:a.createElement(ge,null),onClick:c,disabled:!d&&!s&&!S},"Effacer")),a.createElement(Q,{title:"Forcer le rechargement manuel des données"},a.createElement(B,{type:"primary",icon:a.createElement(te,null),onClick:n,loading:y||w},y||w?"Chargement...":"Forcer Refresh"))))),a.createElement(z,null,a.createElement(N,{span:24},a.createElement(k,{wrap:!0},a.createElement(I,{icon:a.createElement(pe,null),color:"processing"},g.length," arrêts trouvés",d&&!s&&` (modèle: ${d})`,s&&` (machine: ${s})`),(d||s||S)&&a.createElement(I,{color:"blue"},[d&&"Modèle",s&&"Machine",S&&"Date"].filter(Boolean).length," filtre(s) actif(s)"),d&&!s&&y&&a.createElement(I,{color:"processing"},a.createElement(q,{spin:!0})," Filtrage par modèle en cours..."),s&&y&&a.createElement(I,{color:"processing"},a.createElement(q,{spin:!0})," Filtrage par machine spécifique en cours..."),S&&y&&a.createElement(I,{color:"orange"},a.createElement(q,{spin:!0})," Filtrage par date en cours..."),y&&a.createElement(I,{color:"blue"},"Chargement en cours..."),w&&a.createElement(I,{color:"gold"},a.createElement(q,{spin:!0})," Traitement complexe..."),a.createElement(I,{color:"success",style:{marginLeft:"auto"}},"✓ Les changements de filtres actualisent automatiquement les données")))),e.error&&a.createElement(z,{style:{marginTop:"16px"}},a.createElement(N,{span:24},a.createElement("div",{style:{padding:"12px",backgroundColor:"#FFFFFF",borderRadius:"8px",border:`1px solid ${x.PRIMARY_BLUE}`,boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"}},a.createElement("div",{style:{display:"flex",alignItems:"center"}},a.createElement(ae,{style:{color:"#ff4d4f",fontSize:"16px",marginRight:"8px"}}),a.createElement("div",null,a.createElement("div",{style:{fontWeight:"bold",color:x.DARK_GRAY}},"Erreur de chargement des données"),a.createElement("div",{style:{fontSize:"12px",marginTop:"4px",color:x.LIGHT_GRAY}},e.error.includes&&e.error.includes("AbortError")?"La requête a été interrompue. Ceci peut se produire lors d'un changement rapide de page.":typeof e.error=="string"?e.error:"Une erreur est survenue lors du chargement des données. Veuillez réessayer."),a.createElement(k,{style:{marginTop:"8px"}},a.createElement(B,{size:"small",type:"primary",onClick:()=>{e.graphQL&&e.graphQL.invalidateCache&&e.graphQL.invalidateCache(),n()}},"Réessayer"),a.createElement(B,{size:"small",onClick:c},"Réinitialiser les filtres"))))))))};export{ve as A,Ye as a,De as u};
