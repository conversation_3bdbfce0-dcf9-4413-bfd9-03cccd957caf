import{r as e,j as se,m as ne,aq as ae,k as ie,b1 as le,t as q,aE as ce,aB as de,b2 as ge,aX as D,c as T,b3 as ue,aw as me,b4 as pe,af as ve}from"./index-gs31pxOi.js";const H=e.createContext({}),fe=t=>{const{antCls:s,componentCls:n,iconCls:r,avatarBg:a,avatarColor:d,containerSize:C,containerSizeLG:g,containerSizeSM:b,textFontSize:y,textFontSizeLG:v,textFontSizeSM:w,borderRadius:k,borderRadiusLG:f,borderRadiusSM:$,lineWidth:S,lineType:x}=t,z=(o,h,O)=>({width:o,height:o,borderRadius:"50%",[`&${n}-square`]:{borderRadius:O},[`&${n}-icon`]:{fontSize:h,[`> ${r}`]:{margin:0}}});return{[n]:Object.assign(Object.assign(Object.assign(Object.assign({},ae(t)),{position:"relative",display:"inline-flex",justifyContent:"center",alignItems:"center",overflow:"hidden",color:d,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:a,border:`${ie(S)} ${x} transparent`,"&-image":{background:"transparent"},[`${s}-image-img`]:{display:"block"}}),z(C,y,k)),{"&-lg":Object.assign({},z(g,v,f)),"&-sm":Object.assign({},z(b,w,$)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},Se=t=>{const{componentCls:s,groupBorderColor:n,groupOverlapping:r,groupSpace:a}=t;return{[`${s}-group`]:{display:"inline-flex",[s]:{borderColor:n},"> *:not(:first-child)":{marginInlineStart:r}},[`${s}-group-popover`]:{[`${s} + ${s}`]:{marginInlineStart:a}}}},he=t=>{const{controlHeight:s,controlHeightLG:n,controlHeightSM:r,fontSize:a,fontSizeLG:d,fontSizeXL:C,fontSizeHeading3:g,marginXS:b,marginXXS:y,colorBorderBg:v}=t;return{containerSize:s,containerSizeLG:n,containerSizeSM:r,textFontSize:Math.round((d+C)/2),textFontSizeLG:g,textFontSizeSM:a,groupSpace:y,groupOverlapping:-b,groupBorderColor:v}},J=se("Avatar",t=>{const{colorTextLightSolid:s,colorTextPlaceholder:n}=t,r=ne(t,{avatarBg:n,avatarColor:s});return[fe(r),Se(r)]},he);var Ce=function(t,s){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&s.indexOf(r)<0&&(n[r]=t[r]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(t);a<r.length;a++)s.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(t,r[a])&&(n[r[a]]=t[r[a]]);return n};const K=e.forwardRef((t,s)=>{const{prefixCls:n,shape:r,size:a,src:d,srcSet:C,icon:g,className:b,rootClassName:y,style:v,alt:w,draggable:k,children:f,crossOrigin:$,gap:S=4,onError:x}=t,z=Ce(t,["prefixCls","shape","size","src","srcSet","icon","className","rootClassName","style","alt","draggable","children","crossOrigin","gap","onError"]),[o,h]=e.useState(1),[O,B]=e.useState(!1),[A,G]=e.useState(!0),I=e.useRef(null),E=e.useRef(null),P=le(s,I),{getPrefixCls:j,avatar:m}=e.useContext(q),u=e.useContext(H),N=()=>{if(!E.current||!I.current)return;const l=E.current.offsetWidth,i=I.current.offsetWidth;l!==0&&i!==0&&S*2<i&&h(i-S*2<l?(i-S*2)/l:1)};e.useEffect(()=>{B(!0)},[]),e.useEffect(()=>{G(!0),h(1)},[d]),e.useEffect(N,[S]);const F=()=>{(x==null?void 0:x())!==!1&&G(!1)},c=ce(l=>{var i,M;return(M=(i=a??(u==null?void 0:u.size))!==null&&i!==void 0?i:l)!==null&&M!==void 0?M:"default"}),_=Object.keys(typeof c=="object"?c||{}:{}).some(l=>["xs","sm","md","lg","xl","xxl"].includes(l)),L=de(_),Q=e.useMemo(()=>{if(typeof c!="object")return{};const l=ge.find(M=>L[M]),i=c[l];return i?{width:i,height:i,fontSize:i&&(g||f)?i/2:18}:{}},[L,c]),p=j("avatar",n),V=D(p),[U,Y,Z]=J(p,V),ee=T({[`${p}-lg`]:c==="large",[`${p}-sm`]:c==="small"}),W=e.isValidElement(d),te=r||(u==null?void 0:u.shape)||"circle",re=T(p,ee,m==null?void 0:m.className,`${p}-${te}`,{[`${p}-image`]:W||d&&A,[`${p}-icon`]:!!g},Z,V,b,y,Y),oe=typeof c=="number"?{width:c,height:c,fontSize:g?c/2:18}:{};let R;if(typeof d=="string"&&A)R=e.createElement("img",{src:d,draggable:k,srcSet:C,onError:F,alt:w,crossOrigin:$});else if(W)R=d;else if(g)R=g;else if(O||o!==1){const l=`scale(${o})`,i={msTransform:l,WebkitTransform:l,transform:l};R=e.createElement(ue,{onResize:N},e.createElement("span",{className:`${p}-string`,ref:E,style:Object.assign({},i)},f))}else R=e.createElement("span",{className:`${p}-string`,style:{opacity:0},ref:E},f);return U(e.createElement("span",Object.assign({},z,{style:Object.assign(Object.assign(Object.assign(Object.assign({},oe),Q),m==null?void 0:m.style),v),className:re,ref:P}),R))}),X=t=>{const{size:s,shape:n}=e.useContext(H),r=e.useMemo(()=>({size:t.size||s,shape:t.shape||n}),[t.size,t.shape,s,n]);return e.createElement(H.Provider,{value:r},t.children)},be=t=>{var s,n,r,a;const{getPrefixCls:d,direction:C}=e.useContext(q),{prefixCls:g,className:b,rootClassName:y,style:v,maxCount:w,maxStyle:k,size:f,shape:$,maxPopoverPlacement:S,maxPopoverTrigger:x,children:z,max:o}=t,h=d("avatar",g),O=`${h}-group`,B=D(h),[A,G,I]=J(h,B),E=T(O,{[`${O}-rtl`]:C==="rtl"},I,B,b,y,G),P=me(z).map((u,N)=>pe(u,{key:`avatar-key-${N}`})),j=(o==null?void 0:o.count)||w,m=P.length;if(j&&j<m){const u=P.slice(0,j),N=P.slice(j,m),F=(o==null?void 0:o.style)||k,c=((s=o==null?void 0:o.popover)===null||s===void 0?void 0:s.trigger)||x||"hover",_=((n=o==null?void 0:o.popover)===null||n===void 0?void 0:n.placement)||S||"top",L=Object.assign(Object.assign({content:N},o==null?void 0:o.popover),{classNames:{root:T(`${O}-popover`,(a=(r=o==null?void 0:o.popover)===null||r===void 0?void 0:r.classNames)===null||a===void 0?void 0:a.root)},placement:_,trigger:c});return u.push(e.createElement(ve,Object.assign({key:"avatar-popover-key",destroyOnHidden:!0},L),e.createElement(K,{style:F},`+${m-j}`))),A(e.createElement(X,{shape:$,size:f},e.createElement("div",{className:E,style:v},u)))}return A(e.createElement(X,{shape:$,size:f},e.createElement("div",{className:E,style:v},P)))},ye=K;ye.Group=be;export{ye as A};
