import React, { memo } from 'react';
import { Spin, Empty } from 'antd';
import { ResponsiveContainer, AreaChart, Area, XAxis, YA<PERSON>s, CartesianGrid, Tooltip } from 'recharts';
import SOMIPEM_COLORS from '../../../styles/brand-colors';
import { useEnhancedRechartsConfig } from '../../../utils/enhancedRechartsConfig';
import { useSettings } from '../../../hooks/useSettings';

const CHART_COLORS = {
  primary: SOMIPEM_COLORS.PRIMARY_BLUE,
  secondary: SOMIPEM_COLORS.SECONDARY_BLUE,
  success: SOMIPEM_COLORS.PRIMARY_BLUE, // Updated to use SOMIPEM Primary Blue instead of green
  warning: "#faad14",
  danger: "#f5222d",
  purple: SOMIPEM_COLORS.DARK_GRAY,
  pink: SOMIPEM_COLORS.LIGHT_GRAY,
  orange: SOM<PERSON>EM_COLORS.SECONDARY_BLUE,
  cyan: SOMIPEM_COLORS.SECONDARY_BLUE,
  lime: SOMIPEM_COLORS.PRIMARY_BLUE,
};

const ArretHeatmapChart = memo(({ data = [], loading }) => {
  // Get settings for enhanced chart configuration
  const { settings, charts, theme } = useSettings();

  // Enhanced chart configuration
  const enhancedChartConfig = useEnhancedRechartsConfig({
    charts,
    theme
  });

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <Spin size="large" />
      </div>
    );
  }

  // Ensure data is an array - handle both direct arrays and response objects
  const stopsData = Array.isArray(data) ? data : (data?.data || []);

  // Process data using the same logic as ArretTimePatternChart
  const processTimePatternData = (stopsData) => {
    const MAX_REASONABLE_DURATION = 480; // 8 hours maximum
    const MIN_REASONABLE_DURATION = 1;   // 1 minute minimum
    
    // Group by hour of day
    const hourlyStats = {};
    let outlierCount = 0;
    let totalStopsProcessed = 0;
    
    // Initialize all hours
    for (let hour = 0; hour < 24; hour++) {
      hourlyStats[hour] = {
        count: 0,
        totalDuration: 0,
        avgDuration: 0,
        durations: [], // Track individual durations for debugging
        outliers: []   // Track outliers separately
      };
    }
    
    // Helper function to parse date in DD/MM/YYYY HH:mm format
    const parseCustomDate = (dateStr) => {
      if (!dateStr) return null;
      try {
        // Handle format like "30/09/2024 23:16" or " 3/12/2024 09:55:38" or "30/04/2025  14:19:41"
        const trimmed = dateStr.trim();
        
        // Split by spaces, but handle multiple spaces
        const parts = trimmed.split(/\s+/).filter(part => part.length > 0);
        
        if (parts.length >= 2) {
          const datePart = parts[0];
          const timePart = parts[1];
          
          const dateComponents = datePart.split('/');
          const timeComponents = timePart.split(':');
          
          // Validate we have all required components
          if (dateComponents.length === 3 && timeComponents.length >= 2) {
            const [day, month, year] = dateComponents;
            const [hour, minute, second] = timeComponents;
            
            // Ensure all components exist before using padStart
            if (day && month && year && hour && minute) {
              // Create date in ISO format (YYYY-MM-DD HH:mm:ss)
              const isoDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}T${hour.padStart(2, '0')}:${minute.padStart(2, '0')}:${(second || '00').padStart(2, '0')}`;
              const parsedDate = new Date(isoDate);
              
              if (!isNaN(parsedDate.getTime())) {
                return parsedDate;
              }
            }
          }
        }
      } catch (error) {
        console.warn('Error parsing date:', dateStr, error);
      }
      return null;
    };
    
    stopsData.forEach(stop => {
      if (stop.Debut_Stop) {
        try {
          const startTime = parseCustomDate(stop.Debut_Stop);
          if (startTime && !isNaN(startTime.getTime())) {
            const hour = startTime.getHours();
            totalStopsProcessed++;
            
            if (hourlyStats[hour]) {
              // Calculate duration - prefer pre-calculated duration_minutes from GraphQL
              let duration = 0;
              
              if (stop.duration_minutes !== undefined && stop.duration_minutes !== null) {
                // Use pre-calculated duration from GraphQL resolver
                duration = parseFloat(stop.duration_minutes);
              } else if (stop.Fin_Stop_Time) {
                // Fallback to calculating from start/end times
                const endTime = parseCustomDate(stop.Fin_Stop_Time);
                if (endTime && !isNaN(endTime.getTime())) {
                  duration = (endTime - startTime) / (1000 * 60); // Duration in minutes
                }
              }
              
              if (duration > 0) {
                // Check if duration is within reasonable bounds
                if (duration >= MIN_REASONABLE_DURATION && duration <= MAX_REASONABLE_DURATION) {
                  // Normal operational stop
                  hourlyStats[hour].count += 1;
                  hourlyStats[hour].totalDuration += duration;
                  hourlyStats[hour].durations.push(duration);
                } else {
                  // Outlier - track but don't include in average
                  hourlyStats[hour].outliers.push(duration);
                  outlierCount++;
                }
              }
            }
          }
        } catch (error) {
          console.warn('Error parsing time:', error);
        }
      }
    });

    // Calculate averages
    Object.keys(hourlyStats).forEach(hour => {
      const stats = hourlyStats[hour];
      stats.avgDuration = stats.count > 0 ? stats.totalDuration / stats.count : 0;
    });    // Transform to format expected by the chart
    const chartData = [];
    for (let hour = 0; hour < 24; hour++) {
      chartData.push({
        hour: hour,
        avgDuration: Math.round(hourlyStats[hour].avgDuration) // Match ArretTimePatternChart rounding
      });
    }

    return chartData;
  };

  const chartData = processTimePatternData(stopsData);

  // Check if data is empty
  if (!chartData || chartData.length === 0 || chartData.every(item => item.avgDuration === 0)) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', minHeight: '300px' }}>
        <Empty description="Aucune donnée de durée par heure disponible" image={Empty.PRESENTED_IMAGE_SIMPLE} />
      </div>
    );
  }
  return (
    <ResponsiveContainer {...enhancedChartConfig.getResponsiveContainerProps()} height={enhancedChartConfig.getChartHeight() || 300}>
      <AreaChart data={chartData} margin={enhancedChartConfig.getChartMargins()}>
        <CartesianGrid {...enhancedChartConfig.getGridConfig()} />
        <XAxis
          dataKey="hour"
          {...enhancedChartConfig.getAxisConfig()}
          label={{
            value: "Heure de la journée",
            position: "bottom",
            offset: 0,
            style: { textAnchor: 'middle', fill: SOMIPEM_COLORS.LIGHT_GRAY }
          }}
        />
        <YAxis
          {...enhancedChartConfig.getAxisConfig()}
          label={{
            value: "Durée moyenne (min)",
            angle: -90,
            position: "insideLeft",
            style: { textAnchor: 'middle', fill: SOMIPEM_COLORS.LIGHT_GRAY }
          }}
        />        <Tooltip
          formatter={(value) => {
            const numValue = typeof value === 'number' ? value : parseFloat(value);
            const safeValue = isNaN(numValue) ? 0 : numValue;
            return [`${safeValue.toFixed(1)} min`, "Durée moyenne"];
          }}
          contentStyle={{
            backgroundColor: "#fff",
            border: `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}`, // SOMIPEM Primary Blue border
            borderRadius: 4,
            boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
            color: SOMIPEM_COLORS.DARK_GRAY // SOMIPEM Dark Gray text
          }}
        />
        <Area
          type="monotone"
          dataKey="avgDuration"
          stroke={CHART_COLORS.success}
          fill={`${CHART_COLORS.success}33`}
        />
      </AreaChart>
    </ResponsiveContainer>
  );
});

export default ArretHeatmapChart;
