import React, { useState, useCallback, useEffect } from 'react';
import { Modal, Spin, Button, Space, Tooltip } from 'antd';
import { FullscreenOutlined, DownloadOutlined, CloseOutlined } from '@ant-design/icons';
import PropTypes from 'prop-types';

/**
 * Unified Chart Modal Component
 * Provides consistent fullscreen chart display across the application
 * Integrates with unified chart configuration system
 */
const UnifiedChartModal = ({
  visible,
  onClose,
  title,
  data,
  chartType,
  chartConfig,
  children,
  exportEnabled = false,
}) => {
  const [isLoading, setIsLoading] = useState(false);

  // Handle modal close
  const handleClose = useCallback(() => {
    onClose();
  }, [onClose]);

  // Handle keyboard events
  useEffect(() => {
    const handleKeyPress = (event) => {
      if (event.key === 'Escape' && visible) {
        handleClose();
      }
    };

    if (visible) {
      document.addEventListener('keydown', handleKeyPress);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [visible, handleClose]);

  // Handle export functionality
  const handleExport = useCallback(() => {
    // Export functionality would be implemented here
    console.log('Exporting chart:', title);
    // Could integrate with chart-specific export methods
  }, [title]);

  // Calculate modal chart height
  const getModalChartHeight = () => {
    const viewportHeight = window.innerHeight;
    return Math.floor(viewportHeight * 0.7); // 70% of viewport height
  };

  // Render the chart with modal-specific props
  const renderModalChart = () => {
    if (isLoading) {
      return (
        <div
          style={{
            height: getModalChartHeight(),
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <Spin size="large" tip="Chargement du graphique..." />
        </div>
      );
    }

    if (!data || data.length === 0) {
      return (
        <div
          style={{
            height: getModalChartHeight(),
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'column',
            color: '#999',
            fontSize: '16px'
          }}
        >
          <div>Aucune donnée disponible</div>
        </div>
      );
    }

    const chartHeight = getModalChartHeight();

    return (
      <div
        style={{
          height: chartHeight,
          width: '100%',
          position: 'relative'
        }}
      >
        <div
          id="modal-chart"
          style={{
            height: '100%',
            width: '100%',
          }}
        >
          {React.cloneElement(children, {
            // Pass original props first
            ...children.props,
            // Override with modal-specific props
            data: data, // Always use original data in modal
            height: chartHeight,
            enhanced: true, // Always enhanced in modal
            expanded: true, // Always expanded in modal
            isModal: true, // Flag to indicate modal mode
            chartConfig: {
              ...chartConfig,
              // Modal-specific chart configuration
              showAllLabels: true,
              labelInterval: 0,
              maxBarSize: 60,
              strokeWidth: 3,
              dotSize: 6,
            },
          })}
        </div>
      </div>
    );
  };

  // Modal header with unified styling
  const modalHeader = (
    <div style={{
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      width: '100%',
      background: `linear-gradient(135deg, ${chartConfig?.getPrimaryColor() || '#1890ff'} 0%, ${chartConfig?.colors?.[1] || '#096dd9'} 100%)`,
      padding: '12px 20px',
      margin: '-16px -24px 0 -24px',
      borderRadius: '8px 8px 0 0'
    }}>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span style={{
          color: 'white',
          fontSize: '18px',
          fontWeight: 'bold',
          textShadow: '0 1px 2px rgba(0,0,0,0.3)',
          marginRight: '8px'
        }}>
          {title}
        </span>
        <FullscreenOutlined style={{
          color: "white",
          fontSize: '16px',
          textShadow: '0 1px 2px rgba(0,0,0,0.3)'
        }} />
      </div>
      
      <Space>
        {exportEnabled && (
          <Tooltip title="Exporter le graphique">
            <Button
              icon={<DownloadOutlined />}
              onClick={handleExport}
              size="small"
              style={{
                background: 'rgba(255,255,255,0.2)',
                border: '1px solid rgba(255,255,255,0.3)',
                color: 'white'
              }}
            >
              Exporter
            </Button>
          </Tooltip>
        )}
        <Tooltip title="Fermer (Échap)">
          <Button
            icon={<CloseOutlined />}
            onClick={handleClose}
            size="small"
            style={{
              background: 'rgba(255,255,255,0.2)',
              border: '1px solid rgba(255,255,255,0.3)',
              color: 'white'
            }}
          />
        </Tooltip>
      </Space>
    </div>
  );

  return (
    <Modal
      title={modalHeader}
      open={visible}
      onCancel={handleClose}
      width="95vw"
      style={{
        top: 20,
        maxWidth: "95vw",
        padding: 0
      }}
      bodyStyle={{
        height: "80vh",
        padding: "24px",
        margin: 0,
        overflow: "auto",
        background: "#fafafa"
      }}
      footer={null}
      destroyOnClose
      maskClosable={true}
      keyboard={true}
      centered={false}
    >
      <div style={{
        height: "75vh",
        width: "100%",
        background: "white",
        borderRadius: "8px",
        padding: "20px",
        boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
        overflow: "hidden"
      }}>
        {renderModalChart()}
      </div>
    </Modal>
  );
};

UnifiedChartModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
  data: PropTypes.array.isRequired,
  chartType: PropTypes.string,
  chartConfig: PropTypes.object,
  children: PropTypes.element.isRequired,
  exportEnabled: PropTypes.bool,
};

export default UnifiedChartModal;
