import React, { memo, useMemo } from 'react';
import {
  Re<PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Line,
  Bar<PERSON>hart,
  <PERSON>,
  <PERSON>A<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON>
} from 'recharts';
import { Spin, Empty, Typography } from 'antd';
import dayjs from 'dayjs';
import { useDynamicChartConfig } from '../../../hooks/useUnifiedChartConfig';
import SOMIPEM_COLORS from '../../../styles/brand-colors';

const { Text } = Typography;

const CHART_COLORS = {
  primary: SOMIPEM_COLORS.PRIMARY_BLUE,
  secondary: SOMIPEM_COLORS.SECONDARY_BLUE,
  success: SOMIPEM_COLORS.PRIMARY_BLUE, // Updated to SOMIPEM Primary Blue
  warning: "#faad14",
  danger: "#f5222d",
  purple: SOMIPEM_COLORS.DARK_GRAY,
  pink: SOMIPEM_COLORS.LIGHT_GRAY,
  orange: SOM<PERSON>EM_COLORS.SECONDARY_BLUE,
  cyan: SOMIPEM_COLORS.SECONDARY_BLUE,
  lime: SOMIPEM_COLORS.PRIMARY_BLUE,
};

/**
 * Enhanced Dynamic Arret Trend Chart - Can render as Line or Bar based on settings
 * Replaces ArretLineChart with dynamic chart type support
 */
const ArretTrendChart = memo(({
  data = [],
  loading = false,
  title = "Evolution des Arrêts",
  chartType: propChartType, // Override setting if provided
  allowedTypes = ['line', 'bar'], // Restrict which types are allowed
  height = 400
}) => {
  // Get unified chart configuration with dynamic type support
  const chartConfig = useDynamicChartConfig({
    fallbackType: 'line',
    allowedTypes,
    propChartType
  });

  // Extract chart type from unified config
  const chartType = chartConfig.chartType;

  // Process and validate data
  const processedData = useMemo(() => {
    // Ensure data is an array - handle both direct arrays and response objects
    const safeData = Array.isArray(data) ? data : (data?.data || []);

    if (!Array.isArray(safeData) || safeData.length === 0) {
      return [];
    }

    return safeData.map(item => {
      // Handle different date formats and data structures
      const dateValue = item.Stop_Date || item.date || item.Date || item.day || item.Day || item.period;
      const stopsValue = item.stops || item.count || item.frequency || item.total || 0;

      return {
        ...item,
        date: dateValue,
        stops: Number(stopsValue) || 0,
        // Preserve original data structure
        originalData: item
      };
    }).filter(item => item.date && item.stops !== undefined);
  }, [data]);

  // Custom tooltip formatter
  const CustomTooltip = ({ active, payload, label }) => {
    const theme = chartConfig.theme || {};
    if (active && payload && payload.length) {
      const data = payload[0];
      const value = data.value;
      const formattedDate = dayjs(label).format("DD/MM/YYYY");

      return (
        <div style={{
          backgroundColor: theme.darkMode ? '#1f1f1f' : '#ffffff',
          border: `1px solid ${CHART_COLORS.primary}`,
          borderRadius: '8px',
          padding: '12px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
          fontSize: '12px'
        }}>
          <p style={{
            margin: 0,
            fontWeight: 'bold',
            color: theme.darkMode ? '#ffffff' : '#000000'
          }}>
            {`Date: ${formattedDate}`}
          </p>
          <p style={{
            margin: 0,
            color: CHART_COLORS.primary
          }}>
            {`Nombre d'arrêts: ${value}`}
          </p>
        </div>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: height,
        flexDirection: 'column',
        gap: '16px'
      }}>
        <Spin size="large" />
        <Text type="secondary">Chargement de l'évolution des arrêts...</Text>
      </div>
    );
  }

  if (!processedData || processedData.length === 0) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: height,
        width: '100%'
      }}>
        <Empty
          description="Aucune donnée d'évolution disponible"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </div>
    );
  }

  // Prepare chart props
  const chartProps = {
    data: processedData,
    margin: chartConfig.margins
  };

  // Render appropriate chart type
  const renderChart = () => {
    switch (chartType) {
      case 'bar':
        return (
          <BarChart {...chartProps}>
            <CartesianGrid {...chartConfig.gridConfig} />
            <XAxis
              dataKey="date"
              {...chartConfig.AxisConfig}
              tick={{ fontSize: 11 }}
              tickFormatter={(date) => dayjs(date).format("DD/MM")}
              angle={-45}
              textAnchor="end"
              height={80}
            />
            <YAxis
              {...chartConfig.AxisConfig}
              tick={{ fontSize: 11 }}
              label={{
                value: "Nombre d'arrêts",
                angle: -90,
                position: 'insideLeft',
                style: { textAnchor: 'middle' }
              }}
            />
            <Tooltip content={<CustomTooltip />} />
            {chartConfig.charts?.showLegend && <Legend {...chartConfig.getLegendConfig()} />}
            <Bar
              dataKey="stops"
              name="Nombre d'arrêts"
              {...chartConfig.getBarElementConfig(CHART_COLORS.primary)}
            />
          </BarChart>
        );

      case 'line':
      default:
        return (
          <LineChart {...chartProps}>
            <CartesianGrid {...chartConfig.gridConfig} />
            <XAxis
              dataKey="date"
              {...chartConfig.AxisConfig}
              tick={{ fontSize: 11 }}
              tickFormatter={(date) => dayjs(date).format("DD/MM")}
              angle={-45}
              textAnchor="end"
              height={80}
            />
            <YAxis
              {...chartConfig.AxisConfig}
              tick={{ fontSize: 11 }}
              label={{
                value: "Nombre d'arrêts",
                angle: -90,
                position: 'insideLeft',
                style: { textAnchor: 'middle' }
              }}
            />
            <Tooltip content={<CustomTooltip />} />
            {chartConfig.charts?.showLegend && <Legend {...chartConfig.getLegendConfig()} />}
            <Line
              type="monotone"
              dataKey="stops"
              name="Nombre d'arrêts"
              {...chartConfig.getLineElementConfig(CHART_COLORS.primary)}
            />
          </LineChart>
        );
    }
  };

  return (
    <div style={{ height: '100%', padding: '16px', background: 'transparent' }}>
      {/* Title */}
      <div style={{
        textAlign: 'center',
        marginBottom: '20px',
        fontSize: '18px',
        fontWeight: '600',
        color: SOMIPEM_COLORS.PRIMARY_BLUE,
        letterSpacing: '0.3px'
      }}>
        {title}
      </div>

      {/* Chart Container */}
      <div style={{ height: 'calc(100% - 60px)', width: '100%' }}>
        <ResponsiveContainer {...chartConfig.responsiveContainerProps}>
          {renderChart()}
        </ResponsiveContainer>
      </div>
    </div>
  );
});

ArretTrendChart.displayName = 'ArretTrendChart';

export default ArretTrendChart;
